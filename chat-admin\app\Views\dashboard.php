<?php
require __DIR__ . '/partials/header.php';
?>

<div class="content-header">
    <h1 class="content-title">Главная панель</h1>
</div>

<div class="stats-grid">
    <div class="stat-card">
        <i class="fas fa-comments stat-icon"></i>
        <div class="stat-title">Всего сообщений</div>
        <div class="stat-value"><?= htmlspecialchars($totalMessages) ?></div>
    </div>

    <div class="stat-card">
        <i class="fas fa-users stat-icon"></i>
        <div class="stat-title">Пользователей</div>
        <div class="stat-value"><?= htmlspecialchars($totalUsers) ?></div>
    </div>

    <div class="stat-card">
        <i class="fas fa-comment-dots stat-icon"></i>
        <div class="stat-title">Активных чатов</div>
        <div class="stat-value"><?= htmlspecialchars($totalChats) ?></div>
    </div>

    <div class="stat-card">
        <i class="fas fa-chart-line stat-icon"></i>
        <div class="stat-title">Сообщений на чат</div>
        <div class="stat-value"><?= htmlspecialchars($avgMessagesPerChat) ?></div>
    </div>
</div>

<div class="dashboard-charts">
    <div class="chart-container">
        <h2>Динамика сообщений по месяцам</h2>
        <canvas id="monthlyTrendChart"></canvas>
    </div>
    
    <div class="top-users-container">
        <h2>Топ активных пользователей</h2>
        <div class="top-users-list">
            <?php foreach ($topUsers as $index => $user): ?>
            <div class="top-user-item">
                <span class="rank">#<?= $index + 1 ?></span>
                <span class="username"><?= htmlspecialchars($user['username']) ?></span>
                <span class="message-count"><?= htmlspecialchars($user['message_count']) ?> сообщ.</span>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const monthlyData = <?= json_encode($monthlyTrend) ?>;
    
    const ctx = document.getElementById('monthlyTrendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: monthlyData.map(item => item.month),
            datasets: [{
                label: 'Количество сообщений',
                data: monthlyData.map(item => item.message_count),
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1,
                fill: false
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Активность за последние 6 месяцев'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.stat-title {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.chart-container, .top-users-container {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container h2, .top-users-container h2 {
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.2rem;
}

.top-users-list {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.top-user-item {
    display: flex;
    align-items: center;
    padding: 0.8rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.top-user-item .rank {
    font-weight: bold;
    margin-right: 1rem;
    color: var(--primary);
}

.top-user-item .username {
    flex-grow: 1;
}

.top-user-item .message-count {
    color: #666;
    font-size: 0.9rem;
}
</style>

<?php require __DIR__ . '/partials/footer.php'; ?>
