<?php

namespace App\Services;

class GoogleTtsService
{
    public function getSound(string $text, string $apikey, string $lang, string $voiceName, string $folder): ?string
    {
        if (!file_exists($folder)) {
            mkdir($folder, 0777, true);
        }

        $file_name = $folder . strtolower(md5($text));

        if (file_exists("$file_name.wav")) {
            return "$file_name.wav";
        }

        $text = trim($text);
        if ($text === '') {
            return null;
        }

        $params = [
            "audioConfig" => [
                "audioEncoding" => "ALAW",
                "sampleRateHertz" => 8000
            ],
            "input" => [
                "text" => $text
            ],
            "voice" => [
                "languageCode" => $lang,
                "name" => $voiceName
            ]
        ];

        $data_string = json_encode($params);

        $url = "https://texttospeech.googleapis.com/v1/text:synthesize?fields=audioContent&key=$apikey";

        $handle = curl_init($url);
        curl_setopt($handle, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($handle, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($handle, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data_string)
        ]);

        $response = curl_exec($handle);
        $responseDecoded = json_decode($response, true);
        curl_close($handle);

        if (isset($responseDecoded['audioContent'])) {
            file_put_contents("$file_name.wav", base64_decode($responseDecoded['audioContent']));
            return "$file_name.wav";
        }

        // Логируем ответ для отладки
        error_log("Google TTS API response: " . $response);

        return null;
    }
}
