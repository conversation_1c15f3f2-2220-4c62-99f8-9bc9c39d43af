<?php

declare(strict_types=1);

namespace App\Services;

/**
 * Простой email сервис для отправки через внешние API
 */
class SimpleEmailService
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            "api_key" => "",
            "from_email" => "<EMAIL>",
            "from_name" => "AI Chat System",
            "debug" => true
        ], $config);
    }

    /**
     * Отправляет email через внешний API
     */
    public function sendEmail(string $to, string $subject, string $body, ?string $fromEmail = null): bool
    {
        try {
            // Метод 1: Попробуем отправить через простой HTTP запрос
            $result = $this->sendViaHttpService($to, $subject, $body, $fromEmail);
            
            if ($result) {
                error_log("SimpleEmailService: Email sent successfully to $to");
                return true;
            }
            
            // Метод 2: Сохраняем в файл для ручной отправки
            return $this->saveForManualSending($to, $subject, $body, $fromEmail);
            
        } catch (\Exception $e) {
            error_log("SimpleEmailService: Error: " . $e->getMessage());
            return $this->saveForManualSending($to, $subject, $body, $fromEmail);
        }
    }

    /**
     * Отправка через HTTP сервис (заглушка для реального API)
     */
    private function sendViaHttpService(string $to, string $subject, string $body, ?string $fromEmail): bool
    {
        // Здесь можно подключить реальный API сервис типа:
        // - SendGrid API
        // - Mailgun API  
        // - Amazon SES API
        // - Собственный SMTP сервер через HTTP
        
        // Для демонстрации возвращаем false, чтобы использовать сохранение в файл
        return false;
    }

    /**
     * Сохраняет email для ручной отправки
     */
    private function saveForManualSending(string $to, string $subject, string $body, ?string $fromEmail): bool
    {
        $from = $fromEmail ?: $this->config["from_email"];
        
        $timestamp = date("Y-m-d_H-i-s");
        $filename = "ready_to_send_{$timestamp}_" . substr(md5($to . $subject), 0, 8) . ".eml";
        
        // Создаем папку для готовых к отправке писем
        $readyDir = dirname(__DIR__, 2) . "/ready_to_send_emails";
        if (!is_dir($readyDir)) {
            mkdir($readyDir, 0755, true);
        }
        
        $filepath = $readyDir . "/" . $filename;
        
        // Создаем правильный EML файл для импорта в почтовый клиент
        $emlContent = "From: {$this->config["from_name"]} <$from>\r\n";
        $emlContent .= "To: $to\r\n";
        $emlContent .= "Subject: $subject\r\n";
        $emlContent .= "Date: " . date("r") . "\r\n";
        $emlContent .= "Content-Type: text/plain; charset=UTF-8\r\n";
        $emlContent .= "Content-Transfer-Encoding: 8bit\r\n";
        $emlContent .= "\r\n";
        $emlContent .= $body;
        
        if (file_put_contents($filepath, $emlContent)) {
            error_log("SimpleEmailService: Email saved as EML file: $filename");
            
            // Также создаем текстовую версию для удобства
            $txtContent = "=== ГОТОВО К ОТПРАВКЕ ===\n";
            $txtContent .= "Дата создания: " . date("Y-m-d H:i:s") . "\n";
            $txtContent .= "От: $from\n";
            $txtContent .= "Кому: $to\n";
            $txtContent .= "Тема: $subject\n";
            $txtContent .= "\n=== СОДЕРЖИМОЕ ===\n";
            $txtContent .= $body;
            $txtContent .= "\n\n=== ИНСТРУКЦИЯ ===\n";
            $txtContent .= "1. Откройте файл $filename в почтовом клиенте\n";
            $txtContent .= "2. Или скопируйте содержимое и отправьте вручную\n";
            $txtContent .= "3. Или настройте внешний SMTP сервис\n";
            
            $txtFilepath = str_replace(".eml", ".txt", $filepath);
            file_put_contents($txtFilepath, $txtContent);
            
            return true;
        }
        
        return false;
    }

    /**
     * Тестирует отправку email
     */
    public function testEmail(string $to): bool
    {
        $subject = "Тест SimpleEmailService - " . date("Y-m-d H:i:s");
        $body = "Это тестовое письмо от SimpleEmailService.\n\n";
        $body .= "Время: " . date("Y-m-d H:i:s") . "\n";
        $body .= "Сервис: SimpleEmailService\n";
        $body .= "Статус: Готово к отправке\n";
        
        return $this->sendEmail($to, $subject, $body);
    }
}
?>