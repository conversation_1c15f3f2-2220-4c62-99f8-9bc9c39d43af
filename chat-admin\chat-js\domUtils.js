// --- START OF FILE domUtils.js ---
// (с исправлениями для класса уведомлений)

// chat-js/domUtils.js

/**
 * Находит элемент по селектору. Выбрасывает ошибку, если не найден.
 * @param {string} selector - CSS селектор.
 * @param {Element} [parent=document] - Родительский элемент для поиска.
 * @returns {Element} - Найденный элемент.
 * @throws {Error} Если элемент не найден.
 */
export function findOrFail(selector, parent = document) {
    const element = parent.querySelector(selector);
    if (!element) {
        throw new Error(`DOM element not found for selector: ${selector}`);
    }
    return element;
}

/**
 * Находит все элементы по селектору.
 * @param {string} selector - CSS селектор.
 * @param {Element} [parent=document] - Родительский элемент для поиска.
 * @returns {NodeListOf<Element>} - Список найденных элементов.
 */
export function findAll(selector, parent = document) {
    return parent.querySelectorAll(selector);
}

/**
 * Создает HTML элемент с заданными атрибутами и содержимым.
 * @param {string} tag - Тег элемента (например, 'div', 'button').
 * @param {object} [attributes={}] - Объект с атрибутами (например, { className: 'my-class', title: 'Tooltip' }).
 * @param {string|Node|Array<Node>} [content=null] - Содержимое элемента (текст, другой узел или массив узлов).
 * @returns {Element} - Созданный элемент.
 */
export function createElement(tag, attributes = {}, content = null) {
    const element = document.createElement(tag);
    for (const key in attributes) {
        if (attributes.hasOwnProperty(key)) {
            if (key === 'className') {
                element.className = attributes[key];
            } else if (key === 'dataset') {
                 for (const dataKey in attributes.dataset) {
                     if (attributes.dataset.hasOwnProperty(dataKey)) {
                         element.dataset[dataKey] = attributes.dataset[dataKey];
                     }
                 }
            } else {
                element.setAttribute(key, attributes[key]);
            }
        }
    }
    if (content !== null) {
        if (typeof content === 'string') {
            element.innerHTML = content; // Используем innerHTML для вставки HTML строк
        } else if (content instanceof Node) {
            element.appendChild(content);
        } else if (Array.isArray(content)) {
            content.forEach(child => element.appendChild(child));
        }
    }
    return element;
}

/**
 * Показывает уведомление над элементом или возле курсора.
 * @param {string} text - Текст уведомления.
 * @param {Element|{x: number, y: number}} target - Элемент, над которым показать уведомление, ИЛИ объект с координатами {x, y} курсора.
 * @param {number} [duration=1500] - Длительность показа в мс.
 */
export function showNotificationAbove(text, target, duration = 1500) {
    // Используем НОВЫЙ класс для поиска и удаления
    const notificationClass = 'czn-chat-copy-notification'; // <<< ИСПРАВЛЕНО
    const existingNotification = document.querySelector(`.${notificationClass}`); // <<< ИСПРАВЛЕНО
    if (existingNotification) {
        existingNotification.remove();
    }

    // Используем НОВЫЙ класс при создании элемента
    const notification = createElement('div', { className: notificationClass }, text); // <<< ИСПРАВЛЕНО
    document.body.appendChild(notification);

    notification.style.position = 'fixed';
    notification.style.zIndex = '10001';

    // Логика позиционирования (без изменений)
    if (target instanceof Element) {
        const targetRect = target.getBoundingClientRect();
        const targetCenterX = targetRect.left + targetRect.width / 2;
        // Расчет top с учетом высоты уведомления (после добавления в DOM)
        const notificationHeight = notification.offsetHeight;
        notification.style.top = `${targetRect.top - notificationHeight - 5}px`;
        notification.style.left = `${targetCenterX}px`;
        notification.style.transform = 'translateX(-50%)';
    } else if (typeof target === 'object' && target !== null && typeof target.x === 'number' && typeof target.y === 'number') {
        const notificationHeight = notification.offsetHeight;
        notification.style.top = `${target.y - notificationHeight - 10}px`;
        notification.style.left = `${target.x + 10}px`;
        notification.style.transform = 'none'; // Убираем transform для позиционирования по курсору
    } else {
        console.warn("showNotificationAbove: Invalid target provided. Positioning near top-center.");
        notification.style.top = '10px';
        notification.style.left = '50%';
        notification.style.transform = 'translateX(-50%)';
    }

    // Корректировка, чтобы не выходило за экран (без изменений)
    const notificationRect = notification.getBoundingClientRect();
    if (notificationRect.left < 5) notification.style.left = '5px';
    if (notificationRect.right > window.innerWidth - 5) notification.style.left = `${window.innerWidth - notificationRect.width - 5}px`;
    if (notificationRect.top < 5) notification.style.top = '5px';
    // Сбрасываем transform, если он мешает позиционированию по краям
    if (notificationRect.left <= 5 || notificationRect.right >= window.innerWidth - 5) {
        if(notification.style.transform === 'translateX(-50%)') {
           // Если центрировали, но вышло за край, лучше просто прижать к краю
           notification.style.transform = 'none';
           if (notificationRect.left <= 5) notification.style.left = '5px';
           else notification.style.left = `${window.innerWidth - notificationRect.width - 5}px`;
        }
    }


    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

/**
 * Усекает строку до максимальной длины.
 * @param {string} str - Исходная строка.
 * @param {number} maxLength - Максимальная длина.
 * @returns {string} - Усеченная строка.
 */
export function truncateText(str, maxLength) {
    if (!str) return '';
    if (str.length > maxLength) {
        return str.substring(0, maxLength) + '...';
    }
    return str;
}

// --- END OF FILE domUtils.js ---