<?php

declare(strict_types=1);

namespace App\Services;

/**
 * Сервис для детекции контактных данных в сообщениях
 */
class ContactDetectionService
{
    /**
     * Паттерны для поиска телефонных номеров
     */
    private const PHONE_PATTERNS = [
        // Российские номера с +7
        '/\+7\s*[\(\-]?\s*\d{3}\s*[\)\-]?\s*\d{3}\s*[\-]?\s*\d{2}\s*[\-]?\s*\d{2}/',
        // Российские номера с 8
        '/8\s*[\(\-]?\s*\d{3}\s*[\)\-]?\s*\d{3}\s*[\-]?\s*\d{2}\s*[\-]?\s*\d{2}/',
        // Номера в формате +7XXXXXXXXXX
        '/\+?7\s*\d{3}\s*\d{3}\s*\d{2}\s*\d{2}/',
        // 11-значные номера
        '/\d{11}/',
        // Номера в скобках
        '/\+7\(\d{3}\)\d{3}-\d{2}-\d{2}/',
        // Номера с пробелами
        '/\d{1}\s*\d{3}\s*\d{3}\s*\d{2}\s*\d{2}/',
        // Номера в скобках с пробелами
        '/\(\d{3}\)\s*\d{3}[\s-]?\d{2}[\s-]?\d{2}/',
        // Простые последовательности цифр (как указано в ТЗ)
        '/\d{10,11}/',
        // Международные форматы
        '/\+\d{1,3}\s*\d{3,4}\s*\d{3}\s*\d{2}\s*\d{2}/'
    ];

    /**
     * Паттерны для поиска email адресов
     */
    private const EMAIL_PATTERNS = [
        '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/'
    ];

    /**
     * Ключевые слова, указывающие на контактную информацию
     */
    private const CONTACT_KEYWORDS = [
        'телефон', 'номер', 'звонить', 'позвонить', 'связаться',
        'контакт', 'мобильный', 'сотовый', 'phone', 'mobile',
        'email', 'почта', 'емейл', 'мыло', 'адрес'
    ];

    /**
     * Детектирует контактные данные в тексте
     *
     * @param string $text Текст для анализа
     * @return array Массив с найденными контактными данными
     */
    public function detectContacts(string $text): array
    {
        $contacts = [
            'phones' => $this->extractPhones($text),
            'emails' => $this->extractEmails($text),
            'has_contact_intent' => $this->hasContactIntent($text),
            'confidence' => 0.0
        ];

        // Вычисляем уровень уверенности
        $contacts['confidence'] = $this->calculateConfidence($text, $contacts);

        return $contacts;
    }

    /**
     * Извлекает телефонные номера из текста
     *
     * @param string $text
     * @return array
     */
    public function extractPhones(string $text): array
    {
        $phones = [];
        
        foreach (self::PHONE_PATTERNS as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                foreach ($matches[0] as $match) {
                    $cleanPhone = $this->cleanPhone($match);
                    if ($this->isValidPhone($cleanPhone)) {
                        $phones[] = $this->formatPhone($cleanPhone);
                    }
                }
            }
        }

        return array_unique($phones);
    }

    /**
     * Извлекает email адреса из текста
     *
     * @param string $text
     * @return array
     */
    public function extractEmails(string $text): array
    {
        $emails = [];
        
        foreach (self::EMAIL_PATTERNS as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                foreach ($matches[0] as $match) {
                    if (filter_var($match, FILTER_VALIDATE_EMAIL)) {
                        $emails[] = strtolower($match);
                    }
                }
            }
        }

        return array_unique($emails);
    }

    /**
     * Проверяет, содержит ли текст намерение оставить контакт
     *
     * @param string $text
     * @return bool
     */
    public function hasContactIntent(string $text): bool
    {
        $lowerText = function_exists('mb_strtolower') ? mb_strtolower($text, 'UTF-8') : strtolower($text);
        
        foreach (self::CONTACT_KEYWORDS as $keyword) {
            if (function_exists('mb_strpos') ? mb_strpos($lowerText, $keyword) !== false : strpos($lowerText, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Очищает телефонный номер от лишних символов
     *
     * @param string $phone
     * @return string
     */
    private function cleanPhone(string $phone): string
    {
        // Удаляем все символы кроме цифр и +
        $cleanPhone = preg_replace('/[^\d\+]/', '', $phone);
        
        // Если номер начинается с 8, заменяем на +7
        if (strpos($cleanPhone, '8') === 0 && strlen($cleanPhone) === 11) {
            $cleanPhone = '+7' . substr($cleanPhone, 1);
        }
        // Если номер из 10 цифр, добавляем +7
        elseif (strlen($cleanPhone) === 10) {
            $cleanPhone = '+7' . $cleanPhone;
        }
        // Если номер из 11 цифр и начинается с 7, добавляем +
        elseif (strlen($cleanPhone) === 11 && strpos($cleanPhone, '7') === 0) {
            $cleanPhone = '+' . $cleanPhone;
        }

        return $cleanPhone;
    }

    /**
     * Проверяет, является ли номер валидным
     *
     * @param string $phone
     * @return bool
     */
    private function isValidPhone(string $phone): bool
    {
        // Удаляем + для проверки
        $digitsOnly = ltrim($phone, '+');
        
        // Проверяем длину (должно быть 11 цифр для российских номеров)
        if (strlen($digitsOnly) !== 11) {
            return false;
        }

        // Проверяем, что начинается с 7 (Россия)
        if (strpos($digitsOnly, '7') !== 0) {
            return false;
        }

        // Проверяем, что все символы - цифры
        if (!ctype_digit($digitsOnly)) {
            return false;
        }

        return true;
    }

    /**
     * Форматирует телефонный номер
     *
     * @param string $phone
     * @return string
     */
    private function formatPhone(string $phone): string
    {
        // Убираем + для форматирования
        $digitsOnly = ltrim($phone, '+');
        
        if (strlen($digitsOnly) === 11 && strpos($digitsOnly, '7') === 0) {
            // Форматируем как +7 (XXX) XXX-XX-XX
            return '+7 (' . substr($digitsOnly, 1, 3) . ') ' . 
                   substr($digitsOnly, 4, 3) . '-' . 
                   substr($digitsOnly, 7, 2) . '-' . 
                   substr($digitsOnly, 9, 2);
        }

        return $phone;
    }

    /**
     * Вычисляет уровень уверенности в том, что сообщение содержит контактные данные
     *
     * @param string $text
     * @param array $contacts
     * @return float
     */
    private function calculateConfidence(string $text, array $contacts): float
    {
        $confidence = 0.0;

        // Найдены телефоны
        if (!empty($contacts['phones'])) {
            $confidence += 0.6;
        }

        // Найдены email
        if (!empty($contacts['emails'])) {
            $confidence += 0.4;
        }

        // Есть ключевые слова
        if ($contacts['has_contact_intent']) {
            $confidence += 0.3;
        }

        // Бонус за множественные контакты
        $totalContacts = count($contacts['phones']) + count($contacts['emails']);
        if ($totalContacts > 1) {
            $confidence += 0.2;
        }

        return min(1.0, $confidence);
    }

    /**
     * Проверяет, является ли сообщение контактным (содержит контактные данные)
     *
     * @param string $text
     * @param float $threshold Порог уверенности (по умолчанию 0.5)
     * @return bool
     */
    public function isContactMessage(string $text, float $threshold = 0.5): bool
    {
        $contacts = $this->detectContacts($text);
        return $contacts['confidence'] >= $threshold;
    }

    /**
     * Извлекает все контактные данные из массива сообщений
     *
     * @param array $messages Массив сообщений
     * @return array Объединенные контактные данные
     */
    public function extractContactsFromMessages(array $messages): array
    {
        $allPhones = [];
        $allEmails = [];
        $maxConfidence = 0.0;

        foreach ($messages as $message) {
            $text = $message['content'] ?? $message['text'] ?? '';
            $contacts = $this->detectContacts($text);
            
            $allPhones = array_merge($allPhones, $contacts['phones']);
            $allEmails = array_merge($allEmails, $contacts['emails']);
            $maxConfidence = max($maxConfidence, $contacts['confidence']);
        }

        return [
            'phones' => array_unique($allPhones),
            'emails' => array_unique($allEmails),
            'confidence' => $maxConfidence
        ];
    }
}
