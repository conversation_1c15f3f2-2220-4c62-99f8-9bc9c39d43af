<?php

namespace App\Services;

use Exception;

class EdgeTtsService {

    private $tts; // Убираем строгую типизацию для безопасности
    private string $defaultVoice = 'ru-RU-SvetlanaNeural';
    private array $defaultOptions = [
        'rate'   => '-5%',
        'pitch'  => '0Hz',
        'volume' => '0%'
    ];
    private int $timeout = 60;
    private array $settings;

    /**
     * Загружаем настройки TTS из базы данных
     */
    private function loadSettings(): array {
        try {
            $settingsModel = new \App\Models\Settings();
            $settings = $settingsModel->getSettings();
            return [
                'voice' => $settings['tts_voice'] ?? 'SvetlanaNeural',
                'locale' => $settings['tts_locale'] ?? 'ru-RU'
            ];
        } catch (\Exception $e) {
            error_log("Failed to load TTS settings: " . $e->getMessage());
            return [
                'voice' => 'SvetlanaNeural',
                'locale' => 'ru-RU'
            ];
        }
    }

    // Простой статический кеш в памяти процесса
    private static array $ttsCache = [];
    private const CACHE_MAX_SIZE = 100; // Ограничение размера кеша

    public function __construct(int $timeout = 60) {
        // Безопасная инициализация EdgeTTS
        if (class_exists('Afaya\EdgeTTS\Service\EdgeTTS')) {
            try {
                $edgeTtsClass = 'Afaya\EdgeTTS\Service\EdgeTTS';
                $this->tts = new $edgeTtsClass();
                error_log("EdgeTTS initialized successfully");
            } catch (\Exception $e) {
                error_log("EdgeTTS initialization failed: " . $e->getMessage());
                $this->tts = null;
            }
        } else {
            error_log("EdgeTTS class not found, TTS will be disabled");
            $this->tts = null;
        }

        $this->timeout = $timeout;
        $this->settings = $this->loadSettings();

        // Устанавливаем defaultVoice на основе настроек из БД
        // Проверяем, содержит ли voice уже локаль (формат locale-voice)
        if (strpos($this->settings['voice'], '-') !== false) {
            $this->defaultVoice = $this->settings['voice'];
        } else {
            $this->defaultVoice = $this->settings['locale'] . '-' . $this->settings['voice'];
        }
    }

    /**
     * Cleans text for TTS.
     * @param string $text
     * @return string
     */
    private function cleanText(string $text): string {
        // Оставляем чистку как есть, предполагая, что она не главный тормоз
        $text = strip_tags($text);
        $text = preg_replace('/```.*?```/s', '', $text);
        $text = preg_replace('/!\[[^\]]*\]\([^)]+\)/', '', $text);
        $text = preg_replace('/\[([^\]]+)\]\([^)]+\)/', '$1', $text);
        $text = preg_replace('/\b(https?|ftp):\/\/[^\s\/$.?#].[^\s]*/i', '', $text);
        $text = preg_replace('/\s?\*{2}(.+?)\*{2}\s?/s', '$1', $text);
        $text = str_replace(['**', '*', '_', '`'], '', $text);
        $text = preg_replace('/^#+\s*/m', '', $text);
        $text = preg_replace('/^\s*[-*+]\s+/m', '', $text);
        $text = preg_replace('/^\s*\d+\.\s+/m', '', $text);
        $text = preg_replace('/\s+/', ' ', $text);
        return trim($text);
    }

    /**
     * Synthesizes speech using afaya/edge-tts with in-memory caching.
     *
     * @param string $text Text to synthesize.
     * @param string|null $voiceShortName ShortName like 'ru-RU-SvetlanaNeural'. Uses default if null.
     * @param array|null $options Override options like ['rate' => '...', 'pitch' => '...', 'volume' => '...']. Uses defaults if null.
     * @return string|null Base64 encoded audio data or null on failure.
     * @throws \Exception On synthesis error (re-thrown).
     */
    public function synthesizeSpeech(string $text, ?string $voiceShortName = null, ?array $options = null): ?string {
        // Проверяем, инициализирован ли TTS
        if ($this->tts === null) {
            error_log("TTS service not available - EdgeTTS library not installed or failed to initialize");
            return null;
        }

        $cleanedText = $this->cleanText($text);
        if (empty($cleanedText)) {
            // error_log(...) // Логирование можно оставить или убрать для макс. скорости
            return null;
        }

        // Получаем голос, проверяем формат и добавляем локаль, если нужно
        $voice = $voiceShortName ?? $this->defaultVoice;

        // Если голос не содержит локаль (формат locale-voice), добавляем её
        if (strpos($voice, '-') === false) {
            $voice = $this->settings['locale'] . '-' . $voice;
        }

        $synthOptions = $options ?? $this->defaultOptions;

        // --- Кеширование ---
        // Создаем уникальный ключ для кеша
        $cacheKey = md5($cleanedText . '|' . $voice . '|' . json_encode($synthOptions)); // json_encode для опций

        // Проверяем кеш
        if (isset(self::$ttsCache[$cacheKey])) {
            // error_log("TTS Service: Cache hit for key " . $cacheKey); // Лог для отладки кеша
            return self::$ttsCache[$cacheKey];
        }
        // --- Конец Кеширования ---

        $originalTimeout = ini_get('max_execution_time'); // Сохраняем исходный таймаут

        try {
            // Устанавливаем таймаут на время синтеза
            // set_time_limit() может не работать в safe_mode или если отключена.
            // Также может быть неэффективной, если синтез - это в основном ожидание сети.
            if (function_exists('set_time_limit')) {
                set_time_limit($this->timeout + 5); // Небольшой запас
            }

            // ---- Основной вызов библиотеки ----
            $this->tts->synthesize($cleanedText, $voice, $synthOptions);
            $audioBase64 = $this->tts->toBase64();
            // ---------------------------------

            // Восстанавливаем исходный таймаут
            if (function_exists('set_time_limit')) {
                set_time_limit((int)$originalTimeout);
            }

            if (empty($audioBase64)) {
                // error_log(...) // Логирование можно оставить
                 // Не кешируем пустой результат
                return null;
            }

            // --- Сохранение в кеш ---
            // Ограничиваем размер кеша (простая FIFO-подобная логика)
            if (count(self::$ttsCache) >= self::CACHE_MAX_SIZE) {
                array_shift(self::$ttsCache); // Удаляем самый старый элемент
            }
            self::$ttsCache[$cacheKey] = $audioBase64;
            // error_log("TTS Service: Cached result for key " . $cacheKey); // Лог для отладки кеша
            // --- Конец сохранения в кеш ---

            return $audioBase64;

        } catch (Exception $e) { // Ловим базовый Exception
             // Восстанавливаем исходный таймаут при ошибке
             if (function_exists('set_time_limit')) {
                 set_time_limit((int)$originalTimeout);
             }
             error_log(sprintf(
                "EdgeTTS Service Error: %s (Voice: %s, Options: %s, CleanedText: %s...)",
                $e->getMessage(), $voice, json_encode($synthOptions), substr($cleanedText, 0, 100)
             ));
             // Перебрасываем исключение, чтобы вызывающий код знал об ошибке
             throw new Exception("EdgeTTS Synthesis Error: " . $e->getMessage(), 502, $e);
        }
    }
}
