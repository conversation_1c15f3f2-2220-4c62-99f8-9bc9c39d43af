<?php

namespace App\Services;

/**
 * Простой email сервис без PHPMailer
 * Использует встроенную функцию mail() с правильными настройками
 */
class WorkingEmailService
{
    private $config;

    public function __construct($config = [])
    {
        $this->config = array_merge([
            'host' => 'smtp.yandex.ru',
            'port' => 587,
            'username' => '',
            'password' => '',
            'encryption' => 'tls',
            'debug' => false
        ], $config);
    }

    /**
     * Отправка email через встроенную функцию mail()
     */
    public function sendEmail($to, $subject, $body, $from, $fromName = '')
    {
        try {
            // Настраиваем PHP для отправки через Yandex
            ini_set('SMTP', $this->config['host']);
            ini_set('smtp_port', $this->config['port']);
            ini_set('sendmail_from', $this->config['username']);
            
            // Для Windows также устанавливаем аутентификацию
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                ini_set('auth_username', $this->config['username']);
                ini_set('auth_password', $this->config['password']);
            }
            
            // Формируем заголовки
            $headers = [];
            
            if ($fromName) {
                $headers[] = "From: $fromName <$from>";
            } else {
                $headers[] = "From: $from";
            }
            
            $headers[] = "Reply-To: $from";
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
            $headers[] = "Content-Transfer-Encoding: 8bit";
            $headers[] = "X-Mailer: WorkingEmailService/1.0";
            $headers[] = "Date: " . date('r');
            $headers[] = "Message-ID: <" . uniqid() . "@" . $this->config['host'] . ">";
            
            $headerString = implode("\r\n", $headers);
            
            // Отправляем email
            $result = mail($to, $subject, $body, $headerString);
            
            if ($result) {
                error_log("WorkingEmailService: Email sent successfully to $to");
                return true;
            } else {
                error_log("WorkingEmailService: Failed to send email to $to");
                
                // Создаем EML файл как fallback
                return $this->saveAsEmlFile($to, $subject, $body, $from, $fromName);
            }
            
        } catch (\Exception $e) {
            error_log("WorkingEmailService: Error: " . $e->getMessage());
            return $this->saveAsEmlFile($to, $subject, $body, $from, $fromName);
        }
    }
    
    /**
     * Сохраняет email как EML файл
     */
    private function saveAsEmlFile($to, $subject, $body, $from, $fromName = '')
    {
        try {
            $filename = 'email_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.eml';
            $filepath = __DIR__ . '/../../' . $filename;
            
            $emlContent = "From: " . ($fromName ? "$fromName <$from>" : $from) . "\n";
            $emlContent .= "To: $to\n";
            $emlContent .= "Subject: $subject\n";
            $emlContent .= "Date: " . date('r') . "\n";
            $emlContent .= "Content-Type: text/plain; charset=UTF-8\n";
            $emlContent .= "Message-ID: <" . uniqid() . "@yandex.ru>\n\n";
            $emlContent .= $body;
            
            $result = file_put_contents($filepath, $emlContent);
            
            if ($result !== false) {
                error_log("WorkingEmailService: Email saved as EML file: $filepath");
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            error_log("WorkingEmailService: Error saving EML: " . $e->getMessage());
            return false;
        }
    }
}
