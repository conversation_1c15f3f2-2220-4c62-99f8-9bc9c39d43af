<?php
/**
 * Тестовый файл для проверки исправления автозаполнения в CRM настройках
 * 
 * Этот файл создает простую HTML страницу для тестирования того, что браузер
 * больше не автозаполняет поля AmoCRM токена паролем от входа в админку.
 */

// Симуляция настроек для тестирования
$settings = [
    'amocrm_subdomain' => 'testcompany',
    'amocrm_access_token' => 'test_token_12345'
];
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест исправления автозаполнения CRM</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 2rem;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.375rem;
        }
        .success { background-color: #d1edff; border-left: 4px solid #0d6efd; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        
        /* Копируем стили из основного файла */
        .token-field {
            font-family: 'Courier New', Consolas, monospace !important;
            letter-spacing: 1px;
            background-color: #f8f9fa;
            border: 1px solid #ced4da;
            transition: all 0.3s ease;
        }
        
        .crm-field {
            background-color: #f8f9fa;
            border: 1px solid #ced4da;
            transition: all 0.3s ease;
        }
        
        .token-field:focus,
        .crm-field:focus {
            background-color: #ffffff;
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .toggle-visibility {
            border-left: none;
            background-color: #f8f9fa;
            border-color: #ced4da;
            color: #6c757d;
            transition: all 0.3s ease;
        }
        
        .toggle-visibility:hover {
            background-color: #e9ecef;
            color: #495057;
        }
        
        /* Защита от автозаполнения */
        .token-field:-webkit-autofill,
        .token-field:-webkit-autofill:hover,
        .token-field:-webkit-autofill:focus,
        .crm-field:-webkit-autofill,
        .crm-field:-webkit-autofill:hover,
        .crm-field:-webkit-autofill:focus {
            -webkit-box-shadow: 0 0 0 1000px #f8f9fa inset !important;
            -webkit-text-fill-color: #495057 !important;
            transition: background-color 5000s ease-in-out 0s;
        }
        
        input[name="fake_username"],
        input[name="fake_password"] {
            position: absolute !important;
            left: -9999px !important;
            opacity: 0 !important;
            pointer-events: none !important;
            tab-index: -1 !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">
            <i class="fas fa-shield-alt text-primary"></i>
            Тест исправления автозаполнения CRM
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Инструкция по тестированию:</h5>
            <ol>
                <li>Сначала войдите в админку с вашими учетными данными</li>
                <li>Затем откройте эту страницу</li>
                <li>Проверьте, что поля ниже НЕ заполняются автоматически паролем от админки</li>
                <li>Попробуйте ввести тестовые данные и проверить функциональность</li>
            </ol>
        </div>

        <!-- Тест 1: Форма входа (для сравнения) -->
        <div class="test-section">
            <h3><i class="fas fa-sign-in-alt text-secondary"></i> Тест 1: Обычная форма входа</h3>
            <p class="text-muted">Эта форма должна автозаполняться браузером (для сравнения)</p>
            
            <form autocomplete="on">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="test_username" class="form-label">Имя пользователя</label>
                            <input type="text" id="test_username" name="username" class="form-control" placeholder="Введите имя пользователя">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="test_password" class="form-label">Пароль</label>
                            <input type="password" id="test_password" name="password" class="form-control" placeholder="Введите пароль">
                        </div>
                    </div>
                </div>
            </form>
            
            <div class="test-result warning">
                <strong>Ожидаемое поведение:</strong> Браузер должен предложить автозаполнение этих полей
            </div>
        </div>

        <!-- Тест 2: Защищенная форма CRM -->
        <div class="test-section">
            <h3><i class="fas fa-shield-alt text-success"></i> Тест 2: Защищенная форма CRM</h3>
            <p class="text-muted">Эта форма НЕ должна автозаполняться браузером</p>
            
            <form autocomplete="off" id="crm-test-form">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="amocrm_subdomain" class="form-label">Поддомен AmoCRM</label>
                            <input type="text"
                                   id="amocrm_subdomain"
                                   name="amocrm_subdomain"
                                   class="form-control crm-field"
                                   value="<?= htmlspecialchars($settings['amocrm_subdomain'] ?? '') ?>"
                                   placeholder="mycompany"
                                   autocomplete="off"
                                   autocorrect="off"
                                   autocapitalize="off"
                                   spellcheck="false"
                                   data-lpignore="true"
                                   data-form-type="other">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="amocrm_access_token" class="form-label">Access Token</label>
                            <div class="input-group">
                                <input type="text"
                                       id="amocrm_access_token"
                                       name="amocrm_access_token"
                                       class="form-control token-field"
                                       value="<?= htmlspecialchars($settings['amocrm_access_token'] ?? '') ?>"
                                       placeholder="Введите Access Token"
                                       autocomplete="off"
                                       autocorrect="off"
                                       autocapitalize="off"
                                       spellcheck="false"
                                       data-lpignore="true"
                                       data-form-type="other"
                                       style="font-family: monospace; letter-spacing: 1px;">
                                <button type="button" class="btn btn-outline-secondary toggle-visibility" data-target="amocrm_access_token">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="testFormValues()">
                    <i class="fas fa-check"></i> Проверить значения полей
                </button>
            </form>
            
            <div class="test-result success">
                <strong>Ожидаемое поведение:</strong> Браузер НЕ должен автозаполнять эти поля паролем от админки
            </div>
        </div>

        <!-- Результаты тестирования -->
        <div class="test-section">
            <h3><i class="fas fa-clipboard-check text-info"></i> Результаты тестирования</h3>
            <div id="test-results">
                <p class="text-muted">Нажмите кнопку "Проверить значения полей" выше для получения результатов</p>
            </div>
        </div>
    </div>

    <script>
        // Копируем JavaScript из основного файла
        document.addEventListener('DOMContentLoaded', function() {
            // Функция для переключения видимости токена
            function setupTokenVisibilityToggle() {
                const toggleButtons = document.querySelectorAll('.toggle-visibility');
                
                toggleButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const targetId = this.getAttribute('data-target');
                        const targetInput = document.getElementById(targetId);
                        const icon = this.querySelector('i');
                        
                        if (targetInput.type === 'text') {
                            targetInput.type = 'password';
                            icon.className = 'fas fa-eye';
                            this.title = 'Показать токен';
                        } else {
                            targetInput.type = 'text';
                            icon.className = 'fas fa-eye-slash';
                            this.title = 'Скрыть токен';
                        }
                    });
                });
            }
            
            // Защита от автозаполнения
            function preventAutofill() {
                const protectedFields = document.querySelectorAll('.token-field, .crm-field');
                
                protectedFields.forEach(field => {
                    const originalValue = field.value;
                    
                    field.addEventListener('focus', function() {
                        setTimeout(() => {
                            if (this.value !== originalValue && this.value.length > 0 && originalValue.length > 0) {
                                this.value = originalValue;
                                console.log('Предотвращено автозаполнение поля:', this.name);
                            }
                        }, 100);
                    });
                    
                    field.addEventListener('input', function() {
                        if (document.activeElement === this) {
                            field.dataset.userModified = 'true';
                        }
                    });
                });
            }
            
            // Создание полей-приманок
            function createDecoyFields() {
                const form = document.getElementById('crm-test-form');
                
                const decoyUsername = document.createElement('input');
                decoyUsername.type = 'text';
                decoyUsername.name = 'fake_username';
                decoyUsername.style.position = 'absolute';
                decoyUsername.style.left = '-9999px';
                decoyUsername.style.opacity = '0';
                decoyUsername.tabIndex = -1;
                decoyUsername.autocomplete = 'username';
                
                const decoyPassword = document.createElement('input');
                decoyPassword.type = 'password';
                decoyPassword.name = 'fake_password';
                decoyPassword.style.position = 'absolute';
                decoyPassword.style.left = '-9999px';
                decoyPassword.style.opacity = '0';
                decoyPassword.tabIndex = -1;
                decoyPassword.autocomplete = 'current-password';
                
                form.insertBefore(decoyUsername, form.firstChild);
                form.insertBefore(decoyPassword, form.firstChild);
            }
            
            setupTokenVisibilityToggle();
            preventAutofill();
            createDecoyFields();
        });
        
        // Функция для тестирования значений полей
        function testFormValues() {
            const subdomain = document.getElementById('amocrm_subdomain').value;
            const token = document.getElementById('amocrm_access_token').value;
            const resultsDiv = document.getElementById('test-results');
            
            let results = '<h5>Результаты проверки:</h5>';
            
            // Проверяем поддомен
            if (subdomain === 'testcompany') {
                results += '<div class="alert alert-success"><i class="fas fa-check"></i> <strong>Поддомен:</strong> Корректное значение (не изменено автозаполнением)</div>';
            } else if (subdomain === '') {
                results += '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> <strong>Поддомен:</strong> Поле пустое</div>';
            } else {
                results += '<div class="alert alert-danger"><i class="fas fa-times"></i> <strong>Поддомен:</strong> Возможно автозаполнено браузером! Значение: ' + subdomain + '</div>';
            }
            
            // Проверяем токен
            if (token === 'test_token_12345') {
                results += '<div class="alert alert-success"><i class="fas fa-check"></i> <strong>Access Token:</strong> Корректное значение (не изменено автозаполнением)</div>';
            } else if (token === '') {
                results += '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> <strong>Access Token:</strong> Поле пустое</div>';
            } else {
                results += '<div class="alert alert-danger"><i class="fas fa-times"></i> <strong>Access Token:</strong> Возможно автозаполнено браузером! Значение: ' + token + '</div>';
            }
            
            // Общий результат
            if (subdomain === 'testcompany' && token === 'test_token_12345') {
                results += '<div class="alert alert-success mt-3"><h6><i class="fas fa-trophy"></i> Тест пройден успешно!</h6>Автозаполнение браузером заблокировано корректно.</div>';
            } else {
                results += '<div class="alert alert-warning mt-3"><h6><i class="fas fa-exclamation-triangle"></i> Требуется внимание</h6>Проверьте настройки браузера или попробуйте очистить сохраненные пароли.</div>';
            }
            
            resultsDiv.innerHTML = results;
        }
    </script>
</body>
</html>
