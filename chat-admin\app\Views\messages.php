<?php
// Ожидаем переменные из контроллера
$user = $user ?? ['username' => 'Unknown', 'email' => ''];
$messages = $messages ?? [];
$filterData = $filterData ?? ['users' => [], 'sessions' => []];
$currentFilters = $currentFilters ?? [];
$currentPage = $currentPage ?? 1;
$totalPages = $totalPages ?? 1;
$totalMessages = $totalMessages ?? 0;
$flashMessage = $flashMessage ?? null;
$flashType = $flashType ?? 'info';
$pageName = $pageName ?? 'messages'; // Для header.php

require __DIR__ . '/partials/header.php';

// Формируем базовый URL для пагинации и форм (сохраняя текущие фильтры)
$baseQuery = http_build_query(array_merge($currentFilters, ['page' => $currentPage])); // Используем текущие фильтры
$baseUrl = 'index.php?page=messages'; // Базовый URL страницы
$paginationBaseUrl = $baseUrl . '&' . http_build_query($currentFilters); // URL для пагинации без параметра page

?>
<!-- Подключаем специфичные стили, если они были в messages.css -->
<!-- <link rel="stylesheet" href="public/admin-css/messages.css"> -->

<div class="content-header">
    <h1 class="content-title">История сообщений</h1>
    <!-- Кнопка удаления чата будет добавлена JS, если выбран чат -->
</div>

<div class="container">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Фильтры</h2>
        </div>

        <!-- Сообщение об успехе/ошибке -->
        <?php if ($flashMessage): ?>
            <div class="alert alert-<?= htmlspecialchars($flashType) ?>">
                <i class="fas <?= $flashType === 'success' ? 'fa-check-circle' : ($flashType === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle') ?>"></i>
                <?= htmlspecialchars($flashMessage) ?>
            </div>
        <?php endif; ?>

        <form method="GET" class="filters">
            <input type="hidden" name="page" value="messages"> <!-- Указываем текущую страницу для роутера -->
            <div class="filter-group">
                <label for="session_id" class="filter-label">Чат</label>
                <select name="session_id" id="session_id" class="filter-control">
                    <option value="">Все чаты</option>
                    <?php foreach ($filterData['sessions'] as $session): ?>
                        <option value="<?= $session['id'] ?>" <?= ($currentFilters['sessionId'] ?? null) == $session['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($session['title'] ?: "Чат #{$session['id']}") ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="filter-group">
                <label for="user_id" class="filter-label">Пользователь</label>
                <select name="user_id" id="user_id" class="filter-control">
                    <option value="">Все пользователи</option>
                    <?php foreach ($filterData['users'] as $chatUser): ?>
                        <option value="<?= htmlspecialchars($chatUser['id']) ?>" <?= ($currentFilters['userId'] ?? null) == $chatUser['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($chatUser['display_name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="filter-group">
                <label for="search" class="filter-label">Поиск</label>
                <input type="text" name="search" id="search" class="filter-control" value="<?= htmlspecialchars($currentFilters['search'] ?? '') ?>" placeholder="Поиск по тексту">
            </div>

            <div class="filter-group">
                <label for="date_from" class="filter-label">Дата от</label>
                <input type="date" name="date_from" id="date_from" class="filter-control" value="<?= htmlspecialchars($currentFilters['dateFrom'] ?? '') ?>">
            </div>

            <div class="filter-group">
                <label for="date_to" class="filter-label">Дата до</label>
                <input type="date" name="date_to" id="date_to" class="filter-control" value="<?= htmlspecialchars($currentFilters['dateTo'] ?? '') ?>">
            </div>

            <button type="submit" class="btn btn-primary"> <!-- Сделали основной -->
                <i class="fas fa-filter"></i> Применить
            </button>
             <a href="<?= $baseUrl ?>" class="btn btn-secondary"> <!-- Сделали вторичной -->
                 <i class="fas fa-times"></i> Сбросить
             </a>
        </form>
        <!-- Кнопка для массового удаления -->
        <div style="margin-top: -1rem; margin-bottom: 2rem;"> <!-- Размещаем под фильтрами -->
             <button type="button" id="delete-selected-btn" class="btn btn-danger" disabled>
                 <i class="fas fa-trash-alt"></i> Удалить выбранные
             </button>
        </div>
    </div>

    <div class="card">
         <div class="card-header">
             <h2 class="card-title">Сообщения (<?= $totalMessages ?>)</h2>
         </div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th style="width: 1%;"><input type="checkbox" id="select-all-messages" title="Выбрать все"></th> <!-- Чекбокс "Выбрать все" -->
                        <th>Дата</th>
                        <th>Пользователь</th>
                        <th>Тип</th>
                        <th>Сообщение</th>
                        <th style="text-align: right;">Действия</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($messages)): ?>
                        <tr>
                            <td colspan="5" style="text-align: center; color: var(--text-light); padding: 2rem;">Сообщения не найдены.</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($messages as $message): ?>
                            <tr class="<?= $message['sender'] === 'user' ? 'user-message' : 'assistant-message' ?>" data-message-id="<?= $message['id'] ?>">
                                <td><input type="checkbox" class="message-checkbox" name="message_ids[]" value="<?= $message['id'] ?>"></td>
                                <td title="<?= htmlspecialchars($message['timestamp']) ?>">
                                    <?= date('d.m.Y H:i', strtotime($message['timestamp'])) ?>
                                </td>
                                <td><?= htmlspecialchars($message['username'] ?: ($message['user_id'] ?: 'Гость')) ?></td>
                                <td>
                                    <span class="badge <?= $message['sender'] === 'user' ? 'badge-user' : 'badge-assistant' ?>">
                                        <?= $message['sender'] === 'user' ? 'Пользователь' : 'Ассистент' ?>
                                    </span>
                                </td>
                                <!-- Используем nl2br для сохранения переносов строк, htmlspecialchars для безопасности -->
                                <td><?= nl2br(htmlspecialchars($message['content'] ?? '')) ?></td>
                                <td>
                                    <!-- Форма для удаления сообщения -->
                                    <form action="index.php?page=messages&action=deleteMessage&<?= $baseQuery ?>" method="POST" style="display: inline;" class="delete-message-form">
                                        <input type="hidden" name="id" value="<?= $message['id'] ?>">
                                        <!-- TODO: Добавить CSRF токен -->
                                        <!-- Применяем стили btn btn-danger btn-sm -->
                                        <button type="submit" class="btn btn-danger btn-sm delete-message-btn" title="Удалить сообщение">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php
        // Явно приведем totalPages к int перед сравнением, на всякий случай
        $totalPagesInt = (int)$totalPages;
        if ($totalPagesInt > 1):
        ?>
            <div class="pagination">
                <?php
                // Логика для отображения ограниченного числа страниц
                $maxPagesToShow = 7; // Максимальное количество ссылок на страницы
                $halfMax = floor($maxPagesToShow / 2);
                // Приводим $currentPage к int перед использованием в вычислениях
                $currentPageInt = (int)$currentPage;
                $startPage = max(1, $currentPageInt - $halfMax);
                $endPage = min($totalPagesInt, $currentPageInt + $halfMax); // Use $totalPagesInt

                // Корректировка, если в начале или конце не хватает страниц (используем $currentPageInt)
                if ($currentPageInt <= $halfMax) {
                    $endPage = min($totalPagesInt, $maxPagesToShow); // Use $totalPagesInt
                }
                if ($currentPageInt > $totalPagesInt - $halfMax) { // Use $totalPagesInt
                    $startPage = max(1, $totalPagesInt - $maxPagesToShow + 1); // Use $totalPagesInt
                }

                // Кнопка "Назад"
                if ($currentPageInt > 1): ?>
                    <a href="<?= $paginationBaseUrl ?>&page=<?= $currentPageInt - 1 ?>" class="page-link">&laquo;</a>
                <?php endif;

                // Первая страница и многоточие
                if ($startPage > 1): ?>
                    <a href="<?= $paginationBaseUrl ?>&page=1" class="page-link">1</a>
                    <?php if ($startPage > 2): ?>
                        <span class="page-link" style="border:none; background:none;">...</span>
                    <?php endif;
                endif;

                // Основные страницы
                for ($i = $startPage; $i <= $endPage; $i++): ?>
                    <a href="<?= $paginationBaseUrl ?>&page=<?= $i ?>"
                       class="page-link <?= $i == $currentPageInt ? 'active' : '' ?>">
                        <?= $i ?>
                    </a>
                <?php endfor;

                // Последняя страница и многоточие
                if ($endPage < $totalPagesInt): // Use $totalPagesInt
                    if ($endPage < $totalPagesInt - 1): ?>
                         <span class="page-link" style="border:none; background:none;">...</span>
                    <?php endif; ?>
                    <a href="<?= $paginationBaseUrl ?>&page=<?= $totalPagesInt ?>" class="page-link"><?= $totalPagesInt ?></a>
                <?php endif;

                 // Кнопка "Вперед"
                 if ($currentPage < $totalPages): ?>
                    <a href="<?= $paginationBaseUrl ?>&page=<?= $currentPage + 1 ?>" class="page-link">&raquo;</a>
                 <?php endif; ?>
            </div>
        <?php endif; ?>
    </div> <!-- .card -->
</div> <!-- .container -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Подтверждение удаления сообщения
    const deleteForms = document.querySelectorAll('.delete-message-form');
    deleteForms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!confirm('Вы уверены, что хотите удалить это сообщение?')) {
                event.preventDefault(); // Отменяем отправку формы
            } else {
                 // Опционально: добавить индикатор загрузки
                 const button = form.querySelector('.delete-message-btn');
                 if(button) {
                     button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                     button.disabled = true;
                 }
            }
        });
    });

    // Динамическое добавление кнопки "Удалить чат" и обработка клика
    const currentSessionId = new URLSearchParams(window.location.search).get('session_id');
    if (currentSessionId) {
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'btn';
        deleteBtn.style.marginLeft = '1rem';
        deleteBtn.style.backgroundColor = 'var(--error)';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i> Удалить текущий чат';
        deleteBtn.addEventListener('click', async function() {
            if (!confirm('Вы уверены, что хотите удалить этот чат? Все сообщения и связанные файлы будут удалены.')) {
                return;
            }

            deleteBtn.disabled = true;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Удаление...';

            try {
                // Отправляем AJAX POST запрос на новый эндпоинт
                const response = await fetch('index.php?page=messages&action=deleteChatSession', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                        // TODO: Добавить CSRF токен в заголовки, если он используется
                    },
                    body: `id=${encodeURIComponent(currentSessionId)}`
                });

                // Всегда пытаемся распарсить JSON, даже при ошибке сервера
                let data = {};
                try {
                     data = await response.json();
                } catch (e) {
                     console.error("Failed to parse JSON response:", e);
                     // Если JSON не парсится, пытаемся получить текст ответа
                     const textResponse = await response.text();
                     throw new Error(`Server returned non-JSON response: ${response.status} ${response.statusText}. Response body: ${textResponse}`);
                }


                if (!response.ok || !data.success) {
                    throw new Error(data.error || 'Неизвестная ошибка сервера');
                }

                // Показываем уведомление
                alert(`Чат успешно удален. Удалено сообщений: ${data.stats?.messages ?? 0}, файлов: ${data.stats?.files ?? 0}`);

                // Перенаправляем на страницу сообщений без ID сессии
                window.location.href = 'index.php?page=messages';

            } catch (error) {
                console.error('Ошибка удаления чата:', error);
                alert(`Ошибка удаления чата: ${error.message}`);
                 deleteBtn.disabled = false; // Возвращаем кнопку в исходное состояние при ошибке
                 deleteBtn.innerHTML = '<i class="fas fa-trash"></i> Удалить текущий чат';
            }
            // finally не нужен, т.к. при успехе происходит редирект
        });

        const contentHeader = document.querySelector('.content-header');
        if (contentHeader) {
            contentHeader.appendChild(deleteBtn);
        }
    }

     // Скрытие flash-сообщений
     const alertMessage = document.querySelector('.alert');
     if (alertMessage) {
         setTimeout(() => {
             alertMessage.style.transition = 'opacity 0.5s ease';
             alertMessage.style.opacity = '0';
             setTimeout(() => alertMessage.remove(), 500);
         }, 5000);
     }

    // Логика для чекбоксов и массового удаления
    const selectAllCheckbox = document.getElementById('select-all-messages');
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    const deleteSelectedBtn = document.getElementById('delete-selected-btn');

    function toggleDeleteButton() {
        const anyChecked = Array.from(messageCheckboxes).some(cb => cb.checked);
        deleteSelectedBtn.disabled = !anyChecked;
    }

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            messageCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            toggleDeleteButton();
        });
    }

    messageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (!this.checked) {
                selectAllCheckbox.checked = false;
            } else {
                // Проверяем, все ли остальные выбраны
                const allChecked = Array.from(messageCheckboxes).every(cb => cb.checked);
                selectAllCheckbox.checked = allChecked;
            }
            toggleDeleteButton();
        });
    });

    // Обработчик для кнопки "Удалить выбранные" (пока просто выводит ID в консоль)
    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', function() {
            const selectedIds = Array.from(messageCheckboxes)
                                    .filter(cb => cb.checked)
                                    .map(cb => cb.value);

            if (selectedIds.length === 0) {
                alert('Пожалуйста, выберите хотя бы одно сообщение для удаления.');
                return;
            }

            if (confirm(`Вы уверены, что хотите удалить ${selectedIds.length} выбранных сообщений?`)) {
                // TODO: Реализовать отправку AJAX запроса на сервер для удаления
                // Реализуем массовое удаление через AJAX
                fetch('index.php?page=messages&action=deleteSelectedMessages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ ids: selectedIds })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Выбранные сообщения удалены.');
                        window.location.reload(); // Перезагрузить страницу
                    } else {
                        alert('Ошибка удаления: ' + (data.error || 'Неизвестная ошибка'));
                    }
                })
                .catch(error => {
                    console.error('Ошибка AJAX:', error);
                    alert('Ошибка при отправке запроса на удаление.');
                });
            }
        });
    }
});
</script>

<?php require __DIR__ . '/partials/footer.php'; ?>
