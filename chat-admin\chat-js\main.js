// --- START OF FILE main.js ---
// chat-js/main.js

// Импортируем инициализатор конфига ПЕРЕД остальными модулями, которые могут его использовать
import { initializeConfig } from './config.js';

// Теперь импортируем остальные модули
import { initChat, handleSendMessage } from './chatHandler.js';
import { initVoiceCall, openVoiceCall, setVoiceCallAvatar, handleRefreshConnection } from './voiceCallHandler.js';
import textManager from './textManager.js'; // Импортируем менеджер текстов
import analyticsCollector from './analyticsCollector.js'; // Импортируем аналитический модуль

// Expose voice call handler functions globally for UI interactions
window.voiceCallHandler = { handleRefreshConnection };

// Expose analytics functions globally
window.analyticsCollector = analyticsCollector;

import { initFloatingButton } from './floatingButton.js';
// import { toggleVoiceInput, isVoiceInputActive } from './voiceInput.js'; // Импорт не используется

/**
 * Fetches design settings from the API (custom-style.css) and applies them.
 * @param {string} chatAdminBaseUrl - The base URL of the chat-admin directory.
 */
async function applyDesignSettings(chatAdminBaseUrl) {
    // Гарантируем слеш в конце
    const baseUrl = chatAdminBaseUrl.endsWith('/') ? chatAdminBaseUrl : chatAdminBaseUrl + '/';
    const cssUrl = `${baseUrl}chat-css/custom-style.css?v=${Date.now()}`;
    console.log(`[Main] Загрузка кастомных стилей с: ${cssUrl}`);
    try {
        const response = await fetch(cssUrl, { mode: 'cors' });
        if (!response.ok) {
            console.error(`[Main] Не удалось загрузить кастомные стили (${response.status} ${response.statusText}): ${cssUrl}. Проверьте CORS и путь.`);
            return;
        }
        const cssText = await response.text();

        const styleElement = document.createElement('style');
        styleElement.setAttribute('id', 'znak-custom-styles');
        styleElement.textContent = cssText;
        document.head.appendChild(styleElement);
        console.log('[Main] Кастомные стили успешно загружены и применены.');

    } catch (error) {
        console.error('[Main] Ошибка при загрузке или применении кастомных стилей:', error);
    }
}

/**
 * Initializes all chat modules. Called by chat-loader.js.
 * @param {string} chatAdminBaseUrl - The base URL of the chat-admin directory passed from the loader.
 */
async function initializeChatModules(chatAdminBaseUrl) {
    console.log("[Main] Инициализация модулей чата (вызвано загрузчиком)...");
    console.log(`[Main] Получен базовый URL для ресурсов: ${chatAdminBaseUrl}`);

    try {
        // --- ВАЖНО: ИНИЦИАЛИЗИРУЕМ КОНФИГ В САМОМ НАЧАЛЕ ---
        // Передаем полученный от загрузчика URL в config.js
        initializeConfig(chatAdminBaseUrl);
        console.log("[Main] Конфигурация (config.js) инициализирована с правильным URL.");
        // --- ---

        // 1. Применяем настройки дизайна (загружаем custom-style.css)
        // Передаем chatAdminBaseUrl, который мы получили
        await applyDesignSettings(chatAdminBaseUrl);
        console.log("[Main] Настройки дизайна (custom-style.css) обработаны.");

        // 1.2. Устанавливаем правильные пути к изображениям
        setImagePaths(chatAdminBaseUrl);
        console.log("[Main] Пути к изображениям установлены.");

        // 1.5. Инициализация менеджера текстов
        await textManager.initialize();
        console.log("[Main] Менеджер текстов (textManager) инициализирован.");
        
        // Reinitialize text manager to ensure new texts are loaded
        await textManager.initialize();
        console.log("[Main] Менеджер текстов переинициализирован для загрузки новых текстов");

        // 1.5. Инициализация аналитического модуля
        await analyticsCollector.initAnalyticsCollector();
        console.log("[Main] Аналитический модуль инициализирован.");

        // 2. Инициализация основного чата (UI и логика сообщений/сессий)
        // initChat() теперь будет использовать правильные URL из config.js через геттеры
        initChat();
        console.log("[Main] Модуль основного чата (chatHandler) инициализирован.");

        // 3. Инициализация логики голосового вызова (модальное окно)
        // initVoiceCall() теперь будет использовать правильные URL из config.js через геттеры
        initVoiceCall();
        console.log("[Main] Модуль голосового вызова (voiceCallHandler) инициализирован.");

        // 4. Инициализация плавающей кнопки "Звонок AI"
        initFloatingButton(openVoiceCall);
        console.log("[Main] Плавающая кнопка (floatingButton) инициализирована.");

        // Обработчик микрофона для текстового чата настраивается внутри uiChat.js / initChatUI()
        console.log("[Main] Обработчик микрофона текстового чата настраивается в uiChat (через initChat).");

        // 5. Проверяем автооткрытие чата (с проверкой сообщений)
        await checkSimpleAutoOpen();

        console.log("[Main] Все модули чата успешно инициализированы.");

    } catch (error) {
        console.error("[Main] Критическая ошибка во время основной инициализации модулей чата:", error);
        const body = document.querySelector('body');
        if (body && !body.querySelector('.czn-chat-init-error')) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'czn-chat-init-error czn-chat-main-init-error';
            errorDiv.textContent = textManager.getText('error_init');
            errorDiv.style.cssText = 'position: fixed; bottom: 50px; left: 10px; right: 10px; padding: 12px; background-color: #fff0f0; color: #c00; border: 1px solid #c00; border-radius: 5px; z-index: 10000; text-align: center; font-size: 13px; font-family: sans-serif; box-shadow: 0 2px 4px rgba(0,0,0,0.1);';
            body.appendChild(errorDiv);
             setTimeout(() => {
                 if (errorDiv) errorDiv.remove();
             }, 12000);
        }
    }
}

/**
 * Проверка автооткрытия чата для новых пользователей и пользователей без сообщений
 */
async function checkSimpleAutoOpen() {
    try {
        console.log("[Main] Проверка автооткрытия чата...");

        // Импортируем необходимые модули
        const { getSettings, getUserSessions, getMessages } = await import('./api.js');
        const { openChatWindow } = await import('./uiChat.js');

        // Получаем настройки
        const settingsResponse = await getSettings();

        if (!settingsResponse || !settingsResponse.settings) {
            console.log("[Main] Настройки не получены, автооткрытие пропущено");
            return;
        }

        const settings = settingsResponse.settings;
        const autoOpenEnabled = settings.auto_open_chat == 1;
        const autoOpenDelay = parseInt(settings.auto_open_delay) || 0;

        console.log("[Main] Настройки автооткрытия:", { autoOpenEnabled, autoOpenDelay });

        if (!autoOpenEnabled) {
            console.log("[Main] Автооткрытие отключено в настройках");
            return;
        }

        // Проверяем, нужно ли автооткрытие
        const shouldAutoOpen = await checkIfShouldAutoOpen();

        if (shouldAutoOpen) {
            console.log(`[Main] Запускаем автооткрытие через ${autoOpenDelay}мс`);

            setTimeout(() => {
                console.log("[Main] Автоматически открываем чат");
                try {
                    openChatWindow();
                } catch (error) {
                    console.error("[Main] Ошибка при открытии чата:", error);
                }
            }, autoOpenDelay);
        } else {
            console.log("[Main] Автооткрытие не требуется");
        }

    } catch (error) {
        console.error("[Main] Ошибка при проверке автооткрытия:", error);
        // Не критичная ошибка, продолжаем работу
    }
}

/**
 * Определяет, нужно ли автоматически открывать чат
 * @returns {Promise<boolean>} true если нужно автооткрытие
 */
async function checkIfShouldAutoOpen() {
    try {
        // 1. Проверяем localStorage - если новый пользователь, то автооткрытие
        const hasVisited = localStorage.getItem('chatUserVisited');

        if (!hasVisited) {
            console.log("[Main] Новый пользователь (первое посещение)");
            localStorage.setItem('chatUserVisited', 'true');
            return true;
        }

        console.log("[Main] Пользователь уже посещал сайт, проверяем наличие сообщений...");

        // 2. Проверяем, есть ли у пользователя сообщения в базе
        const { getUserSessions, getMessages } = await import('./api.js');

        // Получаем сессии пользователя
        const sessionsResponse = await getUserSessions();

        if (!sessionsResponse || !sessionsResponse.sessions || sessionsResponse.sessions.length === 0) {
            console.log("[Main] У пользователя нет сессий - автооткрытие");
            return true;
        }

        console.log(`[Main] Найдено сессий: ${sessionsResponse.sessions.length}, проверяем сообщения...`);

        // 3. Проверяем сообщения во всех сессиях
        let totalUserMessages = 0;

        for (const session of sessionsResponse.sessions) {
            try {
                const messagesResponse = await getMessages(session.id);

                if (messagesResponse && messagesResponse.messages && messagesResponse.messages.length > 0) {
                    // Считаем только реальные сообщения пользователя (не превью, не временные)
                    const userMessages = messagesResponse.messages.filter(msg =>
                        msg.role === 'user' &&
                        !msg.id.toString().startsWith('preview-') &&
                        !msg.id.toString().startsWith('temp_') &&
                        msg.content && msg.content.trim().length > 0
                    );

                    totalUserMessages += userMessages.length;
                    console.log(`[Main] Сессия ${session.id}: ${userMessages.length} сообщений пользователя`);
                }
            } catch (error) {
                console.warn(`[Main] Ошибка при проверке сессии ${session.id}:`, error);
            }
        }

        console.log(`[Main] Всего сообщений пользователя: ${totalUserMessages}`);

        // 4. Если нет сообщений - автооткрытие
        if (totalUserMessages === 0) {
            console.log("[Main] У пользователя нет сообщений - автооткрытие");
            return true;
        } else {
            console.log("[Main] У пользователя есть сообщения - автооткрытие не нужно");
            return false;
        }

    } catch (error) {
        console.error("[Main] Ошибка при проверке необходимости автооткрытия:", error);
        // В случае ошибки не открываем автоматически
        return false;
    }
}

/**
 * Устанавливает правильные пути к изображениям в CSS
 * @param {string} chatAdminBaseUrl - базовый URL для ресурсов чата
 */
function setImagePaths(chatAdminBaseUrl) {
    try {
        // Гарантируем слеш в конце
        const baseUrl = chatAdminBaseUrl.endsWith('/') ? chatAdminBaseUrl : chatAdminBaseUrl + '/';

        // Находим элементы с иконками и устанавливаем правильные пути
        const style = document.createElement('style');
        style.id = 'znak-dynamic-image-paths';

        // Удаляем предыдущие динамические стили, если есть
        const existingStyle = document.getElementById('znak-dynamic-image-paths');
        if (existingStyle) {
            existingStyle.remove();
        }

        // CSS с динамическими путями к изображениям
        style.textContent = `
            .czn-chat-action-icon {
                mask-image: url("${baseUrl}images/micro.svg") !important;
            }

            .czn-chat-action-control.czn-chat-interrupt .czn-chat-action-icon {
                mask-image: url("${baseUrl}images/stop-speach.svg") !important;
            }
        `;

        document.head.appendChild(style);
        console.log(`[Main] Динамические пути к изображениям установлены с базовым URL: ${baseUrl}`);

    } catch (error) {
        console.error('[Main] Ошибка при установке путей к изображениям:', error);
    }
}

// Экспортируем функцию инициализации и функцию установки аватара для использования в chat-loader.js
export { initializeChatModules, setVoiceCallAvatar };

// --- END OF FILE main.js ---