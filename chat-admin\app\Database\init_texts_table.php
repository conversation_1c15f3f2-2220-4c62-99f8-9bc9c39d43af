<?php
/**
 * Скрипт для инициализации таблицы texts и добавления начальных данных.
 * Запускается из админки при необходимости.
 */

require_once __DIR__ . '/../Models/Database.php';

// Получаем соединение с базой данных
$db = \App\Models\Database::getInstance();

// Проверяем, существует ли таблица texts
$tableExists = false;
$result = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='texts'");
if ($result) {
    $row = $result->fetchArray();
    $tableExists = !empty($row);
}

// Если таблица не существует, создаем ее и добавляем начальные данные
if (!$tableExists) {
    // Читаем SQL-скрипт
    $sql = file_get_contents(__DIR__ . '/create_texts_table.sql');

    // Выполняем SQL-скрипт
    $result = $db->exec($sql);

    if ($result !== false) {
        echo "Таблица texts успешно создана и заполнена начальными данными.<br>";
    } else {
        echo "Ошибка при создании таблицы texts: " . $db->lastErrorMsg() . "<br>";
    }
} else {
    // Проверяем, есть ли данные в таблице
    $result = $db->query("SELECT COUNT(*) as count FROM texts");
    $row = $result->fetchArray();
    $count = $row['count'] ?? 0;

    if ($count == 0) {
        // Если таблица пуста, добавляем начальные данные
        $sql = file_get_contents(__DIR__ . '/create_texts_table.sql');
        // Извлекаем только INSERT-запросы
        if (preg_match_all('/INSERT OR IGNORE INTO texts.*?;/s', $sql, $matches)) {
            $insertSql = implode("\n", $matches[0]);
            $result = $db->exec($insertSql);

            if ($result !== false) {
                echo "Таблица texts успешно заполнена начальными данными.<br>";
            } else {
                echo "Ошибка при заполнении таблицы texts: " . $db->lastErrorMsg() . "<br>";
            }
        }
    } else {
        echo "Таблица texts уже существует и содержит данные.<br>";
    }
}

// Проверяем, что все необходимые ключи присутствуют
$requiredKeys = [
    // Основные тексты интерфейса
    'call_ai_button', 'assistant_hint', 'chat_title', 'new_chat_title', 'close_chat_title',
    'mic_hint', 'mic_active_text', 'message_placeholder', 'voice_input_title', 'send_button_title',
    'modal_title', 'refresh_title', 'open_chat_title', 'close_modal_title', 'connection_status',
    'end_call_title', 'end_call_text', 'voice_session_title',

    // Уведомления
    'notification_copied', 'notification_chat_title_updated', 'notification_edited',
    'notification_new_chat_created',

    // Сообщения в чате
    'chat_deleted_message', 'no_chats_message', 'initial_greeting', 'empty_chat_message',
    'no_assistant_response',

    // Алерты и ошибки
    'error_speech_not_supported', 'error_mic_activation', 'error_recognition',
    'error_chat_title_empty', 'error_update_chat_title', 'error_delete_chat',
    'error_init', 'error_copy_failed', 'error_message_empty',

    // Другие тексты
    'interrupt_response', 'confirm_delete_chat'
];

$missingKeys = [];
foreach ($requiredKeys as $key) {
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM texts WHERE key = :key AND language = 'ru'");
    $stmt->bindValue(':key', $key, SQLITE3_TEXT);
    $result = $stmt->execute();
    $row = $result->fetchArray();
    $count = $row['count'] ?? 0;

    if ($count == 0) {
        $missingKeys[] = $key;
    }
}

// Если есть отсутствующие ключи, добавляем их
if (!empty($missingKeys)) {
    echo "Добавление отсутствующих ключей: " . implode(', ', $missingKeys) . "<br>";

    // Значения по умолчанию для отсутствующих ключей
    $defaultValues = [
        // Основные тексты интерфейса
        'call_ai_button' => 'Звонок AI',
        'assistant_hint' => 'AI ассистент',
        'chat_title' => 'AI Ассистент',
        'new_chat_title' => 'Новый чат',
        'close_chat_title' => 'Закрыть чат',
        'mic_hint' => 'Говорите... Нажмите снова для остановки.',
        'mic_active_text' => 'Говорите',
        'message_placeholder' => 'Введите сообщение...',
        'voice_input_title' => 'Голосовой ввод',
        'send_button_title' => 'Отправить',
        'modal_title' => 'AI ассистент Дарья',
        'refresh_title' => 'Обновить подключение',
        'open_chat_title' => 'Открыть текстовый чат',
        'close_modal_title' => 'Закрыть',
        'connection_status' => 'Подключение...',
        'end_call_title' => 'Завершить разговор',
        'end_call_text' => 'Завершить',
        'voice_session_title' => 'Голосовой чат',

        // Уведомления
        'notification_copied' => 'Скопировано!',
        'notification_chat_title_updated' => 'Название чата обновлено!',
        'notification_edited' => 'Отредактировано!',
        'notification_new_chat_created' => 'Новый чат создан!',

        // Сообщения в чате
        'chat_deleted_message' => 'Чат удален. Выберите или создайте новый.',
        'no_chats_message' => 'Создайте новый чат!',
        'initial_greeting' => 'Привет! Я ваш AI-ассистент. Чем могу помочь?',
        'empty_chat_message' => 'Чат пуст. Начните общение!',
        'no_assistant_response' => '(Нет ответа от ассистента)',

        // Алерты и ошибки
        'error_speech_not_supported' => 'Распознавание речи не поддерживается вашим браузером.',
        'error_mic_activation' => 'Не удалось активировать микрофон: ',
        'error_recognition' => 'Ошибка распознавания',
        'error_chat_title_empty' => 'Название чата не может быть пустым.',
        'error_update_chat_title' => 'Не удалось обновить название чата: ',
        'error_delete_chat' => 'Ошибка удаления чата: ',
        'error_init' => 'Произошла ошибка при инициализации интерфейса ассистента. Пожалуйста, обновите страницу или обратитесь в поддержку.',
        'error_copy_failed' => 'Не удалось скопировать текст.',
        'error_message_empty' => 'Сообщение не может быть пустым.',

        // Другие тексты
        'interrupt_response' => 'Прервать ответ',
        'confirm_delete_chat' => 'Вы уверены, что хотите удалить этот чат? Вся история будет потеряна.'
    ];

    foreach ($missingKeys as $key) {
        $value = $defaultValues[$key] ?? $key;
        $stmt = $db->prepare("INSERT INTO texts (key, language, text) VALUES (:key, 'ru', :text)");
        $stmt->bindValue(':key', $key, SQLITE3_TEXT);
        $stmt->bindValue(':text', $value, SQLITE3_TEXT);
        $result = $stmt->execute();

        if ($result !== false) {
            echo "Добавлен ключ: $key<br>";
        } else {
            echo "Ошибка при добавлении ключа $key: " . $db->lastErrorMsg() . "<br>";
        }
    }
}

// Генерируем JS-файл с текстами
$stmt = $db->prepare("SELECT key, text FROM texts WHERE language = 'ru'");
$result = $stmt->execute();

$texts = [];
if ($result) {
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $texts[$row['key']] = $row['text'];
    }
}

if (!empty($texts)) {
    // Формируем содержимое JS-файла
    $jsContent = "/**\n";
    $jsContent .= " * Автоматически сгенерированный файл с текстами для языка: ru\n";
    $jsContent .= " * Дата генерации: " . date('Y-m-d H:i:s') . "\n";
    $jsContent .= " */\n\n";
    $jsContent .= "export const texts = " . json_encode($texts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . ";\n";

    // Путь к файлу
    $filePath = __DIR__ . '/../../chat-js/texts_ru.js';

    // Сохраняем файл
    $result = file_put_contents($filePath, $jsContent);

    if ($result !== false) {
        echo "JS-файл с текстами успешно сгенерирован: $filePath<br>";
    } else {
        echo "Ошибка при генерации JS-файла с текстами: $filePath<br>";
    }
}

echo "Инициализация таблицы texts завершена.<br>";
