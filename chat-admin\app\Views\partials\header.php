<?php
// Ожидаем, что переменная $user будет передана из контроллера
if (!isset($user) || !is_array($user)) {
    // Устанавливаем значения по умолчанию или выбрасываем ошибку, если пользователь не передан
    $user = ['username' => 'Unknown User', 'email' => ''];
    // Можно добавить логирование ошибки
    error_log("User data not provided to header partial.");
}

// Ожидаем переменную $pageName из контроллера для определения активной страницы
$currentPage = $pageName ?? basename($_SERVER['SCRIPT_NAME']); // Используем переданное имя или старый метод как fallback

// Определяем BASE_URL для подключения статики с учетом подпапки
$scheme = (!empty($_SERVER['REQUEST_SCHEME']) ? $_SERVER['REQUEST_SCHEME'] : (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' ? 'https' : 'http'));
$host = $_SERVER['HTTP_HOST'];
$scriptName = $_SERVER['SCRIPT_NAME'];
// Получаем путь до chat-admin (или другой подпапки)
$adminPath = str_replace('\\', '/', dirname(dirname($scriptName))); // /chat-admin
if ($adminPath === '/' || $adminPath === '\\' || $adminPath === '.') $adminPath = '';
$baseUrl = $scheme . '://' . $host . $adminPath;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- TODO: Сделать title динамическим -->
    <title>Админ-панель</title> <!-- Можно сделать title динамическим -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" crossorigin="anonymous">
    <!-- Резервный CDN для Font Awesome -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.4.0/css/all.css" crossorigin="anonymous">
    <!-- Обновленные пути к CSS файлам админки (относительно корня сайта) -->
    <link rel="stylesheet" href="<?= $baseUrl ?>/chat-admin/public/admin-css/admin.css">
    <!-- Общие стили -->
    <!-- Стили теперь подключаются через admin.css -->
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-robot" style="color: var(--primary); font-size: 1.5rem;"></i>
            <span class="sidebar-title">Админ-панель</span>
        </div>

        <nav class="nav-menu">
            <!-- Обновленные ссылки для роутинга -->
            <a href="index.php?page=dashboard" class="nav-link <?= ($currentPage == 'dashboard' ? 'active' : '') ?>">
                <i class="fas fa-tachometer-alt"></i>
                <span>Главная</span>
            </a>
            <a href="index.php?page=settings" class="nav-link <?= ($currentPage == 'settings' ? 'active' : '') ?>">
                <i class="fas fa-cog"></i>
                <span>Настройки</span>
            </a>
            <a href="index.php?page=crm" class="nav-link <?= ($currentPage == 'crm' ? 'active' : '') ?>">
                <i class="fas fa-handshake"></i>
                <span>Интеграция с CRM</span>
            </a>
            <a href="index.php?page=messages" class="nav-link <?= ($currentPage == 'messages' ? 'active' : '') ?>">
                <i class="fas fa-comments"></i>
                <span>Сообщения</span>
            </a>
            <a href="index.php?page=phone_chats" class="nav-link <?= ($currentPage == 'phone_chats' ? 'active' : '') ?>">
                <i class="fas fa-phone-alt"></i>
                <span>Переписки с телефонами</span>
            </a>
            <a href="index.php?page=profile" class="nav-link <?= ($currentPage == 'profile' ? 'active' : '') ?>">
                <i class="fas fa-user"></i>
                <span>Профиль</span>
            </a>
             <!-- TODO: Добавить другие ссылки, если нужно -->
        </nav>

        <div class="user-profile">
            <div class="user-name"><?= htmlspecialchars($user['username'] ?? 'Admin') ?></div>
            <div class="user-email"><?= htmlspecialchars($user['email'] ?? '') ?></div>
            <!-- Обновленный action для выхода -->
            <form action="index.php?page=logout" method="POST">
                <button type="submit" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Выйти
                </button>
            </form>
        </div>
    </div>

    <div class="main-content">
        <!-- Основной контент страницы будет здесь -->
