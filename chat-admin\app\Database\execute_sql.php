<?php
// Подключаем необходимые файлы
require_once __DIR__ . '/../Models/Database.php';

use App\Models\Database;

// Получаем соединение с базой данных
$db = Database::getInstance();

// Проверяем, что соединение установлено
if (!$db) {
    echo "Ошибка: не удалось установить соединение с базой данных.<br>";
    exit;
}

// Путь к SQL-файлу
$sqlFilePath = __DIR__ . '/../../db/add_missing_texts.sql';

// Проверяем, что файл существует
if (!file_exists($sqlFilePath)) {
    echo "Ошибка: файл $sqlFilePath не найден.<br>";
    exit;
}

// Читаем содержимое файла
$sqlContent = file_get_contents($sqlFilePath);

// Разделяем SQL-запросы
$queries = explode(';', $sqlContent);

// Выполняем каждый запрос
$successCount = 0;
$errorCount = 0;

foreach ($queries as $query) {
    $query = trim($query);
    if (empty($query)) {
        continue;
    }

    // Выполняем запрос
    $result = $db->exec($query);

    if ($result !== false) {
        $successCount++;
    } else {
        $errorCount++;
        echo "Ошибка при выполнении запроса: " . $db->lastErrorMsg() . "<br>";
        echo "Запрос: " . htmlspecialchars($query) . "<br><br>";
    }
}

echo "Выполнено запросов: $successCount<br>";
echo "Ошибок: $errorCount<br>";

// Генерируем JS-файл с текстами
$stmt = $db->prepare("SELECT key, text FROM texts WHERE language = 'ru'");
$result = $stmt->execute();

$texts = [];
if ($result) {
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $texts[$row['key']] = $row['text'];
    }
}

if (!empty($texts)) {
    // Формируем содержимое JS-файла
    $jsContent = "/**\n";
    $jsContent .= " * Автоматически сгенерированный файл с текстами для языка: ru\n";
    $jsContent .= " * Дата генерации: " . date('Y-m-d H:i:s') . "\n";
    $jsContent .= " */\n\n";
    $jsContent .= "export const texts = " . json_encode($texts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . ";\n";
    
    // Путь к файлу
    $filePath = __DIR__ . '/../../chat-js/texts_ru.js';
    
    // Сохраняем файл
    $result = file_put_contents($filePath, $jsContent);
    
    if ($result !== false) {
        echo "JS-файл с текстами успешно сгенерирован: $filePath<br>";
    } else {
        echo "Ошибка при генерации JS-файла с текстами: $filePath<br>";
    }
}

echo "Выполнение SQL-запросов завершено.<br>";
