<?php
// Предполагается, что здесь будут переданы данные о текстах из контроллера
// $texts = $data['texts'] ?? [];
?>

<div class="section-header">
    <h2><i class="fas fa-language"></i> Управление текстами</h2>
    <p>Здесь вы можете просматривать и редактировать все текстовые строки, используемые в чате и голосовом ассистенте.</p>
</div>

<!-- Настройки превью-сообщений -->
<div class="preview-settings-section">
    <h3><i class="fas fa-cog"></i> Настройки превью-сообщений</h3>
    <div class="settings-grid">
        <div class="setting-item">
            <label for="preview-enabled">
                <input type="checkbox" id="preview-enabled" checked>
                Включить превью-сообщения
            </label>
            <small>Показывать превью-сообщения при первом входе в чат</small>
        </div>
        <div class="setting-item">
            <label for="preview-delay">Задержка между сообщениями (мс):</label>
            <input type="number" id="preview-delay" value="3000" min="1000" max="10000" step="500">
            <small>Время между появлением превью-сообщений (1000-10000 мс)</small>
        </div>
        <div class="setting-item">
            <label for="hide-greeting">
                <input type="checkbox" id="hide-greeting" checked>
                Скрывать стандартное приветствие
            </label>
            <small>Если включено, стандартное приветствие не показывается при включенных превью</small>
        </div>
        <div class="setting-item">
            <label for="auto-open-chat">
                <input type="checkbox" id="auto-open-chat" checked>
                Автоматически открывать чат для новых пользователей
            </label>
            <small>Чат будет открываться автоматически только для новых пользователей или тех, кто ранее не отправлял сообщения</small>
        </div>
        <div class="setting-item">
            <label for="auto-open-delay">Задержка автооткрытия чата (мс):</label>
            <input type="number" id="auto-open-delay" value="0" min="0" max="30000" step="500">
            <small>Время до автоматического открытия чата (0 = сразу, максимум 30 секунд)</small>
        </div>
        <div class="setting-item">
            <label for="notification-email">Email для уведомлений о телефонах:</label>
            <input type="email" id="notification-email" placeholder="<EMAIL>">
            <small>На этот email будут отправляться уведомления когда пользователь указывает телефон в чате</small>
        </div>
        <div class="setting-item">
            <label for="email-from">От кого отправлять письма:</label>
            <input type="email" id="email-from" placeholder="<EMAIL>">
            <small>Email адрес отправителя (From)</small>
        </div>
        <div class="setting-item">
            <label for="email-subject">Тема письма:</label>
            <input type="text" id="email-subject" placeholder="Новый телефон из чата: {phone}">
            <small>Тема письма. Используйте {phone} для подстановки номера телефона</small>
        </div>
        <div class="setting-item">
            <label for="email-body">Текст письма:</label>
            <textarea id="email-body" rows="4" placeholder="Пользователь указал телефон в чате.&#10;&#10;Телефон: {phone}&#10;ID чата: {session_id}&#10;Время: {datetime}"></textarea>
            <small>Основной текст письма. Доступные переменные: {phone}, {session_id}, {datetime}, {email}</small>
        </div>
    </div>

    <!-- Настройки отправки email -->
    <h3><i class="fas fa-envelope"></i> Настройки отправки email</h3>

    <!-- SMTP Configuration Block -->
    <div class="smtp-config-block" style="margin-bottom: 2rem;">
        <div class="config-header">
            <div class="service-icon">
                <i class="fas fa-server"></i>
            </div>
            <div class="service-info">
                <h4>SMTP сервер</h4>
                <span class="service-description">Настройка SMTP для отправки email уведомлений</span>
            </div>
        </div>

        <div class="smtp-config-form">
            <div class="config-grid">
                <div class="config-item">
                    <label for="smtp-host">SMTP сервер:</label>
                    <input type="text" id="smtp-host" class="form-control" placeholder="smtp.gmail.com">
                    <small>Адрес SMTP сервера</small>
                </div>
                <div class="config-item">
                    <label for="smtp-port">Порт:</label>
                    <input type="number" id="smtp-port" class="form-control" placeholder="587" min="1" max="65535">
                    <small>587 (TLS) или 465 (SSL)</small>
                </div>
                <div class="config-item">
                    <label for="smtp-username">Логин:</label>
                    <input type="text" id="smtp-username" class="form-control" placeholder="<EMAIL>">
                    <small>Email для аутентификации</small>
                </div>
                <div class="config-item">
                    <label for="smtp-password">Пароль:</label>
                    <div style="position: relative;">
                        <input type="password" id="smtp-password" class="form-control" placeholder="••••••••" style="padding-right: 40px;">
                        <button type="button" id="toggle-smtp-password" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; font-size: 16px;" title="Показать/скрыть пароль">👁️</button>
                    </div>
                    <small>Пароль или пароль приложения</small>
                </div>
                <div class="config-item">
                    <label for="smtp-encryption">Шифрование:</label>
                    <select id="smtp-encryption" class="form-control">
                        <option value="tls">TLS (рекомендуется)</option>
                        <option value="ssl">SSL</option>
                        <option value="none">Без шифрования</option>
                    </select>
                    <small>Тип шифрования соединения</small>
                </div>
                <div class="config-item">
                    <div class="form-check">
                        <input type="checkbox" id="smtp-enabled" class="form-check-input">
                        <label for="smtp-enabled" class="form-label">Включить SMTP</label>
                    </div>
                    <small>Использовать SMTP для отправки email</small>
                </div>
            </div>
        </div>
    </div>


    <button id="save-preview-settings" class="btn btn-primary">
        <i class="fas fa-save"></i> Сохранить настройки превью и email
    </button>
</div>

<div class="search-container">
    <div class="search-box">
        <i class="fas fa-search search-icon"></i>
        <input type="text" id="search-texts" class="form-control" placeholder="Поиск по ключу или тексту...">
    </div>
</div>

<div id="texts-list">
    <!-- Сюда будет загружен список текстов через JavaScript -->
    <div class="loading-container">
        <div class="loading-spinner"></div>
        <p>Загрузка текстов...</p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const textsListDiv = document.getElementById('texts-list');
    const searchInput = document.getElementById('search-texts');
    let allTexts = []; // Сохраняем все тексты для фильтрации

    // Функция для показа уведомлений
    function showNotification(type, message) {
        // Создаем элемент уведомления
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        // Добавляем иконку в зависимости от типа
        let icon = '';
        if (type === 'success') {
            icon = '<i class="fas fa-check-circle"></i>';
        } else if (type === 'error') {
            icon = '<i class="fas fa-exclamation-circle"></i>';
        } else if (type === 'info') {
            icon = '<i class="fas fa-info-circle"></i>';
        }

        // Устанавливаем содержимое
        notification.innerHTML = `${icon} <span>${message}</span>`;

        // Добавляем на страницу
        document.body.appendChild(notification);

        // Анимация появления
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Удаляем через 3 секунды
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Функция для загрузки текстов с сервера
    async function loadTexts() {
        textsListDiv.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <p>Загрузка текстов...</p>
            </div>
        `;

        try {
            const response = await fetch('index.php?page=api&action=getTexts'); // Используем API эндпоинт
            const data = await response.json();

            if (data.status === 'success') {
                allTexts = data.texts; // Сохраняем все тексты
                displayTexts(data.texts);
            } else {
                textsListDiv.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        Ошибка загрузки текстов: ${data.message || 'Неизвестная ошибка'}
                    </div>
                `;
                console.error('Ошибка загрузки текстов:', data.message);
            }
        } catch (error) {
            textsListDiv.innerHTML = `
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    Ошибка сети при загрузке текстов.
                </div>
            `;
            console.error('Ошибка сети:', error);
        }
    }

    // Функция для отображения текстов
    function displayTexts(texts) {
        if (texts.length === 0) {
            textsListDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Текстовые строки не найдены в базе данных.
                </div>
            `;
            return;
        }

        // Группируем тексты по категориям
        const categories = {
            'Основные тексты интерфейса': ['call_ai_button', 'assistant_hint', 'chat_title', 'new_chat_title', 'close_chat_title', 'mic_hint', 'mic_active_text', 'message_placeholder', 'voice_input_title', 'send_button_title', 'modal_title', 'refresh_title', 'open_chat_title', 'close_modal_title', 'connection_status', 'end_call_title', 'end_call_text', 'voice_session_title'],
            'Уведомления': ['notification_copied', 'notification_chat_title_updated', 'notification_edited', 'notification_new_chat_created'],
            'Сообщения в чате': ['chat_deleted_message', 'no_chats_message', 'initial_greeting', 'empty_chat_message', 'no_assistant_response'],
            'Алерты и ошибки': ['error_speech_not_supported', 'error_mic_activation', 'error_recognition', 'error_chat_title_empty', 'error_update_chat_title', 'error_delete_chat', 'error_init', 'error_copy_failed', 'error_message_empty'],
            'Другие тексты': ['interrupt_response', 'confirm_delete_chat'],
            'Превью беседы': ['preview_message_1', 'preview_message_2']
        };

        // Создаем HTML для каждой категории
        let html = '';
        
        for (const [categoryName, keys] of Object.entries(categories)) {
            const categoryTexts = texts.filter(text => keys.includes(text.text_key));
            if (categoryTexts.length === 0) continue;

            html += `<div class="category-section">`;
            html += `<h3 class="category-header"><i class="fas ${categoryName === 'Превью беседы' ? 'fa-comment-alt' : 'fa-list'}"></i> ${categoryName}</h3>`;
            html += '<table class="admin-table">';
            html += '<thead><tr><th>Ключ</th><th>Текст</th><th>Действия</th></tr></thead>';
            html += '<tbody>';

            categoryTexts.forEach(text => {
                html += `
                    <tr data-id="${text.id}">
                        <td>${escapeHTML(text.text_key)}</td>
                        <td>
                            <span class="text-content">${escapeHTML(text.text_value)}</span>
                            <textarea class="text-edit-input" style="display: none;">${escapeHTML(text.text_value)}</textarea>
                        </td>
                        <td>
                            <button class="edit-text-btn btn btn-primary btn-sm"><i class="fas fa-pencil-alt"></i> Редактировать</button>
                            <button class="save-text-btn btn btn-success btn-sm" style="display: none;"><i class="fas fa-check"></i> Сохранить</button>
                            <button class="cancel-edit-btn btn btn-secondary btn-sm" style="display: none;"><i class="fas fa-times"></i> Отмена</button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            html += `</div>`;
        }

        textsListDiv.innerHTML = html;

        // Добавляем обработчики событий для кнопок
        textsListDiv.querySelectorAll('.edit-text-btn').forEach(button => {
            button.addEventListener('click', handleEditClick);
        });
        textsListDiv.querySelectorAll('.save-text-btn').forEach(button => {
            button.addEventListener('click', handleSaveClick);
        });
        textsListDiv.querySelectorAll('.cancel-edit-btn').forEach(button => {
            button.addEventListener('click', handleCancelClick);
        });
    }

    // Вспомогательная функция для экранирования HTML
    function escapeHTML(str) {
        const div = document.createElement('div');
        div.appendChild(document.createTextNode(str));
        return div.innerHTML;
    }

    // Обработчик клика по кнопке "Редактировать"
    function handleEditClick(event) {
        const row = event.target.closest('tr');
        row.querySelector('.text-content').style.display = 'none';
        row.querySelector('.text-edit-input').style.display = 'block';
        row.querySelector('.edit-text-btn').style.display = 'none';
        row.querySelector('.save-text-btn').style.display = 'inline-block';
        row.querySelector('.cancel-edit-btn').style.display = 'inline-block';
    }

    // Обработчик клика по кнопке "Отмена"
    function handleCancelClick(event) {
        const row = event.target.closest('tr');
        const span = row.querySelector('.text-content');
        const textarea = row.querySelector('.text-edit-input');

        // Восстанавливаем исходный текст в textarea
        textarea.value = span.textContent; // Используем textContent для получения неэкранированного текста

        span.style.display = 'inline';
        textarea.style.display = 'none';
        row.querySelector('.edit-text-btn').style.display = 'inline-block';
        row.querySelector('.save-text-btn').style.display = 'none';
        row.querySelector('.cancel-edit-btn').style.display = 'none';
    }


    // Обработчик клика по кнопке "Сохранить"
    async function handleSaveClick(event) {
        const row = event.target.closest('tr');
        const textId = row.dataset.id;
        const newContent = row.querySelector('.text-edit-input').value;

        // Простая валидация: текст не должен быть пустым
        if (newContent.trim() === '') {
            showNotification('error', 'Текст не может быть пустым.');
            return;
        }

        try {
            const response = await fetch('index.php?page=api&action=updateText', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: textId, content: newContent }),
            });
            const data = await response.json();

            if (data.status === 'success') {
                console.log('Текст успешно обновлен');
                // Обновить отображаемый текст в span
                row.querySelector('.text-content').textContent = newContent;
                // Переключить обратно в режим просмотра
                row.querySelector('.text-content').style.display = 'inline';
                row.querySelector('.text-edit-input').style.display = 'none';
                row.querySelector('.edit-text-btn').style.display = 'inline-block';
                row.querySelector('.save-text-btn').style.display = 'none';
                row.querySelector('.cancel-edit-btn').style.display = 'none';

                // Показываем уведомление об успехе
                showNotification('success', 'Текст успешно сохранен!');

                // Подсвечиваем строку
                row.classList.add('row-updated');
                setTimeout(() => {
                    row.classList.remove('row-updated');
                }, 2000);
            } else {
                console.error('Ошибка при обновлении текста:', data.message);
                showNotification('error', 'Ошибка при обновлении текста: ' + (data.message || 'Неизвестная ошибка'));
                // Остаться в режиме редактирования, чтобы пользователь мог исправить
            }
        } catch (error) {
            console.error('Ошибка сети при обновлении текста:', error);
            showNotification('error', 'Ошибка сети при обновлении текста.');
            // Остаться в режиме редактирования
        }
    }


    // Функция для фильтрации текстов
    function filterTexts(searchTerm) {
        searchTerm = searchTerm.toLowerCase().trim();

        if (!searchTerm) {
            displayTexts(allTexts);
            return;
        }

        const filteredTexts = allTexts.filter(text => {
            const key = text.text_key.toLowerCase();
            const value = text.text_value.toLowerCase();

            return key.includes(searchTerm) || value.includes(searchTerm);
        });

        if (filteredTexts.length === 0) {
            textsListDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-search"></i>
                    По запросу "${escapeHTML(searchTerm)}" ничего не найдено.
                </div>
            `;
        } else {
            displayTexts(filteredTexts);
        }
    }

    // Обработчик события ввода в поле поиска
    searchInput.addEventListener('input', function() {
        filterTexts(this.value);
    });

    // Функции для работы с настройками превью
    async function loadPreviewSettings() {
        try {
            const response = await fetch('index.php?page=api&action=getAdminSettings');
            const data = await response.json();

            if (data.status === 'success') {
                const settings = data.settings;
                document.getElementById('preview-enabled').checked = settings.preview_messages_enabled == 1;
                document.getElementById('preview-delay').value = settings.preview_message_delay || 3000;
                document.getElementById('hide-greeting').checked = settings.preview_hide_greeting == 1;
                document.getElementById('auto-open-chat').checked = settings.auto_open_chat == 1;
                document.getElementById('auto-open-delay').value = settings.auto_open_delay || 0;
                document.getElementById('notification-email').value = settings.notification_email || '';
                document.getElementById('email-from').value = settings.email_from || '<EMAIL>';
                document.getElementById('email-subject').value = settings.email_subject || 'Новый телефон из чата: {phone}';
                document.getElementById('email-body').value = settings.email_body || 'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}';



                // SMTP настройки
                document.getElementById('smtp-enabled').checked = settings.smtp_enabled == 1;
                document.getElementById('smtp-host').value = settings.smtp_host || '';
                document.getElementById('smtp-port').value = settings.smtp_port || 587;
                document.getElementById('smtp-username').value = settings.smtp_username || '';
                document.getElementById('smtp-password').value = settings.smtp_password || '';
                document.getElementById('smtp-encryption').value = settings.smtp_encryption || 'tls';
            }
        } catch (error) {
            console.error('Ошибка загрузки настроек превью:', error);
        }
    }

    async function savePreviewSettings() {
        const enabled = document.getElementById('preview-enabled').checked ? 1 : 0;
        const delay = parseInt(document.getElementById('preview-delay').value);
        const hideGreeting = document.getElementById('hide-greeting').checked ? 1 : 0;
        const autoOpenChat = document.getElementById('auto-open-chat').checked ? 1 : 0;
        const autoOpenDelay = parseInt(document.getElementById('auto-open-delay').value) || 0;
        const email = document.getElementById('notification-email').value.trim();
        const emailFrom = document.getElementById('email-from').value.trim();
        const emailSubject = document.getElementById('email-subject').value.trim();
        const emailBody = document.getElementById('email-body').value.trim();



        // SMTP настройки
        const smtpEnabledElement = document.getElementById('smtp-enabled');
        const smtpHostElement = document.getElementById('smtp-host');
        const smtpPortElement = document.getElementById('smtp-port');
        const smtpUsernameElement = document.getElementById('smtp-username');
        const smtpPasswordElement = document.getElementById('smtp-password');
        const smtpEncryptionElement = document.getElementById('smtp-encryption');

        const smtpEnabled = smtpEnabledElement ? (smtpEnabledElement.checked ? 1 : 0) : 0;
        const smtpHost = smtpHostElement ? smtpHostElement.value.trim() : '';
        const smtpPort = smtpPortElement ? (parseInt(smtpPortElement.value) || 587) : 587;
        const smtpUsername = smtpUsernameElement ? smtpUsernameElement.value.trim() : '';
        const smtpPassword = smtpPasswordElement ? smtpPasswordElement.value.trim() : '';
        const smtpEncryption = smtpEncryptionElement ? smtpEncryptionElement.value : 'tls';

        // Отладочная информация
        console.log('SMTP Settings:', {
            smtpEnabled: smtpEnabled,
            smtpHost: smtpHost,
            smtpPort: smtpPort,
            smtpUsername: smtpUsername
        });

        try {
            console.log('Отправляем настройки:', {
                preview_messages_enabled: enabled,
                preview_message_delay: delay,
                preview_hide_greeting: hideGreeting,
                auto_open_chat: autoOpenChat,
                auto_open_delay: autoOpenDelay,
                notification_email: email,
                email_from: emailFrom,
                email_subject: emailSubject,
                email_body: emailBody
            });

            const response = await fetch('index.php?page=api&action=updateSettings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    preview_messages_enabled: enabled,
                    preview_message_delay: delay,
                    preview_hide_greeting: hideGreeting,
                    auto_open_chat: autoOpenChat,
                    auto_open_delay: autoOpenDelay,
                    notification_email: email,
                    email_from: emailFrom,
                    email_subject: emailSubject,
                    email_body: emailBody,
                    // SMTP настройки
                    smtp_enabled: smtpEnabled,
                    smtp_host: smtpHost,
                    smtp_port: smtpPort,
                    smtp_username: smtpUsername,
                    smtp_password: smtpPassword,
                    smtp_encryption: smtpEncryption
                }),
            });
            const data = await response.json();

            if (data.status === 'success') {
                showNotification('success', 'Настройки превью сохранены!');
            } else {
                showNotification('error', 'Ошибка сохранения настроек: ' + (data.message || 'Неизвестная ошибка'));
            }
        } catch (error) {
            console.error('Ошибка сохранения настроек превью:', error);
            showNotification('error', 'Ошибка сети при сохранении настроек.');
        }
    }



    // Обработчик для кнопки сохранения настроек превью
    document.getElementById('save-preview-settings').addEventListener('click', savePreviewSettings);

    // Загружаем тексты и настройки при загрузке страницы
    loadTexts();
    loadPreviewSettings();
});
</script>

<style>
.section-header {
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border);
    padding-bottom: 1rem;
}

.section-header h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.section-header p {
    color: var(--text-light);
    font-size: 1rem;
    max-width: 800px;
}

.section-header i {
    color: var(--primary);
}

/* Стили для настроек превью */
.preview-settings-section {
    background: #f8f9fa;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.preview-settings-section h3 {
    color: var(--primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.setting-item {
    background: white;
    padding: 1rem;
    border-radius: var(--radius);
    border: 1px solid var(--border);
}

.setting-item label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text);
}

.setting-item input[type="checkbox"] {
    margin-right: 0.5rem;
}

.setting-item input[type="number"],
.setting-item input[type="email"],
.setting-item input[type="text"],
.setting-item textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border);
    border-radius: 4px;
    margin-top: 0.5rem;
    font-family: inherit;
}

.setting-item textarea {
    resize: vertical;
    min-height: 80px;
}

.setting-item small {
    display: block;
    color: var(--text-light);
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.search-container {
    margin-bottom: 1.5rem;
}

/* Email Service Selector Styles */
.email-service-selector {
    margin-bottom: 2rem;
}

.service-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.service-option {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    transition: all 0.3s ease;
}



.smtp-option::before {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.service-option:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.service-option.active {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(91, 127, 255, 0.1);
}

.service-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.service-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}



.smtp-option .service-icon {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.service-info {
    flex: 1;
}

.service-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text);
}

.service-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-badge.recommended {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.service-badge.backup {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.service-toggle {
    position: relative;
}

.service-toggle input[type="radio"] {
    display: none;
}

.toggle-label {
    width: 50px;
    height: 26px;
    background: #ddd;
    border-radius: 13px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
}

.toggle-label::after {
    content: '';
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.service-toggle input[type="radio"]:checked + .toggle-label {
    background: var(--primary);
}

.service-toggle input[type="radio"]:checked + .toggle-label::after {
    transform: translateX(24px);
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.feature i {
    color: var(--success);
    font-size: 0.8rem;
}

.smtp-option .feature i {
    color: var(--info);
}

.service-config {
    border-top: 1px solid var(--border);
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

/* SMTP Configuration Block Styles */
.smtp-config-block {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.config-header .service-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1.2rem;
}

.config-header .service-info h4 {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.config-header .service-description {
    color: var(--text-light);
    font-size: 0.9rem;
}

.smtp-config-form {
    margin-top: 1rem;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.config-item {
    display: flex;
    flex-direction: column;
}

.config-item label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text);
}

.config-item .form-control {
    margin-bottom: 0.5rem;
}

.config-item small {
    color: var(--text-light);
    font-size: 0.85rem;
}

.config-item small a {
    color: var(--primary);
    text-decoration: none;
}

.config-item small a:hover {
    text-decoration: underline;
}

.config-item .form-check {
    margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .service-header {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .config-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .service-features {
        flex-direction: column;
        gap: 0.5rem;
    }
}

.search-box {
    position: relative;
    max-width: 500px;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

#search-texts {
    padding-left: 2.5rem;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
    color: var(--text-light);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(91, 127, 255, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary);
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Стили для уведомлений */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    transform: translateY(-20px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    max-width: 350px;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification i {
    font-size: 1.2rem;
}

.notification-success {
    background-color: #d1e7dd;
    border-left: 4px solid var(--success);
}

.notification-success i {
    color: var(--success);
}

.notification-error {
    background-color: #f8d7da;
    border-left: 4px solid var(--danger);
}

.notification-error i {
    color: var(--danger);
}

.notification-info {
    background-color: #cff4fc;
    border-left: 4px solid var(--info);
}

.notification-info i {
    color: var(--info);
}

/* Стиль для подсветки обновленной строки */
.row-updated {
    animation: highlight 2s ease;
}

@keyframes highlight {
    0% { background-color: rgba(25, 135, 84, 0.1); }
    100% { background-color: transparent; }
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.admin-table th, .admin-table td {
    border: 1px solid var(--border);
    padding: 12px 16px;
    text-align: left;
}

.admin-table th {
    background-color: var(--primary);
    color: #fff;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.admin-table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.admin-table tr:hover {
    background-color: rgba(91, 127, 255, 0.05);
}

.text-edit-input {
    width: 100%;
    min-height: 60px;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border);
    border-radius: 0.7rem;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.6);
    color: var(--text);
    outline: none;
    transition: border-color var(--transition), box-shadow var(--transition), background var(--transition);
    font-family: inherit;
    resize: vertical;
    box-sizing: border-box;
}

.text-edit-input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(91, 127, 255, 0.15);
    background: #fff;
}

/* Стили для кнопок */
.btn-success {
    background: var(--success);
    box-shadow: 0 2px 6px rgba(25, 135, 84, 0.15);
}

.btn-success:hover {
    background: #0d6e42;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.2);
}

/* Ограничиваем ширину колонок */
.admin-table td:first-child {
    width: 25%;
    word-break: break-word;
}

.admin-table td:last-child {
    width: 25%;
    text-align: center;
}

.admin-table td:nth-child(2) {
    width: 50%;
}

/* Стили для мобильных устройств */
@media (max-width: 768px) {
    .admin-table, .admin-table thead, .admin-table tbody, .admin-table th, .admin-table td, .admin-table tr {
        display: block;
    }

    .admin-table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .admin-table tr {
        border: 1px solid var(--border);
        margin-bottom: 15px;
        border-radius: var(--radius);
        overflow: hidden;
    }

    .admin-table td {
        border: none;
        border-bottom: 1px solid var(--border);
        position: relative;
        padding-left: 50%;
        text-align: left;
    }

    .admin-table td:before {
        position: absolute;
        top: 12px;
        left: 16px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        font-weight: 600;
    }

    .admin-table td:nth-of-type(1):before { content: "Ключ"; }
    .admin-table td:nth-of-type(2):before { content: "Текст"; }
    .admin-table td:nth-of-type(3):before { content: "Действия"; }

    .admin-table td:last-child {
        text-align: left;
    }
}
</style>

<script>
// Функциональность показа/скрытия пароля SMTP
document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.getElementById('toggle-smtp-password');
    const passwordField = document.getElementById('smtp-password');

    if (toggleButton && passwordField) {
        toggleButton.addEventListener('click', function() {
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleButton.textContent = '🙈';
                toggleButton.title = 'Скрыть пароль';
            } else {
                passwordField.type = 'password';
                toggleButton.textContent = '👁️';
                toggleButton.title = 'Показать пароль';
            }
        });
    }
});
</script>
