<?php

namespace App\Controllers;

use App\Models\Settings;
use App\Models\Message;
use App\Models\UploadedFile;
use App\Services\EdgeTtsService;
use App\Services\ContextBuilder;

class StreamController extends BaseController {
    private $settingsModel;
    private $edgeTtsService;
    private $contextBuilder;

    public function __construct() {
        $this->settingsModel = new Settings();
        $this->edgeTtsService = new EdgeTtsService();
        $this->contextBuilder = new ContextBuilder();
    }

    /**
     * Обрабатывает запрос на подготовку потоковой передачи данных
     */
    public function prepareStream(): void {
        header('Content-Type: application/json');

        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                http_response_code(405);
                echo json_encode(['error' => 'Method Not Allowed']);
                exit;
            }

            $rawInput = file_get_contents('php://input');
            if (!$rawInput) {
                http_response_code(400);
                echo json_encode(['error' => 'Empty request body']);
                exit;
            }

            $payload = json_decode($rawInput, true);
            if ($payload === null) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON']);
                exit;
            }

            // Генерируем уникальный request_id
            $requestId = uniqid('stream_', true);

            // Путь для хранения временного файла
            $tmpDir = dirname(dirname(__DIR__)) . '/tmp';
            $logFile = dirname(dirname(__DIR__)) . '/debug_log.txt';

            if (!is_dir($tmpDir)) {
                $created = mkdir($tmpDir, 0777, true);
                file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "prepareStream mkdir $tmpDir result: " . ($created ? 'success' : 'fail') . "\n", FILE_APPEND);
            } else {
                file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "prepareStream tmp dir exists: $tmpDir\n", FILE_APPEND);
            }

            $filePath = $tmpDir . "/" . $requestId . ".json";

            $jsonData = json_encode($payload);
            $saveResult = @file_put_contents($filePath, $jsonData);
            file_put_contents($logFile, date('[Y-m-d H:i:s] ') . "prepareStream save payload to $filePath, bytes: $saveResult, request_id=$requestId\n", FILE_APPEND);

            echo json_encode(['status' => 'success', 'request_id' => $requestId]);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Server error', 'message' => $e->getMessage()]);
        }
    }

    /**
     * Обрабатывает запрос на синтез речи
     */
    public function ttsStream(): void {
        header('Content-Type: application/json');

        try {
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            $text = $data['text'] ?? '';

            if (!$text) {
                http_response_code(400);
                echo json_encode(['error' => 'No text provided']);
                exit;
            }

            $audioBase64 = $this->edgeTtsService->synthesizeSpeech($text);

            if ($audioBase64) {
                echo json_encode(['audio' => $audioBase64]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'TTS synthesis failed']);
            }
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    /**
     * Обрабатывает запрос на потоковую передачу данных
     */
    public function streamProxy(): void {
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('X-Accel-Buffering: no'); // Важно для Nginx

        ignore_user_abort(true);
        @set_time_limit(0);

        // Отключаем буферизацию PHP
        @ini_set('output_buffering', 'off');
        @ini_set('zlib.output_compression', false);
        while (ob_get_level() > 0) {
            ob_end_flush();
        }
        ob_implicit_flush(true);

        $this->logMessage("streamProxy called");

        $requestId = $_GET['request_id'] ?? '';
        $q = $_GET['q'] ?? '';

        if ($requestId) {
            $tmpFile = dirname(dirname(__DIR__)) . "/tmp/" . basename($requestId) . ".json";
            if (!file_exists($tmpFile)) {
                $this->logMessage("Request ID not found: looking for $tmpFile");
                echo "data: " . json_encode(['type' => 'error', 'message' => 'Request ID not found']) . "\n\n";
                flush();
                exit;
            }
            $json = file_get_contents($tmpFile);
            if ($json === false) {
                echo "data: " . json_encode(['type' => 'error', 'message' => 'Failed to read request payload']) . "\n\n";
                flush();
                exit;
            }
        } elseif ($q) {
            $json = base64_decode($q);
            if ($json === false) {
                echo "data: " . json_encode(['type' => 'error', 'message' => 'Invalid base64 query param']) . "\n\n";
                flush();
                exit;
            }
        } else {
            echo "data: " . json_encode(['type' => 'error', 'message' => 'No request_id or query param']) . "\n\n";
            flush();
            exit;
        }

        $data = json_decode($json, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "data: " . json_encode(['type' => 'error', 'message' => 'Invalid JSON payload: ' . json_last_error_msg()]) . "\n\n";
            flush();
            exit;
        }

        $sessionId = isset($data['session_id']) ? intval($data['session_id']) : 0;

        if ($sessionId > 0) {
            try {
                // Получаем системную инструкцию, контекст и расширенную историю (например, 50 последних сообщений)
                $systemContextAndHistory = $this->contextBuilder->build($sessionId, 50);
            } catch (\Exception $e) {
                $this->logMessage("Error building context: " . $e->getMessage());
                $systemContextAndHistory = [];
            }
        } else {
            // Если сессии нет, добавляем хотя бы системную инструкцию из настроек
            $systemContextAndHistory = [];
            try {
                $settingsTmp = $this->settingsModel->getSettings();
                if (!empty($settingsTmp['system_message'])) {
                    $systemContextAndHistory[] = [
                        'role' => 'system',
                        'content' => $settingsTmp['system_message']
                    ];
                }
            } catch (\Exception $e) {
                $this->logMessage("Error loading system message without session: " . $e->getMessage());
            }
        }

        // Здесь должна быть реализация обработки потоковой передачи данных
        // Код из stream_proxy.php, который обрабатывает запросы к Mistral API
        // ...
    }

    /**
     * Логирует сообщение в файл debug_log.txt
     */
    private function logMessage(string $msg): void {
        $file = dirname(dirname(__DIR__)) . '/debug_log.txt';
        $date = date('[Y-m-d H:i:s] ');
        file_put_contents($file, $date . $msg . "\n", FILE_APPEND | LOCK_EX);
    }
}