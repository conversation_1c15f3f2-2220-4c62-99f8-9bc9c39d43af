<?php

namespace App\Models;

use App\Models\Database;

class UploadedFile {
    private $db;
    private $uploadDir = __DIR__ . '/../../context_files'; // Директория для хранения контекстных файлов

    public function __construct() {
        $this->db = Database::getInstance();
        // Создаем директорию context_files если ее нет
        if (!file_exists($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
            // Добавляем .htaccess для запрета прямого доступа
            file_put_contents($this->uploadDir.'/.htaccess', "Deny from all");
        }
    }

    /**
     * Получает все загруженные файлы.
     *
     * @return array Массив файлов.
     */
    public function getAllFiles(): array {
        $files = [];
        try {
            $stmt = $this->db->prepare("SELECT id, user_id, file_name, file_path, file_type, file_size, processed_content, created_at FROM uploaded_files ORDER BY created_at DESC");
            $result = $stmt->execute();
            while ($row = $result->fetchArray(\SQLITE3_ASSOC)) {
                // Если есть processed_content, добавляем его
                if (!empty($row['processed_content'])) {
                    $row['content'] = $row['processed_content'];
                } else {
                    // Иначе пытаемся прочитать файл
                    if (file_exists($row['file_path'])) {
                        $row['content'] = file_get_contents($row['file_path']);
                    }
                }
                $files[] = $row;
            }
        } catch (\Exception $e) {
            error_log("Error fetching uploaded files: " . $e->getMessage());
        }
        return $files;
    }

    /**
     * Сохраняет загруженный файл и добавляет запись в базу данных.
     *
     * @param array $fileData Данные файла из $_FILES.
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function saveUploadedFile(array $fileData): bool {
        if ($fileData['error'] !== UPLOAD_ERR_OK) {
            error_log("File upload error: " . $fileData['error']);
            return false;
        }

        $originalName = $fileData['name'];
        $tmpName = $fileData['tmp_name'];
        $fileType = $fileData['type']; // MIME-тип файла

        // Генерируем уникальное имя файла, чтобы избежать коллизий
        $uniqueId = uniqid('file_', true);
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $newFileName = $uniqueId . '.' . $extension;
        $filePath = $this->uploadDir . '/' . $newFileName;

        // Перемещаем файл из временной директории в постоянную
        if (move_uploaded_file($tmpName, $filePath)) {
            try {
                $stmt = $this->db->prepare("
                    INSERT INTO uploaded_files (file_name, file_path, file_type)
                    VALUES (:file_name, :file_path, :file_type)
                ");
                $stmt->bindValue(':file_name', $originalName, \SQLITE3_TEXT);
                $stmt->bindValue(':file_path', $filePath, \SQLITE3_TEXT); // Сохраняем полный путь
                $stmt->bindValue(':file_type', $fileType, \SQLITE3_TEXT); // Сохраняем MIME-тип

                if ($stmt->execute()) {
                    return true;
                } else {
                    error_log("Failed to insert file record into database: " . $this->db->lastErrorMsg());
                    // Удаляем файл, если запись в БД не удалась
                    unlink($filePath);
                    return false;
                }
            } catch (\Exception $e) {
                error_log("Exception saving file record: " . $e->getMessage());
                // Удаляем файл, если произошла ошибка БД
                unlink($filePath);
                return false;
            }
        } else {
            error_log("Failed to move uploaded file to destination: " . $filePath);
            return false;
        }
    }

    /**
     * Удаляет файл из файловой системы и запись из базы данных.
     *
     * @param int $fileId ID файла для удаления.
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function deleteFile(int $fileId): bool {
        try {
            $stmt = $this->db->prepare("SELECT file_path FROM uploaded_files WHERE id = :id");
            $stmt->bindValue(':id', $fileId, \SQLITE3_INTEGER);
            $result = $stmt->execute();
            $fileData = $result->fetchArray(\SQLITE3_ASSOC);

            if (!$fileData) {
                error_log("File with ID $fileId not found in database.");
                return false; // Файл не найден в БД
            }

            $filePath = $fileData['file_path'];

            // Удаляем запись из БД
            $deleteStmt = $this->db->prepare("DELETE FROM uploaded_files WHERE id = :id");
            $deleteStmt->bindValue(':id', $fileId, \SQLITE3_INTEGER);

            if ($deleteStmt->execute()) {
                // Удаляем файл из файловой системы, если он существует
                if (file_exists($filePath)) {
                    if (!unlink($filePath)) {
                        error_log("Failed to delete file from filesystem: " . $filePath);
                        // Возможно, стоит откатить транзакцию, если она используется
                        return false; // Ошибка удаления файла
                    }
                } else {
                     error_log("File not found on filesystem, but record deleted from DB: " . $filePath);
                }
                return true; // Успешное удаление
            } else {
                error_log("Failed to delete file record from database (ID: $fileId): " . $this->db->lastErrorMsg());
                return false;
            }
        } catch (\Exception $e) {
            error_log("Exception deleting file (ID: $fileId): " . $e->getMessage());
            return false;
        }
    } // End of deleteFile method

    /**
     * Gets file data by ID from database.
     *
     * @param int $fileId ID of the file to retrieve.
     * @return array|null File data as associative array or null if not found.
     */
    public function getFileById(int $fileId): ?array {
        try {
            $stmt = $this->db->prepare("
                SELECT id, user_id, file_name, file_path, file_type, file_size 
                FROM uploaded_files 
                WHERE id = :id
            ");
            $stmt->bindValue(':id', $fileId, \SQLITE3_INTEGER);
            $result = $stmt->execute();
            
            $fileData = $result->fetchArray(\SQLITE3_ASSOC);
            return $fileData ?: null;
        } catch (\Exception $e) {
            error_log("Error getting file by ID {$fileId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Adds a record for a context file to the database.
     * Assumes the file has already been moved to its final location.
     * Assumes 'uploaded_files' table has columns: user_id, file_name, file_path, file_type, file_size.
     *
     * @param int $userId ID of the user uploading the file.
     * @param string $originalName Original name of the uploaded file.
     * @param string $filePath Full path where the file was saved.
     * @param string $fileType Determined file type (e.g., 'text/plain', 'application/pdf').
     * @param int $fileSize Size of the file in bytes.
     * @return int|false The ID of the inserted row on success, false on failure.
     */
    public function addContextFile(int $userId, string $originalName, string $filePath, string $fileType, int $fileSize): int|false {
        try {
            // NOTE: Assumes user_id and file_size columns exist in uploaded_files table!
            $stmt = $this->db->prepare("
                INSERT INTO uploaded_files (user_id, file_name, file_path, file_type, file_size)
                VALUES (:user_id, :file_name, :file_path, :file_type, :file_size)
            ");
            $stmt->bindValue(':user_id', $userId, \SQLITE3_INTEGER);
            $stmt->bindValue(':file_name', $originalName, \SQLITE3_TEXT);
            $stmt->bindValue(':file_path', $filePath, \SQLITE3_TEXT);
            $stmt->bindValue(':file_type', $fileType, \SQLITE3_TEXT);
            $stmt->bindValue(':file_size', $fileSize, \SQLITE3_INTEGER);

            if ($stmt->execute()) {
                return $this->db->lastInsertRowID();
            } else {
                error_log("Failed to insert context file record into database: " . $this->db->lastErrorMsg());
                return false;
            }
        } catch (\Exception $e) {
            error_log("Exception saving context file record: " . $e->getMessage());
            return false;
        }
    } // End of addContextFile method

} // Closing brace for the class UploadedFile
