let socket = null;
let messageBuffer = '';

import * as audioPlayer from './audioPlayer.js'; // <<< Используем импорт модуля

/**
 * Подключается к голосовому WebSocket серверу
 * @param {string} wsUrl - URL сервера, например ws://localhost:8081
 * @param {function} onTextChunk - колбэк для текстовых чанков
 * @param {function} onAudioChunk - колбэк для аудио чанков
 * @param {function} onEnd - колбэк по завершении ответа
 */
export function connectVoiceBot(wsUrl, onTextChunk, onAudioChunk, onEnd) {
    socket = new WebSocket(wsUrl);

    socket.onopen = () => {
        console.log('VoiceBotSocket: Connected to', wsUrl);
        messageBuffer = '';
    };

    socket.onerror = (e) => {
        console.error('VoiceBotSocket: Error', e);
    };

    socket.onclose = () => {
        console.log('VoiceBotSocket: Connection closed');
    };

    socket.onmessage = (event) => {
        try {
            const msg = JSON.parse(event.data);
            if (msg.type === 'text_chunk') {
                messageBuffer += msg.data;
                if (typeof onTextChunk === 'function') onTextChunk(msg.data);
            } else if (msg.type === 'audio_chunk') {
                if (typeof onAudioChunk === 'function') onAudioChunk(msg.data);
                // <<< Вызываем метод нового плеера >>>
                audioPlayer.scheduleChunk(msg.data);
            } else if (msg.type === 'end') {
                // <<< TODO: Возможно, здесь нужно будет вызвать audioPlayer.markEndOfStream() ??? >>>
                // Пока оставляем как есть, но нужно проверить, как обрабатывается конец потока в WebSocket
                if (typeof onEnd === 'function') onEnd(messageBuffer);
                messageBuffer = '';
            } else if (msg.type === 'error') {
                console.error('VoiceBotSocket: Server error:', msg.message);
            }
        } catch (e) {
            console.error('VoiceBotSocket: Failed to parse message', e);
        }
    };
}

/**
 * Отправляет текст на сервер для генерации ответа
 * @param {string} text
 */
export function sendTextToVoiceBot(text) {
    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ text }));
    } else {
        console.warn('VoiceBotSocket: Socket not connected');
    }
}

/**
 * Закрывает соединение
 */
export function closeVoiceBotConnection() {
    if (socket) {
        socket.close();
        socket = null;
    }
}
