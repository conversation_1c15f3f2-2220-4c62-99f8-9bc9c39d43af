<?php

namespace App\Controllers;

use App\Models\Auth;

class AuthController extends BaseController {

    /**
     * Отображает страницу с формой входа.
     * Если пользователь уже авторизован, перенаправляет на главную страницу.
     */
    public function showLoginForm(): void {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        // Если пользователь уже вошел, перенаправляем на главную
        if (!empty($_SESSION['user_id'])) {
            // TODO: Использовать базовый URL или хелпер для генерации URL
            header('Location: index.php');
            exit;
        }

        // Загружаем представление формы входа
        // Передаем пустой массив данных, так как пока нет ошибок
        $this->loadView('login', []);
    }

    /**
     * Обрабатывает данные, отправленные из формы входа.
     */
    public function processLogin(): void {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Проверяем, что это POST запрос
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            // Если не POST, перенаправляем на форму входа
            // TODO: Использовать базовый URL или хелпер для генерации URL
            header('Location: index.php?page=login'); // Пример URL для роутера
            exit;
        }

        $username = trim($_POST['username'] ?? '');
        $password = trim($_POST['password'] ?? '');
        $error = null;

        if (empty($username) || empty($password)) {
            $error = "Имя пользователя и пароль не могут быть пустыми.";
        } else {
            $authModel = new Auth();
            $user = $authModel->verifyCredentials($username, $password);

            if ($user) {
                // Успешная аутентификация
                // Обновляем ID сессии для безопасности
                session_regenerate_id(true);
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                // Перенаправляем на главную страницу админки
                // TODO: Использовать базовый URL или хелпер для генерации URL
                header('Location: index.php');
                exit;
            } else {
                // Неверные учетные данные
                $error = "Неверное имя пользователя или пароль.";
            }
        }

        // Если дошли сюда, значит была ошибка
        // Загружаем представление формы входа снова, передавая сообщение об ошибке
        $this->loadView('login', ['error' => $error]);
    }

    /**
     * Выполняет выход пользователя из системы.
     */
    public function logout(): void {
        $authModel = new Auth();
        $authModel->logout();
        // Перенаправляем на страницу входа
        // TODO: Использовать базовый URL или хелпер для генерации URL
        header('Location: index.php?page=login'); // Пример URL для роутера
        exit;
    }

    /**
     * Отображает форму восстановления пароля.
     */
    public function showForgotPasswordForm(): void {
        $this->loadView('forgot_password', []);
    }

    /**
     * Обрабатывает восстановление пароля.
     */
    public function processForgotPassword(): void {
        $error = null;
        $success = null;

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=forgot');
            exit;
        }

        $email = trim($_POST['email'] ?? '');
        if (empty($email)) {
            $error = "Пожалуйста, введите email.";
            $this->loadView('forgot_password', ['error' => $error]);
            return;
        }

        $authModel = new \App\Models\Auth();
        $user = $authModel->getUserByEmail($email);

        if (!$user) {
            $error = "Пользователь с таким email не найден.";
            $this->loadView('forgot_password', ['error' => $error]);
            return;
        }

        // Генерируем новый пароль
        $newPassword = bin2hex(random_bytes(4)); // 8 символов

        // Обновляем пароль в базе
        if (!$authModel->updatePassword($user['id'], $newPassword)) {
            $error = "Ошибка при обновлении пароля. Попробуйте позже.";
            $this->loadView('forgot_password', ['error' => $error]);
            return;
        }

        // Отправляем письмо
        $to = $user['email'];
        $subject = "Восстановление пароля администратора";
        $message = "Ваш логин: {$user['username']}\nНовый пароль: {$newPassword}\n\nРекомендуем сменить пароль после входа.";
        $headers = "From: no-reply@" . $_SERVER['SERVER_NAME'] . "\r\nContent-Type: text/plain; charset=UTF-8";

        if (mail($to, $subject, $message, $headers)) {
            $success = "Новый пароль отправлен на ваш email.";
            $this->loadView('forgot_password', ['success' => $success]);
        } else {
            $error = "Не удалось отправить письмо. Обратитесь к администратору.";
            $this->loadView('forgot_password', ['error' => $error]);
        }
    }
}
