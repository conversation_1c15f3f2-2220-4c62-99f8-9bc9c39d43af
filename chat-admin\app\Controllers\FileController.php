<?php

namespace App\Controllers;

use App\Models\UploadedFile;

class FileController extends BaseController {
    private $uploadedFileModel;

    public function __construct() {
        parent::__construct();
        $this->uploadedFileModel = new UploadedFile();
    }

    /**
     * Обрабатывает загрузку файла
     */
    public function uploadFile(): void {
        // Проверяем авторизацию
        if (!$this->isAuthenticated()) {
            header('Location: index.php?page=login');
            exit;
        }
        
        // Убедимся, что сессия запущена
        $this->startSession();

        // Проверяем загрузку файла
        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            header('Location: index.php?page=messages&error=file_upload');
            exit;
        }

        $file = $_FILES['file'];
        $maxSize = 10 * 1024 * 1024; // 10MB
        $allowedTypes = ['text/plain', 'application/pdf', 'application/msword', 
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

        // Проверяем тип и размер файла
        if (!in_array($file['type'], $allowedTypes) || $file['size'] > $maxSize) {
            header('Location: index.php?page=messages&error=invalid_file');
            exit;
        }

        // Получаем ID пользователя из сессии
        $userId = $_SESSION['user_id'] ?? null;
        $sessionId = $_POST['session_id'] ?? null;
        
        // Проверяем валидность session_id
        if (!$sessionId || !is_numeric($sessionId)) {
            header('Location: index.php?page=messages&error=invalid_session');
            exit;
        }

        // Создаем папку для загрузок, если ее нет
        $uploadDir = dirname(dirname(__DIR__)) . '/uploads/';
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                header('Location: index.php?page=messages&error=directory_creation_failed');
                exit;
            }
        }

        // Проверяем, доступна ли директория для записи
        if (!is_writable($uploadDir)) {
            header('Location: index.php?page=messages&error=directory_not_writable');
            exit;
        }

        // Генерируем уникальное имя файла
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $fileName = uniqid() . '.' . $extension;
        $filePath = $uploadDir . $fileName;

        // Сохраняем файл
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            header('Location: index.php?page=messages&error=save_failed');
            exit;
        }

        // Сохраняем информацию о файле в БД
        $result = $this->uploadedFileModel->addFile(
            $userId,
            $sessionId,
            $file['name'],
            $filePath,
            $file['type'],
            $file['size']
        );

        if ($result) {
            header('Location: index.php?page=messages&success=file_uploaded');
        } else {
            header('Location: index.php?page=messages&error=db_error');
        }
        exit;
    }

    /**
     * Проверяет, авторизован ли пользователь
     */
    private function isAuthenticated(): bool {
        $this->startSession();
        return isset($_SESSION['user_id']);
    }
    
    /**
     * Запускает сессию, если она еще не запущена
     */
    private function startSession(): void {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * Получает список файлов для указанной сессии
     * 
     * @param int $sessionId ID сессии
     * @return array Массив с информацией о файлах
     */
    public function getSessionFiles(int $sessionId): array {
        // Проверяем авторизацию
        if (!$this->isAuthenticated()) {
            return [];
        }
        
        $userId = $_SESSION['user_id'] ?? null;
        if (!$userId) {
            return [];
        }
        
        return $this->uploadedFileModel->getFilesBySession($userId, $sessionId);
    }
    
    /**
     * Обрабатывает запрос на скачивание файла
     * 
     * @param int $fileId ID файла
     */
    public function downloadFile(int $fileId): void {
        // Проверяем авторизацию
        if (!$this->isAuthenticated()) {
            header('Location: index.php?page=login');
            exit;
        }
        
        $userId = $_SESSION['user_id'] ?? null;
        $file = $this->uploadedFileModel->getFileById($fileId);
        
        // Проверяем, существует ли файл и принадлежит ли он пользователю
        if (!$file || $file['user_id'] != $userId) {
            header('Location: index.php?page=messages&error=file_not_found');
            exit;
        }
        
        // Проверяем, существует ли физический файл
        if (!file_exists($file['file_path'])) {
            header('Location: index.php?page=messages&error=file_not_found');
            exit;
        }
        
        // Отправляем файл пользователю
        header('Content-Description: File Transfer');
        header('Content-Type: ' . $file['mime_type']);
        header('Content-Disposition: attachment; filename="' . $file['original_name'] . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . $file['file_size']);
        readfile($file['file_path']);
        exit;
    }
}