<?php

declare(strict_types=1);

namespace App\Services;

/**
 * Простая интеграция с AmoCRM API v4
 * Создание контактов и сделок с комментариями без OAuth
 */
class SimpleAmoCrmService
{
    private string $subdomain;
    private string $accessToken;
    private string $baseUrl;
    
    public function __construct(string $subdomain, string $accessToken)
    {
        $this->subdomain = $subdomain;
        $this->accessToken = $accessToken;
        $this->baseUrl = "https://{$subdomain}.amocrm.ru";
    }
    
    /**
     * Создает HTTP клиент для запросов к AmoCRM
     */
    private function createClient(): callable
    {
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->accessToken,
        ];
        
        return function (string $method, string $url, ?array $data = null) use ($headers): array {
            $curl = curl_init();
            curl_setopt($curl, CURLOPT_URL, $this->baseUrl . $url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($curl, CURLOPT_TIMEOUT, 30);
            
            if ($data !== null) {
                curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
            }
            
            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $error = curl_error($curl);
            curl_close($curl);
            
            if ($error) {
                throw new \Exception("cURL error: {$error}");
            }
            
            return [
                'content' => is_bool($response) ? $response : json_decode($response, true),
                'status_code' => $httpCode,
                'success' => $httpCode >= 200 && $httpCode < 300
            ];
        };
    }
    
    /**
     * Тестирует подключение к AmoCRM
     */
    public function testConnection(): array
    {
        try {
            $client = $this->createClient();
            $response = $client('GET', '/api/v4/account');
            
            if ($response['success']) {
                $account = $response['content'];
                return [
                    'success' => true,
                    'message' => "✅ Подключение успешно! Аккаунт: {$account['name']}"
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "❌ Ошибка подключения. HTTP код: {$response['status_code']}"
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => "❌ Ошибка: " . $e->getMessage()
            ];
        }
    }
    
    /**
     * Создает контакт с телефоном и дополнительными данными
     */
    public function createContact(string $name, string $phone, ?array $analyticsData = null): int
    {
        $client = $this->createClient();

        $customFields = [
            [
                'field_code' => 'PHONE',
                'values' => [
                    [
                        'enum_code' => 'WORK',
                        'value' => $phone
                    ]
                ]
            ]
        ];

        // ClientID добавляется только в лиды, не в контакты
        // (поле metrika_client_id с ID 631683 существует только для лидов)

        $contactData = [
            [
                'name' => $name,
                'custom_fields_values' => $customFields
            ]
        ];

        $response = $client('POST', '/api/v4/contacts', $contactData);

        if ($response['success']) {
            return $response['content']['_embedded']['contacts'][0]['id'];
        }

        throw new \Exception('Ошибка создания контакта: ' . json_encode($response));
    }


    
    /**
     * Получает список доступных воронок
     */
    public function getPipelines(): array
    {
        $client = $this->createClient();

        $response = $client('GET', '/api/v4/leads/pipelines');

        if ($response['success'] && !empty($response['content']['_embedded']['pipelines'])) {
            return [
                'success' => true,
                'pipelines' => $response['content']['_embedded']['pipelines']
            ];
        }

        return [
            'success' => false,
            'message' => 'Не удалось получить список воронок: ' . json_encode($response)
        ];
    }

    /**
     * Создает сделку с привязанным контактом
     */
    public function createLead(string $name, int $contactId, ?int $pipelineId = null, ?int $responsibleUserId = null, ?array $analyticsData = null): int
    {
        $client = $this->createClient();

        $leadData = [
            [
                'name' => $name,
                'price' => 0,
                '_embedded' => [
                    'contacts' => [
                        [
                            'id' => $contactId
                        ]
                    ]
                ],
                'custom_fields_values' => []
            ]
        ];

        // Добавляем ClientID в поле metrika_client_id лида (ID: 631683)
        if ($analyticsData && !empty($analyticsData['clientId'])) {
            $leadData[0]['custom_fields_values'][] = [
                'field_id' => 631683, // ID поля metrika_client_id
                'values' => [
                    [
                        'value' => $analyticsData['clientId']
                    ]
                ]
            ];
        }

        // Проверяем и добавляем ID воронки если указан
        if ($pipelineId) {
            $pipelinesResult = $this->getPipelines();
            if ($pipelinesResult['success']) {
                $validPipelineIds = array_column($pipelinesResult['pipelines'], 'id');
                if (in_array($pipelineId, $validPipelineIds)) {
                    $leadData[0]['pipeline_id'] = $pipelineId;
                } else {
                    // Используем первую доступную воронку если указанная недоступна
                    if (!empty($validPipelineIds)) {
                        $leadData[0]['pipeline_id'] = $validPipelineIds[0];
                    }
                }
            }
            // Если не удалось получить воронки, создаем лид без указания воронки
        }

        // Добавляем ответственного если указан
        if ($responsibleUserId) {
            $leadData[0]['responsible_user_id'] = $responsibleUserId;
        }

        $response = $client('POST', '/api/v4/leads', $leadData);

        if ($response['success']) {
            return $response['content']['_embedded']['leads'][0]['id'];
        }

        throw new \Exception('Ошибка создания сделки: ' . json_encode($response));
    }
    
    /**
     * Добавляет примечание к сделке
     */
    public function addNoteToLead(int $leadId, string $text): bool
    {
        $client = $this->createClient();
        
        $noteData = [
            [
                'entity_id' => $leadId,
                'note_type' => 'common',
                'params' => [
                    'text' => $text
                ]
            ]
        ];
        
        $response = $client('POST', '/api/v4/leads/notes', $noteData);
        
        return $response['success'];
    }
    
    /**
     * Создает полный лид с контактом и историей чата
     */
    public function createFullLead(string $phone, array $chatHistory, ?int $pipelineId = null, ?int $responsibleUserId = null, ?array $analyticsData = null): array
    {
        try {
            // Формируем имя контакта с учетом UTM
            $contactName = "Клиент из чата ({$phone})";
            if ($analyticsData && !empty($analyticsData['utmParams']['utm_source'])) {
                $contactName = "Клиент из {$analyticsData['utmParams']['utm_source']} ({$phone})";
                if (!empty($analyticsData['utmParams']['utm_campaign'])) {
                    $contactName = "Клиент из {$analyticsData['utmParams']['utm_source']} - {$analyticsData['utmParams']['utm_campaign']} ({$phone})";
                }
            }

            $contactId = $this->createContact($contactName, $phone, $analyticsData);

            // Формируем название сделки с учетом UTM
            $leadName = "Заявка из чата - " . date('d.m.Y H:i');
            if ($analyticsData && !empty($analyticsData['utmParams']['utm_source'])) {
                $leadName = "Заявка из {$analyticsData['utmParams']['utm_source']} - " . date('d.m.Y H:i');
            }

            $leadId = $this->createLead($leadName, $contactId, $pipelineId, $responsibleUserId, $analyticsData);
            
            // Формируем детальную историю переписки с разделением по типам сообщений
            $chatText = $this->formatDetailedChatHistory($phone, $chatHistory, $analyticsData);
            
            // Добавляем примечание с историей
            $this->addNoteToLead($leadId, $chatText);
            
            return [
                'success' => true,
                'contact_id' => $contactId,
                'lead_id' => $leadId,
                'message' => "✅ Лид создан успешно! ID: {$leadId}"
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => "❌ Ошибка создания лида: " . $e->getMessage()
            ];
        }
    }

    /**
     * Форматирует детальную историю чата с разделением по типам сообщений
     *
     * @param string $phone Номер телефона
     * @param array $chatHistory История чата
     * @param array|null $analyticsData Аналитические данные
     * @return string Отформатированная история
     */
    private function formatDetailedChatHistory(string $phone, array $chatHistory, ?array $analyticsData = null): string
    {
        $text = "📋 ИНФОРМАЦИЯ О ЛИДЕ\n";
        $text .= str_repeat("=", 50) . "\n";
        $text .= "📞 Телефон: {$phone}\n";
        $text .= "📅 Дата создания: " . date('d.m.Y H:i:s') . "\n";

        // Добавляем ClientID в отдельный блок
        if ($analyticsData && !empty($analyticsData['clientId'])) {
            $text .= "🆔 Client ID: {$analyticsData['clientId']}\n";
        }
        $text .= "\n";

        // Блок аналитических данных
        if ($analyticsData) {
            $text .= "📊 АНАЛИТИЧЕСКИЕ ДАННЫЕ\n";
            $text .= str_repeat("=", 50) . "\n";

            if (!empty($analyticsData['pageUrl'])) {
                $text .= "🌐 URL страницы: {$analyticsData['pageUrl']}\n";
            }
            if (!empty($analyticsData['referrer'])) {
                $text .= "🔗 Источник перехода: {$analyticsData['referrer']}\n";
            }

            // UTM метки в отдельном подблоке
            if (!empty($analyticsData['utmParams'])) {
                $text .= "\n🎯 UTM-МЕТКИ:\n";
                foreach ($analyticsData['utmParams'] as $key => $value) {
                    $utmLabel = strtoupper(str_replace('utm_', '', $key));
                    $text .= "• {$utmLabel}: {$value}\n";
                }
            }
            $text .= "\n";
        }

        // История переписки с четкими подписями
        $text .= "💬 ИСТОРИЯ ПЕРЕПИСКИ\n";
        $text .= str_repeat("=", 50) . "\n";
        foreach ($chatHistory as $message) {
            $timestamp = is_numeric($message['timestamp']) ? (int)$message['timestamp'] : strtotime($message['timestamp']);
            $time = date('d.m.Y H:i:s', $timestamp);
            $sender = $message['is_user'] ? 'Пользователь' : 'Бот';
            $text .= "[{$time}] {$sender}: {$message['message']}\n";
        }

        return $text;
    }

    /**
     * Извлекает телефон из текста сообщения
     */
    public static function extractPhone(string $text): ?string
    {
        // Паттерны для поиска телефонов
        $patterns = [
            '/\+7\s*\(\d{3}\)\s*\d{3}[-\s]*\d{2}[-\s]*\d{2}/',  // +7 (999) 123-45-67
            '/8\s*\(\d{3}\)\s*\d{3}[-\s]*\d{2}[-\s]*\d{2}/',   // 8 (999) 123-45-67
            '/\+7\d{10}/',                                       // +79991234567
            '/8\d{10}/',                                         // 89991234567
            '/\d{10}/',                                          // 9991234567
            '/\d{3}[-\s]*\d{3}[-\s]*\d{2}[-\s]*\d{2}/',        // 999-123-45-67
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                $phone = preg_replace('/[^\d+]/', '', $matches[0]);
                
                // Нормализуем телефон
                if (strlen($phone) === 10) {
                    $phone = '+7' . $phone;
                } elseif (strlen($phone) === 11 && $phone[0] === '8') {
                    $phone = '+7' . substr($phone, 1);
                } elseif (strlen($phone) === 11 && $phone[0] === '7') {
                    $phone = '+' . $phone;
                }
                
                return $phone;
            }
        }
        
        return null;
    }
}
