<?xml version="1.0" encoding="UTF-8"?>
<files psalm-version="5.26.1@d747f6500b38ac4f7dfc5edbcae6e4b637d7add0">
  <file src="src/Client.php">
    <PossiblyUndefinedVariable>
      <code><![CDATA[$options]]></code>
    </PossiblyUndefinedVariable>
    <UndefinedInterfaceMethod>
      <code><![CDATA[getBoundary]]></code>
    </UndefinedInterfaceMethod>
  </file>
  <file src="src/Cookie/CookieJar.php">
    <InvalidReturnStatement>
      <code><![CDATA[new \ArrayIterator(\array_values($this->cookies))]]></code>
    </InvalidReturnStatement>
    <InvalidReturnType>
      <code><![CDATA[\ArrayIterator<int, SetCookie>]]></code>
    </InvalidReturnType>
    <PossiblyFalseOperand>
      <code><![CDATA[$result]]></code>
    </PossiblyFalseOperand>
  </file>
  <file src="src/Cookie/SetCookie.php">
    <RedundantCast>
      <code><![CDATA[(bool) $discard]]></code>
      <code><![CDATA[(bool) $httpOnly]]></code>
      <code><![CDATA[(bool) $secure]]></code>
      <code><![CDATA[(int) $maxAge]]></code>
      <code><![CDATA[(string) $domain]]></code>
      <code><![CDATA[(string) $name]]></code>
      <code><![CDATA[(string) $path]]></code>
      <code><![CDATA[(string) $timestamp]]></code>
      <code><![CDATA[(string) $value]]></code>
    </RedundantCast>
    <UndefinedFunction>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing a bool to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing a bool to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing a bool to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing a string or null to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing a string to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing a string to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing a string to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing an int or null to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing an int, string or null to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
    </UndefinedFunction>
  </file>
  <file src="src/Handler/CurlFactory.php">
    <FalseOperand>
      <code><![CDATA[$timeoutRequiresNoSignal]]></code>
    </FalseOperand>
    <InvalidOperand>
      <code><![CDATA[$options['connect_timeout'] < 1]]></code>
    </InvalidOperand>
    <PossiblyFalseOperand>
      <code><![CDATA[$timeoutRequiresNoSignal]]></code>
    </PossiblyFalseOperand>
    <PossiblyInvalidArgument>
      <code><![CDATA[$easy->handle]]></code>
      <code><![CDATA[$easy->handle]]></code>
      <code><![CDATA[$easy->handle]]></code>
      <code><![CDATA[$easy->handle]]></code>
      <code><![CDATA[$easy->handle]]></code>
      <code><![CDATA[$easy->handle]]></code>
      <code><![CDATA[$handle]]></code>
      <code><![CDATA[$resource]]></code>
      <code><![CDATA[$resource]]></code>
      <code><![CDATA[$resource]]></code>
      <code><![CDATA[$resource]]></code>
      <code><![CDATA[$resource]]></code>
      <code><![CDATA[$resource]]></code>
      <code><![CDATA[$sslKey]]></code>
    </PossiblyInvalidArgument>
    <PossiblyInvalidCast>
      <code><![CDATA[$sslKey]]></code>
    </PossiblyInvalidCast>
    <UndefinedDocblockClass>
      <code><![CDATA[private $handles = [];]]></code>
      <code><![CDATA[resource[]|\CurlHandle[]]]></code>
    </UndefinedDocblockClass>
    <UndefinedVariable>
      <code><![CDATA[$startingResponse]]></code>
    </UndefinedVariable>
  </file>
  <file src="src/Handler/CurlHandler.php">
    <PossiblyInvalidArgument>
      <code><![CDATA[$easy->handle]]></code>
      <code><![CDATA[$easy->handle]]></code>
    </PossiblyInvalidArgument>
  </file>
  <file src="src/Handler/CurlMultiHandler.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$this->timeToNext()]]></code>
    </ArgumentTypeCoercion>
    <InvalidCast>
      <code><![CDATA[$easy->handle]]></code>
    </InvalidCast>
    <PossiblyInvalidArgument>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
      <code><![CDATA[$this->_mh]]></code>
    </PossiblyInvalidArgument>
    <RedundantPropertyInitializationCheck>
      <code><![CDATA[isset($this->_mh)]]></code>
    </RedundantPropertyInitializationCheck>
    <TypeDoesNotContainType>
      <code><![CDATA[false === $multiHandle]]></code>
    </TypeDoesNotContainType>
    <UndefinedDocblockClass>
      <code><![CDATA[resource|\CurlMultiHandle]]></code>
      <code><![CDATA[resource|\CurlMultiHandle]]></code>
    </UndefinedDocblockClass>
    <UndefinedFunction>
      <code><![CDATA[trigger_deprecation('guzzlehttp/guzzle', '7.4', 'Not passing an integer to %s::%s() is deprecated and will cause an error in 8.0.', __CLASS__, __FUNCTION__)]]></code>
    </UndefinedFunction>
  </file>
  <file src="src/Handler/EasyHandle.php">
    <InvalidReturnType>
      <code><![CDATA[void]]></code>
    </InvalidReturnType>
    <UndefinedDocblockClass>
      <code><![CDATA[resource|\CurlHandle]]></code>
    </UndefinedDocblockClass>
  </file>
  <file src="src/Handler/MockHandler.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[(int) $options['delay'] * 1000]]></code>
    </ArgumentTypeCoercion>
  </file>
  <file src="src/Handler/StreamHandler.php">
    <RedundantCondition>
      <code><![CDATA[!empty($options)]]></code>
      <code><![CDATA[empty($options)]]></code>
    </RedundantCondition>
    <UndefinedVariable>
      <code><![CDATA[$http_response_header]]></code>
    </UndefinedVariable>
  </file>
  <file src="src/MessageFormatter.php">
    <InvalidArgument>
      <code><![CDATA[function (array $matches) use ($request, $response, $error, &$cache) {
                if (isset($cache[$matches[1]])) {
                    return $cache[$matches[1]];
                }

                $result = '';
                switch ($matches[1]) {
                    case 'request':
                        $result = Psr7\Message::toString($request);
                        break;
                    case 'response':
                        $result = $response ? Psr7\Message::toString($response) : '';
                        break;
                    case 'req_headers':
                        $result = \trim($request->getMethod()
                                .' '.$request->getRequestTarget())
                            .' HTTP/'.$request->getProtocolVersion()."\r\n"
                            .$this->headers($request);
                        break;
                    case 'res_headers':
                        $result = $response ?
                            \sprintf(
                                'HTTP/%s %d %s',
                                $response->getProtocolVersion(),
                                $response->getStatusCode(),
                                $response->getReasonPhrase()
                            )."\r\n".$this->headers($response)
                            : 'NULL';
                        break;
                    case 'req_body':
                        $result = $request->getBody()->__toString();
                        break;
                    case 'res_body':
                        if (!$response instanceof ResponseInterface) {
                            $result = 'NULL';
                            break;
                        }

                        $body = $response->getBody();

                        if (!$body->isSeekable()) {
                            $result = 'RESPONSE_NOT_LOGGEABLE';
                            break;
                        }

                        $result = $response->getBody()->__toString();
                        break;
                    case 'ts':
                    case 'date_iso_8601':
                        $result = \gmdate('c');
                        break;
                    case 'date_common_log':
                        $result = \date('d/M/Y:H:i:s O');
                        break;
                    case 'method':
                        $result = $request->getMethod();
                        break;
                    case 'version':
                        $result = $request->getProtocolVersion();
                        break;
                    case 'uri':
                    case 'url':
                        $result = $request->getUri()->__toString();
                        break;
                    case 'target':
                        $result = $request->getRequestTarget();
                        break;
                    case 'req_version':
                        $result = $request->getProtocolVersion();
                        break;
                    case 'res_version':
                        $result = $response
                            ? $response->getProtocolVersion()
                            : 'NULL';
                        break;
                    case 'host':
                        $result = $request->getHeaderLine('Host');
                        break;
                    case 'hostname':
                        $result = \gethostname();
                        break;
                    case 'code':
                        $result = $response ? $response->getStatusCode() : 'NULL';
                        break;
                    case 'phrase':
                        $result = $response ? $response->getReasonPhrase() : 'NULL';
                        break;
                    case 'error':
                        $result = $error ? $error->getMessage() : 'NULL';
                        break;
                    default:
                        // handle prefixed dynamic headers
                        if (\strpos($matches[1], 'req_header_') === 0) {
                            $result = $request->getHeaderLine(\substr($matches[1], 11));
                        } elseif (\strpos($matches[1], 'res_header_') === 0) {
                            $result = $response
                                ? $response->getHeaderLine(\substr($matches[1], 11))
                                : 'NULL';
                        }
                }

                $cache[$matches[1]] = $result;

                return $result;
            }]]></code>
    </InvalidArgument>
  </file>
  <file src="src/Middleware.php">
    <InvalidArgument>
      <code><![CDATA[$request]]></code>
    </InvalidArgument>
  </file>
  <file src="src/RetryMiddleware.php">
    <TooManyArguments>
      <code><![CDATA[($this->delay)(++$options['retries'], $response, $request)]]></code>
    </TooManyArguments>
  </file>
  <file src="src/Utils.php">
    <ArgumentTypeCoercion>
      <code><![CDATA[$depth]]></code>
      <code><![CDATA[$depth]]></code>
    </ArgumentTypeCoercion>
    <ForbiddenCode>
      <code><![CDATA[\var_dump($input)]]></code>
    </ForbiddenCode>
    <PossiblyInvalidCast>
      <code><![CDATA[$_SERVER[$name]]]></code>
    </PossiblyInvalidCast>
  </file>
</files>
