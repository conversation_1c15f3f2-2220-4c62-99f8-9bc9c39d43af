/* ===== SUPER MODERN ADMIN PANEL DESIGN - V2.1 (Structure-Aware + Utils) ===== */

/* Import Inter font */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

:root {
  --primary: #5b7fff; /* Vibrant Blue */
  --primary-lite: #6a83dd; /* Vibrant Blue */
  --primary-dark: #4a69e0;
  --accent: #ffc107; /* Bright Yellow */
  --danger: #dc3545; /* Standard Red */
  --success: #198754; /* Standard Green */
  --info: #0dcaf0; /* Cyan Info */
  --bg: #f6f8fc; /* Light Grayish Blue */
  --glass-bg: rgba(255, 255, 255, 0.7);
  --glass-blur: 16px;
  --card-bg: rgba(255, 255, 255, 0.85);
  --sidebar-bg: rgba(255, 255, 255, 0.92);
  --sidebar-blur: 20px;
  --text: #212529; /* Dark Gray */
  --text-light: #6c757d; /* Medium Gray */
  --border: rgba(0, 0, 0, 0.08);
  --shadow: 0 8px 24px rgba(91, 127, 255, 0.08);
  --shadow-hover: 0 12px 32px rgba(91, 127, 255, 0.12);
  --radius: 1rem; /* Slightly smaller radius */
  --transition: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== BASE & TYPOGRAPHY ===== */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: "Inter", "Segoe UI", system-ui, -apple-system, sans-serif;
  background: var(--bg);
  color: var(--text);
  font-size: 14px; /* Base font size ÑƒÐ¼ÐµÐ½ÑŒÑˆÐµÐ½ */
  line-height: 1.6;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg) 0%, #eef2f7 100%);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition);
}
a:hover {
  color: var(--primary-dark);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.75em;
  font-weight: 600;
  color: var(--text);
}

p {
  margin-top: 0;
  margin-bottom: 1em;
}

/* ===== UTILITIES ===== */
.visually-hidden {
  /* For accessible hiding */
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
  white-space: nowrap; /* Prevent line breaks */
}
.text-center {
  text-align: center !important;
}
.text-muted {
  color: var(--text-light) !important;
}
.p-4 {
  padding: 1.5rem !important;
} /* Example padding utility */

/* ===== SCROLLBAR ===== */
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}
::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}
::-webkit-scrollbar-thumb {
  background: #ced4da;
  border-radius: 10px;
  transition: background var(--transition);
}
::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}

/* ===== SIDEBAR ===== */
.sidebar {
  position: sticky;
  top: 0px;
  left: 0;
  height: 88vh; /* Full viewport height */
  width: 260px; /* Slightly narrower */
  background: var(--sidebar-bg);
  backdrop-filter: blur(var(--sidebar-blur));
  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.05);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  z-index: 100;
  transition: box-shadow var(--transition), background var(--transition);
  padding: 1.5rem;
  flex-shrink: 0;
  /* overflow removed from sidebar itself */
  border-radius: 14px;
  margin-top: 20px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 2rem;
  padding-bottom: 1.2rem;
  border-bottom: 1px solid var(--border);
}
.sidebar-header i.fa-robot {
  font-size: 1.8rem;
  color: var(--primary);
}
.sidebar-title {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--primary-dark);
}

.nav-menu {
  flex: 1; /* Takes available space */
  display: flex;
  flex-direction: column;
  gap: 0.4rem; /* Tighter spacing */
  overflow-y: auto; /* Allow ONLY the menu to scroll if needed */
  margin-bottom: 1rem; /* Add space before user profile */
}
.nav-link {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.75rem 1rem; /* Adjusted padding */
  border-radius: 0.7rem; /* Consistent radius */
  color: var(--text);
  font-weight: 500;
  font-size: 1rem;
  transition: background var(--transition), color var(--transition),
    box-shadow var(--transition), transform var(--transition);
  position: relative;
  max-width: 80%;
}
.nav-link.active,
.nav-link:hover {
  background: var(--primary);
  color: #fff;
  box-shadow: 0 3px 12px rgba(91, 127, 255, 0.15);
  transform: translateX(4px);
}
.nav-link i {
  width: 1.3rem;
  text-align: center;
  font-size: 1.1rem;
}

.user-profile {
  position: absolute;
  margin-top: 66vh;
  padding: 1rem 0 0 0;
  border-top: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
}
.user-name {
  font-weight: 600;
  font-size: 0.95rem;
}
.user-email {
  font-size: 0.85rem;
  color: var(--text-light);
}
.logout-btn {
  background: transparent;
  color: var(--danger);
  border: 1px solid var(--danger);
  border-radius: 0.7rem;
  text-align: center; /* Center text */
  cursor: pointer;
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 0.8rem;
  transition: background var(--transition), color var(--transition);
}
.logout-btn:hover {
  background: var(--danger);
  color: #fff;
}

/* ===== MAIN CONTENT AREA ===== */
.main-content {
  flex: 1;
  padding: 2rem 2.5vw; /* Adjusted padding */
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 2rem; /* Consistent gap */
  overflow-y: auto; /* Ensure scrolling */
}
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem; /* Reduced margin */
  flex-wrap: wrap; /* Allow wrapping */
  gap: 1rem;
}
.content-title {
  font-size: 1.8rem; /* Slightly smaller title */
  font-weight: 700;
  color: var(--primary-dark);
}

/* ===== CARDS ===== */
.card {
  background: var(--card-bg);
  backdrop-filter: blur(var(--glass-blur));
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 1.8rem; /* Adjusted padding */
  border: 1px solid var(--border);
  transition: box-shadow var(--transition), background var(--transition);
  margin-bottom: 40px; /* Remove default margin, use gap in parent */
}
.card:hover {
  box-shadow: var(--shadow-hover);
  background: rgba(255, 255, 255, 0.95);
}
.card-header {
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  color: var(--primary-dark);
}

/* ===== FORMS & INPUTS ===== */
.form-group {
  margin-bottom: 1.5rem;
}
.form-label {
  max-width: 370px;
  display: block;
  margin-bottom: 0.4rem;
  font-weight: 500; /* Slightly lighter */
  font-size: 0.95rem;
  color: var(--text);
}
/* Specific form titles (Profile) */
.form-group h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border);
  color: var(--primary-dark);
}

.form-control,
.login-form input,
.filter-control,
select,
textarea {
  box-sizing: border-box;
  width: 100%;
  padding: 0.75rem 1rem; /* Adjusted padding */
  border: 1px solid var(--border);
  border-radius: 0.7rem;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.6);
  color: var(--text);
  outline: none;
  transition: border-color var(--transition), box-shadow var(--transition),
    background var(--transition);
  font-family: inherit;
}

/* Specific styles for color input */
input[type="color"].form-control-color {
  height: calc(2.25rem + 20px); /* Match height of standard inputs */
  padding: 0.375rem 0.375rem; /* Adjust padding */
  /* Remove default border/background if needed, but usually browser default is okay */
  cursor: pointer;
}

.form-control:focus,
.login-form input:focus,
.filter-control:focus,
select:focus,
textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(91, 127, 255, 0.1);
  background: #fff;
}
textarea.form-control {
  border: 3px solid;
  border-color: #dbdbdb;
  min-height: 400px;
  resize: vertical;
  margin-top: 20px;
  padding: 20px;
  background: #f9f9f9;
}
select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%236c757d' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.8rem center;
  background-size: 16px 12px;
  padding-right: 2.5rem;
}
.form-check {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem; /* Add some space */
}
.form-check-input {
  width: 1.15em;
  height: 1.15em;
  margin-top: 0; /* Align better */
  cursor: pointer;
  accent-color: var(--primary); /* Modern checkbox color */
  border: 1px solid var(--border);
  border-radius: 0.25em;
  flex-shrink: 0; /* Prevent shrinking */
}
.form-check .form-label {
  margin-bottom: 0;
  font-weight: normal;
}

/* ===== BUTTONS ===== */
.btn,
.login-form button {
  display: inline-flex;
  align-items: center;
  justify-content: center; /* Center content */
  gap: 0.5rem;
  padding: 0.7rem 1.4rem; /* Adjusted padding */
  border-radius: 0.7rem;
  font-size: 0.95rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  background: var(--primary);
  color: #fff;
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.15);
  transition: background var(--transition), box-shadow var(--transition),
    transform var(--transition);
  text-decoration: none;
  line-height: 1.5; /* Ensure consistent height */
  white-space: nowrap; /* Prevent wrapping */
}
.btn:hover,
.login-form button:hover {
  background: var(--primary-dark);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.2);
  transform: translateY(-1px);
}
.btn:active,
.login-form button:active {
  transform: translateY(0px);
  box-shadow: 0 1px 4px rgba(91, 127, 255, 0.1);
}
.btn-primary {
  background: var(--primary);
}
.btn-primary:hover {
  background: var(--primary-dark);
}
.btn-danger {
  background: var(--danger);
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.15);
}
.btn-danger:hover {
  background: #bb2d3b;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}
.btn-secondary {
  background: #e9ecef;
  color: var(--text);
  border: 1px solid var(--border);
  box-shadow: none;
}
.btn-secondary:hover {
  background: #dee2e6;
  border-color: #adb5bd;
  transform: none; /* No lift for secondary */
  box-shadow: none;
}
.btn-sm {
  width: 100%;
  padding: 0.35rem 0.8rem;
  font-size: 0.85rem;
  border-radius: 0.5rem;
  box-sizing: border-box;
}
.btn i,
.btn-sm i {
  font-size: 1em;
  margin-right: 0.1em;
}
.btn.mt-3 {
  margin-top: 1.5rem;
} /* Keep utility */
button:disabled {
  opacity: 0.65;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* ===== ALERTS ===== */
.alert {
  padding: 0.9rem 1.2rem;
  margin-bottom: 1.5rem;
  border-radius: 0.7rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 0.95rem;
  border: 1px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.alert i {
  font-size: 1.1em;
  margin-right: 0.2em;
}
.alert-success {
  background-color: #d1e7dd;
  color: #0f5132;
  border-color: #badbcc;
}
.alert-success i {
  color: #0f5132;
}
.alert-error {
  background-color: #f8d7da;
  color: #842029;
  border-color: #f5c2c7;
}
.alert-error i {
  color: #842029;
}
.alert-info {
  background-color: #cff4fc;
  color: #055160;
  border-color: #b6effb;
}
.alert-info i {
  color: #055160;
}

/* Fixed position notifications */
.alert.fixed-top {
  position: fixed;
  top: 20px;
  right: 0;
  left: 0;
  max-width: 500px;
  margin: 0 auto;
  z-index: 9999;
}

/* ===== DASHBOARD SPECIFIC ===== */
.stats-container {
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(220px, 1fr)
  ); /* Adjust min size */
  gap: 1.5rem;
}
.stat-card {
  padding: 1.5rem; /* Slightly less padding */
  position: relative;
  overflow: hidden;
}
.stat-card::before {
  /* Subtle gradient accent */
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary) 0%, var(--info) 100%);
  opacity: 0.7;
  border-radius: 1rem 0 0 1rem; /* Match card radius */
}
.stat-card:nth-child(2n)::before {
  background: linear-gradient(180deg, var(--accent) 0%, var(--success) 100%);
}
.stat-title {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 0.3rem;
  font-weight: 500;
}
.stat-value {
  font-size: 1.9rem; /* Larger value */
  font-weight: 700;
  color: var(--primary-dark);
  line-height: 1.2; /* Adjust line height */
}
.stat-card:nth-child(2n) .stat-value {
  color: var(--success);
}

/* ===== MESSAGES SPECIFIC ===== */
.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem; /* Space before delete button */
  align-items: flex-end;
}
.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  flex: 1 1 180px; /* Allow wrapping */
}
.filter-label {
  font-size: 0.85rem;
  color: var(--text-light);
}
.filters .btn {
  flex-shrink: 0;
} /* Prevent buttons shrinking */
.bulk-actions-container {
  /* Container for delete selected button */
  margin-top: -0.5rem; /* Pull up slightly */
  margin-bottom: 1.5rem;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
}
table {
  width: 100%;
  border-collapse: separate; /* Use separate for spacing/radius */
  border-spacing: 0;
  background: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  overflow: hidden; /* Clip content to radius */
  border: 1px solid var(--border);
}
th,
td {
  padding: 0.8rem 1rem; /* Adjusted padding */
  text-align: left;
  font-size: 0.95rem;
  border-bottom: 1px solid var(--border);
  vertical-align: middle; /* Align content vertically */
}
th {
  font-weight: 600;
  background: rgba(91, 127, 255, 0.04);
  color: var(--primary-dark);
  white-space: nowrap;
  position: sticky; /* Sticky header */
  top: 0; /* Stick to top */
  z-index: 1; /* Ensure header is above content */
}
/* Style first header cell (checkbox) */
thead th:first-child {
  width: 1%; /* Minimal width */
  padding-left: 0.7rem;
  padding-right: 0.7rem;
  text-align: center;
}
/* Style first body cell (checkbox) */
tbody td:first-child {
  padding-left: 0.7rem;
  padding-right: 0.7rem;
  text-align: center;
}
/* Style last header cell (actions) */
thead th:last-child {
  text-align: right;
  width: 1%; /* Minimal width */
}
tbody tr {
  transition: background var(--transition);
}
tbody tr:hover {
  background: rgba(91, 127, 255, 0.04);
}
tbody tr:last-child td {
  border-bottom: none;
}
td:last-child {
  text-align: right;
  white-space: nowrap;
}
td .badge {
  display: inline-block;
  vertical-align: middle;
} /* Ensure badges behave well */
td form.delete-message-form {
  display: inline-block;
  margin: 0;
  vertical-align: middle;
} /* Fix delete form display */
td .btn-danger.btn-sm {
  /* Style delete button */
  padding: 0.3rem 0.5rem;
  background: transparent;
  border: none;
  color: var(--text-light);
  box-shadow: none;
}
td .btn-danger.btn-sm:hover {
  color: var(--danger);
  background: rgba(220, 53, 69, 0.1);
  transform: none;
}

.badge {
  padding: 0.25em 0.6em;
  border-radius: 1em;
  font-size: 0.8rem;
  font-weight: 600;
  color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-transform: capitalize;
  line-height: 1.4; /* Adjust line height */
}
.badge-user {
  background: var(--primary);
}
.badge-assistant {
  background: var(--info);
  color: var(--text);
}

.pagination {
  display: flex;
  justify-content: center;
  flex-wrap: wrap; /* Allow wrapping */
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border);
}
.page-link {
  padding: 0.5rem 0.9rem;
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  color: var(--primary);
  background: #fff;
  font-weight: 500;
  font-size: 0.9rem;
  transition: background var(--transition), color var(--transition),
    box-shadow var(--transition), border-color var(--transition);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  line-height: 1.4; /* Ensure consistent height */
}
.page-link:hover {
  background: var(--primary);
  color: #fff;
  border-color: var(--primary);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.15);
}
.page-link.active {
  background: var(--primary);
  color: #fff;
  border-color: var(--primary);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.15);
  cursor: default;
}
.page-link.disabled {
  /* Style for '...' */
  color: var(--text-light);
  background: transparent;
  border: none;
  box-shadow: none;
  padding: 0.5rem 0.3rem; /* Adjust padding for ... */
  cursor: default;
}

/* ===== PROFILE SPECIFIC ===== */
.user-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border);
}
.user-avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary) 0%, var(--info) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.2);
}
.user-details {
}
.user-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.1rem 0;
  color: var(--primary-dark);
}
.user-email {
  font-size: 1rem;
  color: var(--text-light);
  margin: 0;
}

/* ===== SETTINGS SPECIFIC ===== */
.api-status {
  display: inline-flex; /* Use inline-flex */
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  margin-top: 1rem;
  padding: 0.5rem 0.9rem;
  border-radius: 1em; /* Pill shape */
  background-color: rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border);
}
.api-status i.fa-circle {
  font-size: 0.7em;
}
.api-status.active {
  color: var(--success);
  background-color: #d1e7dd;
  border-color: #badbcc;
}
.api-status.inactive {
  color: var(--danger);
  background-color: #f8d7da;
  border-color: #f5c2c7;
}

.file-upload-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 2px dashed var(--border); /* Dashed border */
  padding: 1rem;
  border-radius: 0.7rem;
  background-color: rgba(91, 127, 255, 0.02);
  transition: border-color var(--transition), background-color var(--transition);
  margin-bottom: 0.5rem; /* Space before list */
}
.file-upload-wrapper:hover {
  border-color: var(--primary);
  background-color: rgba(91, 127, 255, 0.05);
}
/* No need for .file-upload-input styles as it's visually hidden */
.file-upload-label {
  /* Style like a button */
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  background-color: #e9ecef;
  color: var(--text);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid var(--border);
}
.file-upload-label:hover {
  background-color: #dee2e6;
}
.file-upload-filename {
  font-size: 0.9rem;
  color: var(--text-light);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
}
.file-upload-filename i {
  margin-right: 0.3em;
} /* Icon spacing */

.file-list {
  margin-top: 1rem;
  border: 1px solid var(--border);
  border-radius: 0.7rem;
  max-height: 350px; /* Increased height */
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.5);
}
.file-list p {
  /* Empty state */
  text-align: center;
  color: var(--text-light);
  padding: 1.5rem;
  margin: 0;
  font-style: italic;
}
.file-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem 1rem;
  border-bottom: 1px solid var(--border);
  font-size: 0.95rem;
  transition: background var(--transition);
}
.file-item:last-child {
  border-bottom: none;
}
.file-item:hover {
  background: rgba(91, 127, 255, 0.04);
}
.file-item i.fas {
  /* Icon styling */
  color: var(--primary);
  width: 1.3em;
  text-align: center;
  font-size: 1.1em;
  flex-shrink: 0; /* Prevent icon shrinking */
}
.file-item span:not(.file-size) {
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 0.5rem;
} /* Allow filename to grow */
.file-size {
  margin-left: auto; /* Push size and button right */
  font-size: 0.85rem;
  color: var(--text-light);
  margin-right: 1rem;
  white-space: nowrap;
  flex-shrink: 0;
}
.delete-file-form {
  margin: 0;
  line-height: 1;
  flex-shrink: 0;
}
.delete-file-btn {
  /* Style delete button */
  padding: 0.3rem 0.5rem;
  background: transparent;
  border: none;
  color: var(--text-light);
  box-shadow: none;
}
.delete-file-btn:hover {
  color: var(--danger);
  background: rgba(220, 53, 69, 0.1);
  transform: none;
}

/* ===== LOGIN PAGE SPECIAL ===== */
/* Ensure body takes full height for centering */
body.login-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 1rem; /* Padding for small screens */
}
.login-container {
  background: var(--card-bg);
  backdrop-filter: blur(var(--glass-blur));
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 2.5rem;
  width: 100%;
  max-width: 400px; /* Slightly narrower */
  margin: 0 auto; /* Center horizontally */
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid var(--border);
  animation: fadeIn 0.5s var(--transition);
}
.login-header {
  text-align: center;
  margin-bottom: 1.5rem;
}
.login-header i.fa-robot {
  font-size: 3rem;
  color: var(--primary);
  margin-bottom: 1rem;
  display: block; /* Center icon */
}
.login-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 0.3rem;
}
.login-header p {
  /* Subtitle */
  color: var(--text-light);
  font-size: 0.95rem;
  margin-bottom: 0;
}
.login-form {
  width: 100%;
}
.login-form .form-group {
  margin-bottom: 1.2rem;
}
.login-form label {
  display: block;
  margin-bottom: 0.3rem;
  font-weight: 500;
  font-size: 0.9rem;
  color: var(--text);
}
.login-form input {
  padding: 0.8rem 1rem;
  font-size: 1rem;
}
.login-form button {
  width: 100%;
  margin-top: 1rem; /* Space above button */
  padding: 0.8rem;
}
.error-message {
  color: var(--danger);
  margin-top: 1.2rem;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 500;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: none;
  }
}

/* Apply fade-in to main content cards */
.main-content > .card,
.main-content > .stats-container > .stat-card {
  animation: fadeIn 0.4s var(--transition) backwards;
}
/* Stagger card animations */
.main-content > .card:nth-child(2),
.main-content > .stats-container > .stat-card:nth-child(2) {
  animation-delay: 0.05s;
}
.main-content > .card:nth-child(3),
.main-content > .stats-container > .stat-card:nth-child(3) {
  animation-delay: 0.1s;
}
.main-content > .card:nth-child(4),
.main-content > .stats-container > .stat-card:nth-child(4) {
  animation-delay: 0.15s;
}
/* Add more if needed */
body > div.main-content > div.content-header > button {
  margin-left: 1rem;
  background-color: var(--danger) !important;
  /* ===== MARKDOWN STYLES ===== */
  .markdown-body {
    color: var(--text);
    background: transparent;
    font-size: 1rem;
    line-height: 1.7;
    max-width: 100%;
    word-break: break-word;
    padding: 0.5em 0;
  }
  .markdown-body h1,
  .markdown-body h2,
  .markdown-body h3,
  .markdown-body h4,
  .markdown-body h5,
  .markdown-body h6 {
    color: var(--text);
    font-weight: 700;
    margin-top: 1.2em;
    margin-bottom: 0.6em;
    line-height: 1.25;
  }
  .markdown-body h1 {
    font-size: 2em;
    border-bottom: 1px solid var(--border);
    padding-bottom: 0.3em;
  }
  .markdown-body h2 {
    font-size: 1.5em;
    border-bottom: 1px solid var(--border);
    padding-bottom: 0.2em;
  }
  .markdown-body h3 {
    font-size: 1.2em;
  }
  .markdown-body h4,
  .markdown-body h5,
  .markdown-body h6 {
    font-size: 1em;
  }

  .markdown-body p {
    margin: 0.7em 0 1.1em 0;
  }

  .markdown-body ul,
  .markdown-body ol {
    margin: 0.7em 0 1.1em 2em;
    padding-left: 1.5em;
  }
  .markdown-body li {
    margin-bottom: 0.3em;
    line-height: 1.6;
  }
  .markdown-body ul ul,
  .markdown-body ol ol,
  .markdown-body ul ol,
  .markdown-body ol ul {
    margin-top: 0;
    margin-bottom: 0;
  }

  .markdown-body blockquote {
    margin: 1em 0;
    padding: 0.7em 1.2em;
    background: rgba(91, 127, 255, 0.06);
    border-left: 4px solid var(--primary);
    color: var(--text-light);
    font-style: italic;
  }

  .markdown-body pre {
    background: #f4f6fa;
    color: #222;
    border-radius: 0.5em;
    padding: 1em;
    overflow-x: auto;
    margin: 1em 0;
    font-size: 0.97em;
  }
  .markdown-body code {
    background: #f4f6fa;
    color: #222;
    border-radius: 0.3em;
    padding: 0.15em 0.4em;
    font-family: "Fira Mono", "Consolas", "Menlo", monospace;
    font-size: 0.97em;
  }
  .markdown-body pre code {
    background: none;
    color: inherit;
    padding: 0;
    font-size: inherit;
  }

  .markdown-body table {
    border-collapse: collapse;
    margin: 1em 0;
    width: 100%;
  }
  .markdown-body th,
  .markdown-body td {
    border: 1px solid var(--border);
    padding: 0.5em 0.8em;
    text-align: left;
  }
  .markdown-body th {
    background: #f4f6fa;
    font-weight: 600;
  }
  .markdown-body tr:nth-child(even) td {
    background: #f8fafc;
  }

  .markdown-body a {
    color: var(--primary);
    text-decoration: underline;
    transition: color var(--transition);
  }
  .markdown-body a:hover {
    color: var(--primary-dark);
  }

  .markdown-body hr {
    border: none;
    border-top: 1px solid var(--border);
    margin: 2em 0;
  }
}

.file-upload-input {
  display: none !important;
}

/* ÐŸÑ€Ð¾ÑÑ‚Ñ‹Ðµ ÑÑ‚Ð¸Ð»Ð¸ Ð´Ð»Ñ Ñ‚Ð°Ð±Ð¾Ð² */
.tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 1rem;
}
.tab-link {
  padding: 0.75rem 1.25rem;
  border: 1px solid transparent;
  border-bottom: 0;
  cursor: pointer;
  margin-right: 2px;
  background-color: #f8f9fa;
  color: #495057;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  text-decoration: none; /* Ð£Ð±Ð¸Ñ€Ð°ÐµÐ¼ Ð¿Ð¾Ð´Ñ‡ÐµÑ€ÐºÐ¸Ð²Ð°Ð½Ð¸Ðµ */
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    color 0.15s ease-in-out;
}
.tab-link:hover {
  background-color: #e9ecef;
  border-color: #e9ecef #e9ecef #dee2e6;
}
.tab-link.active {
  color: #0d6efd; /* Ð¦Ð²ÐµÑ‚ Ð°ÐºÑ‚Ð¸Ð²Ð½Ð¾Ð³Ð¾ Ñ‚Ð°Ð±Ð° */
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
  border-bottom: 1px solid #fff; /* ÐŸÐµÑ€ÐµÐºÑ€Ñ‹Ð²Ð°ÐµÑ‚ Ð½Ð¸Ð¶Ð½ÑŽÑŽ Ð³Ñ€Ð°Ð½Ð¸Ñ†Ñƒ .tabs */
  margin-bottom: -1px; /* ÐŸÐ¾Ð´Ð½Ð¸Ð¼Ð°ÐµÑ‚ Ð°ÐºÑ‚Ð¸Ð²Ð½Ñ‹Ð¹ Ñ‚Ð°Ð± */
}
.tab-content {
  display: none; /* Ð¡ÐºÑ€Ñ‹Ð²Ð°ÐµÐ¼ Ð²ÑÐµ Ñ‚Ð°Ð±Ñ‹ Ð¿Ð¾ ÑƒÐ¼Ð¾Ð»Ñ‡Ð°Ð½Ð¸ÑŽ */
  padding: 1rem 0; /* Ð”Ð¾Ð±Ð°Ð²Ð»ÑÐµÐ¼ Ð¾Ñ‚ÑÑ‚ÑƒÐ¿ ÑÐ²ÐµÑ€Ñ…Ñƒ/ÑÐ½Ð¸Ð·Ñƒ */
}
.tab-content.active {
  display: block; /* ÐŸÐ¾ÐºÐ°Ð·Ñ‹Ð²Ð°ÐµÐ¼ Ð°ÐºÑ‚Ð¸Ð²Ð½Ñ‹Ð¹ Ñ‚Ð°Ð± */
}

.upload-avatar-section {
  border: 2px dashed #dee2e6;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  transition: all 0.2s ease;
}

.upload-avatar-section:hover {
  border-color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.05);
}

.avatar-preview img {
  max-width: 100px;
  transition: transform 0.2s ease;
}

.avatar-card.current {
  border-color: #28a745;
  box-shadow: 0 0 0 1px #28a745;
}

.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active {
  background-color: #0d6efd;
  color: white;
}

.disabled {
  pointer-events: none;
  opacity: 0.65;
}

#new_avatar {
  display: none;
}

.upload-avatar-section.mb-3 {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.avatar-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 15px;
}

.avatar-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: center;
}

.avatar-actions {
  width: 85%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.avatar-card {
  width: 144px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  padding: 10px 10px 27px 10px;
  border-radius: 15px;
  background: #f2f4f9;
}

.color-input-group {
    max-width: 150px;
}
/* Ð¡ÐºÑ€Ñ‹Ñ‚Ð¸Ðµ ÑÐ»ÐµÐ¼ÐµÐ½Ñ‚Ð¾Ð² Ð´Ð¸Ð·Ð°Ð¹Ð½Ð° Ð¿Ð¾ Ð·Ð°Ð¿Ñ€Ð¾ÑÑƒ */
.color-setting-item:nth-child(6),
.color-setting-item:nth-child(7),
.form-group:nth-child(8),
.form-group:nth-child(9),
#design-settings > div > div.card-body > form > div > div:nth-child(2) > div:nth-child(7),
#design-settings > div > div.card-body > form > div > div:nth-child(2) > div:nth-child(10) > div,
#design-settings > div > div.card-body > form > div > div:nth-child(2) > div:nth-child(11),
#design-settings > div > div.card-body > form > div > div:nth-child(2) > div:nth-child(10)
{
    display: none;
}

/* ===== СТИЛИ ДЛЯ ПЕРЕПИСОК С ТЕЛЕФОНАМИ ===== */

/* Пагинация */
.pagination-nav-simple {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.pagination-simple {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 4px;
    align-items: center;
}

.page-item-simple {
    display: flex;
}

.page-link-simple {
    padding: 8px 12px;
    background: #ffffff;
    border: 1px solid #dee2e6;
    color: #007bff;
    text-decoration: none;
    font-weight: 400;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    font-size: 14px;
    border-radius: 4px;
}

.page-link-simple:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    color: #0056b3;
    text-decoration: none;
}

.page-item-simple.active .page-link-simple {
    background: #007bff;
    color: #ffffff;
    border-color: #007bff;
}

.page-item-simple.disabled .page-link-simple {
    color: #6c757d;
    background: #ffffff;
    border-color: #dee2e6;
    cursor: default;
}

.page-item-simple.disabled .page-link-simple:hover {
    background: #ffffff;
    border-color: #dee2e6;
    color: #6c757d;
}

/* Компактные карточки */
.chat-card-compact {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
}

.chat-card-compact:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.card-header-compact {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem 1rem;
    border: none;
}

.card-body-compact {
    padding: 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.card-footer-compact {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.chat-title-compact {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0;
    color: white;
}

.chat-meta-compact {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.7rem;
}

.messages-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.date-info-compact {
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 8px;
    border-left: 3px solid #2196f3;
}

.digital-data-compact-section {
    flex-grow: 1;
}

.data-compact-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
}

.data-badge-compact {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
}

.data-badge-compact:hover {
    transform: scale(1.05);
}

.no-data-compact {
    text-align: center;
    padding: 1rem;
    color: #6c757d;
    font-style: italic;
}

.action-buttons-compact {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.btn-compact {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.75rem;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    flex: 1;
}

.btn-view-compact {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-view-compact:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    color: white;
    text-decoration: none;
}

.btn-delete-compact {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.btn-delete-compact:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    color: white;
}

/* Цветовые схемы для бейджей данных */
.data-badge-phone {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-color: #28a745;
}

.data-badge-number {
    background: linear-gradient(135deg, #007bff, #6610f2);
    color: white;
    border-color: #007bff;
}

.data-badge-code {
    background: linear-gradient(135deg, #fd7e14, #e83e8c);
    color: white;
    border-color: #fd7e14;
}

.data-badge-date {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    color: white;
    border-color: #6f42c1;
}

.data-badge-time {
    background: linear-gradient(135deg, #17a2b8, #6610f2);
    color: white;
    border-color: #17a2b8;
}

.data-badge-money {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
    border-color: #ffc107;
}

.data-badge-more {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    border-color: #6c757d;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
    .chat-card-compact {
        margin-bottom: 0.75rem;
    }

    .card-header-compact {
        padding: 0.5rem 0.75rem;
    }

    .card-body-compact {
        padding: 0.75rem;
    }

    .chat-title-compact {
        font-size: 0.8rem;
    }

    .action-buttons-compact {
        flex-direction: row;
        gap: 0.25rem;
    }

    .btn-compact {
        flex: 1;
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }

    .pagination-simple {
        flex-wrap: wrap;
        gap: 0.15rem;
    }

    .page-link-simple {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
        min-width: 35px;
    }
}

@media (max-width: 576px) {
    .data-compact-grid {
        flex-direction: column;
    }

    .data-badge-compact {
        text-align: center;
        justify-content: center;
    }

    .pagination-container {
        padding: 1rem;
    }

    .col-xl-3 {
        margin-bottom: 0.5rem;
    }
}