<?php

namespace App\Models;

use App\Models\Database;

class Auth {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Проверяет учетные данные пользователя.
     *
     * @param string $username Имя пользователя.
     * @param string $password Пароль.
     * @return array|false Ассоциативный массив с id и username пользователя в случае успеха, иначе false.
     */
    public function verifyCredentials(string $username, string $password): array|false {
        try {
            $stmt = $this->db->prepare("SELECT id, username, password FROM users WHERE username = :username");
            $stmt->bindValue(':username', $username, \SQLITE3_TEXT);
            $result = $stmt->execute();
            $user = $result->fetchArray(\SQLITE3_ASSOC);

            if ($user && password_verify($password, $user['password'])) {
                // Возвращаем только необходимые данные для сессии
                return [
                    'id' => $user['id'],
                    'username' => $user['username']
                ];
            } else {
                return false; // Неверное имя пользователя или пароль
            }
        } catch (\Exception $e) {
            error_log("Error verifying credentials for user '{$username}': " . $e->getMessage());
            return false; // Ошибка при выполнении запроса
        }
    }

    /**
     * Завершает сессию пользователя (выход).
     */
    public function logout(): void {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        // Уничтожаем все данные сессии.
        $_SESSION = [];

        // Если используется сессионная cookie, удаляем и её.
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // Наконец, уничтожаем сессию.
        session_destroy();
    }
    /**
     * Получает пользователя по email.
     *
     * @param string $email
     * @return array|false
     */
    public function getUserByEmail(string $email): array|false {
        $userModel = new User();
        return $userModel->getUserByEmail($email);
    }

    /**
     * Обновляет пароль пользователя по его ID.
     *
     * @param int $userId
     * @param string $newPassword
     * @return bool
     */
    public function updatePassword(int $userId, string $newPassword): bool {
        $userModel = new User();
        return $userModel->updatePassword($userId, $newPassword);
    }
}
