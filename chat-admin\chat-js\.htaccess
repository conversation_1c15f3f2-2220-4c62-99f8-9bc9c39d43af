# ============================================================================
# СПЕЦИАЛЬНЫЙ .HTACCESS ДЛЯ JS ДИРЕКТОРИИ
# ============================================================================
# Этот файл обеспечивает CORS заголовки специально для JavaScript файлов

<IfModule mod_headers.c>
    # Устанавливаем CORS заголовки для всех файлов в этой директории
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, HEAD, OPTIONS"
    Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
    Header always set Access-Control-Max-Age "86400"
    
    # Принудительно устанавливаем правильный Content-Type для JS
    Header always set Content-Type "application/javascript; charset=utf-8"
    
    # Кэширование для лучшей производительности
    Header always set Cache-Control "public, max-age=31536000"
    Header always set Expires "Thu, 31 Dec 2025 23:59:59 GMT"
</IfModule>

# Обработка OPTIONS запросов
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Принудительная установка MIME типа для JS файлов
<FilesMatch "\.(js|mjs)$">
    ForceType application/javascript
    <IfModule mod_headers.c>
        Header always set Access-Control-Allow-Origin "*"
        Header always set Content-Type "application/javascript; charset=utf-8"
    </IfModule>
</FilesMatch>

# Запрет доступа к служебным файлам
<FilesMatch "\.(log|txt|md)$">
    <IfModule mod_authz_core.c>
        Require all denied
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order allow,deny
        Deny from all
    </IfModule>
</FilesMatch>
