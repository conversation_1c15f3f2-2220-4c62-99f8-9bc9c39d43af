<?php
// Ожидаем переменные $error и $success из контроллера
$error = $error ?? null;
$success = $success ?? null;
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <title>Восстановление пароля</title>
    <link rel="stylesheet" href="../chat-admin/public/admin-css/admin.css">
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1 class="login-title">Восстановление пароля</h1>
            <p>Введите email, указанный при регистрации администратора</p>
        </div>
        <form class="login-form" method="POST" action="index.php?page=forgot">
            <div class="form-group">
                <label for="email">Email администратора</label>
                <input type="email" id="email" name="email" required>
            </div>
            <button type="submit">Восстановить пароль</button>
            <?php if ($error): ?>
                <div class="error-message"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>
            <?php if ($success): ?>
                <div class="success-message" style="color:green;"><?= htmlspecialchars($success) ?></div>
            <?php endif; ?>
        </form>
        <div style="text-align:center; margin-top:1rem;">
            <a href="index.php?page=login">Вернуться к входу</a>
        </div>
    </div>
</body>
</html>