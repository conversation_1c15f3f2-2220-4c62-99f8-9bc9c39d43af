<?php

namespace App\Controllers;

class BaseController {

    /**
     * Загружает файл представления (view).
     *
     * @param string $view Имя файла представления (без .php) внутри папки Views.
     * @param array $data Ассоциативный массив данных, которые будут доступны в представлении.
     *                    Ключи массива становятся переменными в области видимости представления.
     * @throws \Exception Если файл представления не найден.
     */
    protected function loadView(string $view, array $data = []): void {
        // Формируем полный путь к файлу представления
        $viewPath = __DIR__ . '/../Views/' . $view . '.php';

        if (file_exists($viewPath)) {
            // Извлекаем данные в локальные переменные для доступа в представлении
            extract($data);

            // Подключаем файл представления
            // Обертка в буфер вывода не обязательна здесь, если контроллер просто выводит view.
            // Если нужно будет перехватывать вывод, можно добавить ob_start()/ob_get_clean().
            require $viewPath;
        } else {
            // Если файл не найден, выбрасываем исключение
            throw new \Exception("View file not found: " . $viewPath);
        }
    }

    /**
     * Проверяет, авторизован ли пользователь.
     * Если нет, перенаправляет на страницу входа и завершает выполнение скрипта.
     */
    protected function checkAuth(): void {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        if (empty($_SESSION['user_id'])) {
            // TODO: Сделать URL для редиректа более гибким (например, через константу или конфиг)
            header('Location: index.php?page=login'); // Указываем правильный URL для роутера
            exit;
        }
    }

     /**
     * Получает ID авторизованного пользователя из сессии.
     *
     * @return int|null ID пользователя или null, если не авторизован.
     */
    protected function getUserId(): ?int
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        return $_SESSION['user_id'] ?? null;
    }

    /**
     * Отправляет JSON ответ
     *
     * @param mixed $data Данные для отправки в формате JSON
     * @param int $statusCode HTTP код статуса (по умолчанию 200)
     */
    protected function sendJsonResponse($data, int $statusCode = 200): void {
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Проверяет, является ли текущий пользователь администратором.
     * Перенаправляет на страницу входа, если пользователь не авторизован.
     *
     * @throws \Exception Если пользователь не является администратором
     */
    protected function requireAdmin(): void {
        // Проверяем, авторизован ли пользователь
        $this->checkAuth();

        // Здесь можно добавить дополнительную проверку на права администратора,
        // если в системе есть разные роли пользователей
        // Например:
        // if (!$_SESSION['is_admin']) {
        //     header('Location: index.php?page=dashboard&error=access_denied');
        //     exit;
        // }
    }

    /**
     * Проверяет, является ли запрос AJAX запросом
     *
     * @return bool
     */
    protected function isAjaxRequest(): bool {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Отправляет JSON ответ (алиас для sendJsonResponse)
     *
     * @param array $data Данные для отправки
     * @param int $statusCode HTTP код статуса
     */
    protected function jsonResponse(array $data, int $statusCode = 200): void {
        $this->sendJsonResponse($data, $statusCode);
    }

    /**
     * Получает текущего пользователя
     *
     * @return array|null
     */
    protected function getCurrentUser(): ?array {
        $userId = $this->getUserId();
        if (!$userId) {
            return null;
        }

        // Здесь можно добавить логику получения данных пользователя из базы
        // Пока возвращаем базовую информацию
        return [
            'id' => $userId,
            'username' => $_SESSION['username'] ?? 'Admin',
            'email' => $_SESSION['email'] ?? '<EMAIL>'
        ];
    }
}
