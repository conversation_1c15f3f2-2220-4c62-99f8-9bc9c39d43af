<?php
namespace App\Services;

/**
 * Альтернативный email сервис для OpenServer
 * Использует встроенную функцию mail() если SMTP недоступен
 */
class FallbackEmailService
{
    /**
     * Отправка email с автоматическим fallback
     */
    public static function sendEmail($to, $subject, $body, $from = "<EMAIL>", $smtpConfig = null)
    {
        // Сначала пытаемся SMTP если конфигурация предоставлена
        if ($smtpConfig && !empty($smtpConfig["host"])) {
            try {
                error_log("FallbackEmailService: Attempting SMTP delivery");
                $result = SmtpService::quickSend($smtpConfig, $to, $subject, $body, $from);
                if ($result) {
                    error_log("FallbackEmailService: SMTP delivery successful");
                    return true;
                }
            } catch (\Exception $e) {
                error_log("FallbackEmailService: SMTP failed: " . $e->getMessage());
            }
        }
        
        // Fallback к встроенной функции mail()
        error_log("FallbackEmailService: Using mail() function fallback");
        return self::sendViaMail($to, $subject, $body, $from);
    }
    
    /**
     * Отправка через встроенную функцию mail()
     */
    private static function sendViaMail($to, $subject, $body, $from)
    {
        if (!function_exists("mail")) {
            error_log("FallbackEmailService: mail() function not available");
            return false;
        }
        
        $headers = "From: $from\r\n";
        $headers .= "Reply-To: $from\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
        
        $result = mail($to, $subject, $body, $headers);
        
        if ($result) {
            error_log("FallbackEmailService: mail() delivery successful");
        } else {
            error_log("FallbackEmailService: mail() delivery failed");
        }
        
        return $result;
    }
    
    /**
     * Проверка доступности SMTP
     */
    public static function testSmtpConnection($host, $port, $timeout = 5)
    {
        $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
        if ($connection) {
            fclose($connection);
            return true;
        }
        return false;
    }
}