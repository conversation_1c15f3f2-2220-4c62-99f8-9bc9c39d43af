-- Миграция для добавления полей простой интеграции AmoCRM
-- Добавляем новые поля для простой интеграции

-- Добавляем поле для поддомена AmoCRM
ALTER TABLE crm_settings ADD COLUMN amocrm_subdomain TEXT;

-- Добавляем поле для ID воронки
ALTER TABLE crm_settings ADD COLUMN amocrm_pipeline_id INTEGER;

-- Добавляем поле для ID ответственного пользователя
ALTER TABLE crm_settings ADD COLUMN amocrm_responsible_user_id INTEGER;

-- Добавляем поле для включения/отключения интеграции
ALTER TABLE crm_settings ADD COLUMN integration_enabled INTEGER DEFAULT 1;

-- Обновляем существующие записи, устанавливая поддомен из URL портала
UPDATE crm_settings 
SET amocrm_subdomain = CASE 
    WHEN amocrm_portal_url LIKE 'https://%.amocrm.ru' THEN 
        SUBSTR(amocrm_portal_url, 9, INSTR(amocrm_portal_url, '.amocrm.ru') - 9)
    WHEN amocrm_portal_url LIKE 'https://%.amocrm.com' THEN 
        SUBSTR(amocrm_portal_url, 9, INSTR(amocrm_portal_url, '.amocrm.com') - 9)
    ELSE 'advokatpushkarev'
END
WHERE amocrm_subdomain IS NULL;
