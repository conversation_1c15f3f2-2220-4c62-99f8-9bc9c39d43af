// chat-admin/chat-js/markdownFormatter.js

/**
 * Форматирует простой Markdown текст в HTML для чата.
 * Особенности:
 * - ### Заголовки — крупный жирный текст (без маркеров)
 * - **жирный** и *курсив* — стандартно
 * - Все маркеры списков (точки, цифры, дефисы, звёздочки) убираются
 * - Только заголовки выделяются, списки не отображаются как списки
 * - Красивое форматирование для текста
 * @param {string} text - Исходный текст в Markdown.
 * @returns {string} - Текст, отформатированный в HTML.
 */
/**
 * Расширенный самописный Markdown-парсер для чата.
 * Поддерживает: заголовки, жирный, курсив, списки, цитаты, код, таблицы, ссылки, горизонтальные линии.
 * Не требует глобального marked, работает как модуль.
 */
export function formatMarkdown(text) {
    if (!text) return '';

    let html = text;

    // Экранируем HTML
    html = html.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");

    // Заголовки
    html = html.replace(/^###### (.*)$/gm, '<h6>$1</h6>');
    html = html.replace(/^##### (.*)$/gm, '<h5>$1</h5>');
    html = html.replace(/^#### (.*)$/gm, '<h4>$1</h4>');
    html = html.replace(/^### (.*)$/gm, '<h3>$1</h3>');
    html = html.replace(/^## (.*)$/gm, '<h2>$1</h2>');
    html = html.replace(/^# (.*)$/gm, '<h1>$1</h1>');

    // Горизонтальная линия
    html = html.replace(/^\s*([-*_]){3,}\s*$/gm, '<hr>');

    // Блочные цитаты
    html = html.replace(/^> (.*)$/gm, '<blockquote>$1</blockquote>');

    // Списки (упорядоченные и неупорядоченные)
    // Неупорядоченные
    html = html.replace(/^(\s*)[-*+] (.*)$/gm, '$1<li>$2</li>');
    // Упорядоченные
    html = html.replace(/^(\s*)\d+\. (.*)$/gm, '$1<li>$2</li>');
    // Оборачиваем группы <li> в <ul> или <ol>
    html = html.replace(/((?:<li>.*<\/li>\s*)+)/g, function(match) {
        // Если хотя бы один элемент начинается с цифры, это <ol>
        return /^\s*<li>\d+\./m.test(match) ? `<ol>${match}</ol>` : `<ul>${match}</ul>`;
    });

    // Многострочные блоки кода
    html = html.replace(/```([\s\S]*?)```/g, function(_, code) {
        return `<pre><code>${code.replace(/^\s+|\s+$/g, '')}</code></pre>`;
    });

    // Инлайн-код
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

    // Жирный и курсив
    html = html.replace(/\*\*\*(.+?)\*\*\*/g, '<strong><em>$1</em></strong>');
    html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.+?)\*/g, '<em>$1</em>');
    html = html.replace(/__(.+?)__/g, '<strong>$1</strong>');
    html = html.replace(/_(.+?)_/g, '<em>$1</em>');

    // Ссылки [текст](url)
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener">$1</a>');

    // Таблицы (простая поддержка)
    html = html.replace(/^\|(.+)\|\n\|([ -:|]+)\|\n((?:\|.*\|\n?)*)/gm, function(_, header, align, rows) {
        const ths = header.split('|').map(h => `<th>${h.trim()}</th>`).join('');
        const trs = rows.trim().split('\n').map(row =>
            '<tr>' + row.split('|').map(cell => `<td>${cell.trim()}</td>`).join('') + '</tr>'
        ).join('');
        return `<table><thead><tr>${ths}</tr></thead><tbody>${trs}</tbody></table>`;
    });

    // Переносы строк (только если не внутри <pre>)
    html = html.replace(/(?<!<\/pre>)\n/g, '<br>');

    return `<div class="markdown-body">${html}</div>`;
}