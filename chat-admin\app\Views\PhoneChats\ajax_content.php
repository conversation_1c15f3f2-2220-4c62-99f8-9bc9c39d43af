<!-- AJAX Content Debug: <?= date('H:i:s') ?> | Total chats: <?= isset($phoneChats) ? count($phoneChats) : 'undefined' ?> -->
<?php if (!isset($phoneChats) || empty($phoneChats)): ?>
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-phone-alt fa-3x text-muted mb-3"></i>
            <h4>Переписки с телефонами не найдены</h4>
            <p class="text-muted">Переписки с телефонами появятся здесь автоматически, когда пользователи будут указывать номера телефонов в чате</p>
            <?php if (isset($error)): ?>
                <div class="alert alert-danger mt-3">
                    <strong>Ошибка:</strong> <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php else: ?>
    <!-- Компактные карточки -->
    <div class="row">
        <?php foreach ($phoneChats as $chat): ?>
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                <div class="chat-card-compact">
                    <!-- Заголовок карточки -->
                    <div class="card-header-compact">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="chat-info">
                                <h6 class="chat-title-compact">
                                    <i class="fas fa-comments me-1"></i>
                                    <?= htmlspecialchars(mb_substr($chat['title'] ?? 'Переписка #' . ($chat['id'] ?? 'N/A'), 0, 20)) ?><?= mb_strlen($chat['title'] ?? '') > 20 ? '...' : '' ?>
                                </h6>
                                <small class="chat-meta-compact">
                                    ID: <?= $chat['id'] ?? 'N/A' ?> | <?= htmlspecialchars(substr($chat['session_uuid'] ?? '', 0, 6)) ?>...
                                </small>
                            </div>
                            <span class="messages-badge">
                                <?= $chat['message_count'] ?? 0 ?>
                            </span>
                        </div>
                    </div>

                    <!-- Тело карточки -->
                    <div class="card-body-compact">
                        <!-- Информация о дате -->
                        <div class="date-info-compact">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?= isset($chat['created_at']) ? date('d.m.y H:i', strtotime($chat['created_at'])) : 'N/A' ?>
                            </small>
                        </div>

                        <!-- Цифровые данные -->
                        <div class="digital-data-compact-section">
                            <?php
                            $allData = [];

                            // Собираем телефоны
                            if (!empty($chat['phones'])) {
                                foreach ($chat['phones'] as $phone) {
                                    $allData[] = ['type' => 'phones', 'value' => $phone, 'icon' => 'phone', 'color' => 'success'];
                                }
                            }

                            // Собираем остальные данные
                            if (!empty($chat['digital_data']) && is_array($chat['digital_data'])) {
                                foreach ($chat['digital_data'] as $data) {
                                    if (is_array($data) && isset($data['type']) && isset($data['value']) && $data['type'] !== 'phones') {
                                        $color = match($data['type']) {
                                            'numbers' => 'primary',
                                            'codes' => 'warning',
                                            'dates' => 'info',
                                            'time' => 'secondary',
                                            'money' => 'success',
                                            default => 'dark'
                                        };
                                        $allData[] = [
                                            'type' => $data['type'],
                                            'value' => $data['value'],
                                            'icon' => $controller->getIconForType($data['type']),
                                            'color' => $color
                                        ];
                                    }
                                }
                            }
                            ?>

                            <?php if (!empty($allData)): ?>
                                <div class="data-compact-grid">
                                    <?php
                                    // Показываем больше элементов для лучшего отображения данных
                                    $displayData = array_slice($allData, 0, 8);
                                    $remainingCount = count($allData) - 8;
                                    ?>

                                    <?php foreach ($displayData as $item): ?>
                                        <span class="digital-data-label <?= $item['type'] ?>"
                                              <?php
                                              $styles = [
                                                  'phones' => 'background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; font-size: 14px !important; font-weight: bold !important; padding: 6px 12px !important; border-radius: 16px !important; box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3) !important;',
                                                  'numbers' => 'background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important;',
                                                  'codes' => 'background: linear-gradient(135deg, #6f42c1, #5a32a3) !important; color: white !important;',
                                                  'dates' => 'background: linear-gradient(135deg, #fd7e14, #e8590c) !important; color: white !important;',
                                                  'time' => 'background: linear-gradient(135deg, #6c757d, #545b62) !important; color: white !important;',
                                                  'money' => 'background: linear-gradient(135deg, #ffc107, #e0a800) !important; color: #212529 !important;'
                                              ];
                                              if (isset($styles[$item['type']])):
                                              ?>
                                              style="<?= $styles[$item['type']] ?>"
                                              <?php endif; ?>>
                                            <i class="fas fa-<?= $item['icon'] ?> me-1"></i>
                                            <?= htmlspecialchars(mb_substr($item['value'], 0, 10)) ?><?= mb_strlen($item['value']) > 10 ? '...' : '' ?>
                                        </span>
                                    <?php endforeach; ?>

                                    <?php if ($remainingCount > 0): ?>
                                        <span class="digital-data-label" style="background: linear-gradient(135deg, #6c757d, #495057); color: white;">
                                            +<?= $remainingCount ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="no-data-compact">
                                    <small class="text-muted">
                                        <i class="fas fa-search me-1"></i>Данные не найдены
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Футер карточки с действиями -->
                    <div class="card-footer-compact">
                        <div class="action-buttons-compact">
                            <a href="index.php?page=phone_chats&action=view&session_id=<?= $chat['id'] ?? 0 ?>"
                               class="btn-compact btn-view-compact">
                                <i class="fas fa-eye me-1"></i>Просмотр
                            </a>
                            <button type="button"
                                    class="btn-compact btn-delete-compact delete-chat-btn"
                                    data-session-id="<?= $chat['id'] ?? 0 ?>">
                                <i class="fas fa-trash me-1"></i>Удалить
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Пагинация -->
    <?php if ($totalPages > 1): ?>
        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Навигация по страницам">
                <ul class="pagination-simple">
                    <?php
                    // Логика отображения страниц
                    $showPages = [];
                    
                    if ($totalPages <= 7) {
                        // Если страниц мало, показываем все
                        for ($i = 1; $i <= $totalPages; $i++) {
                            $showPages[] = $i;
                        }
                    } else {
                        // Если страниц много, показываем с многоточием
                        if ($currentPage <= 4) {
                            for ($i = 1; $i <= 5; $i++) {
                                $showPages[] = $i;
                            }
                            $showPages[] = '...';
                            $showPages[] = $totalPages;
                        } elseif ($currentPage >= $totalPages - 3) {
                            $showPages[] = 1;
                            $showPages[] = '...';
                            for ($i = $totalPages - 4; $i <= $totalPages; $i++) {
                                $showPages[] = $i;
                            }
                        } else {
                            $showPages[] = 1;
                            $showPages[] = '...';
                            for ($i = $currentPage - 2; $i <= $currentPage + 2; $i++) {
                                $showPages[] = $i;
                            }
                            $showPages[] = '...';
                            $showPages[] = $totalPages;
                        }
                    }
                    ?>

                    <?php foreach ($showPages as $page): ?>
                        <?php if ($page === '...'): ?>
                            <li class="page-item-simple disabled">
                                <span class="page-link-simple">...</span>
                            </li>
                        <?php else: ?>
                            <li class="page-item-simple <?= $page == $currentPage ? 'active' : '' ?>">
                                <a class="page-link-simple <?= $page == $currentPage ? 'active-current' : '' ?>"
                                   href="javascript:void(0);"
                                   onclick="loadPage(<?= $page ?>)"
                                   data-page="<?= $page ?>">
                                    <?= $page ?>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
<?php endif; ?>

<script>
// Переинициализируем обработчики событий для новых элементов
document.querySelectorAll('.delete-chat-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const sessionId = this.dataset.sessionId;

        if (confirm('Удалить эту переписку? Это действие нельзя отменить.')) {
            // Показываем индикатор загрузки
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.disabled = true;

            fetch('index.php?page=phone_chats&action=deleteChat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `session_id=${sessionId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Удаляем карточку с анимацией
                    const card = this.closest('.col-xl-3');
                    card.style.transition = 'all 0.3s ease';
                    card.style.opacity = '0';
                    card.style.transform = 'scale(0.8)';

                    setTimeout(() => {
                        card.remove();
                        showNotification('Переписка удалена', 'success');
                    }, 300);
                } else {
                    // Восстанавливаем кнопку
                    this.innerHTML = '<i class="fas fa-trash"></i>';
                    this.disabled = false;
                    showNotification('Ошибка: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Восстанавливаем кнопку
                this.innerHTML = '<i class="fas fa-trash"></i>';
                this.disabled = false;
                showNotification('Произошла ошибка при удалении', 'error');
            });
        }
    });
});
</script>
