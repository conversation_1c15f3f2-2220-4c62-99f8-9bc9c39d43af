<?php
// Ожидаем переменные $user, $settings, $message, $messageType, $currentPage из контроллера
$user = $user ?? ['username' => 'Unknown', 'email' => ''];
$settings = $settings ?? [];
$message = $message ?? null;
$messageType = $messageType ?? 'info';
$currentPage = $currentPage ?? 'crm'; // Для header.php

require __DIR__ . '/partials/header.php';
?>

<div class="content-header">
    <h1 class="content-title">Интеграция с CRM</h1>
    <p class="content-subtitle">Настройка интеграции с AmoCRM и Яндекс.Метрикой</p>
</div>

<div class="container">
    <!-- Сообщение об успехе/ошибке -->
    <?php if ($message): ?>
        <div class="alert alert-<?= htmlspecialchars($messageType) ?>">
            <i class="fas <?= $messageType === 'success' ? 'fa-check-circle' : ($messageType === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle') ?>"></i>
            <?= htmlspecialchars($message) ?>
            <button type="button" class="close" onclick="this.parentElement.style.display='none'">&times;</button>
        </div>
    <?php endif; ?>

    <!-- Навигация по табам -->
    <div class="tabs">
        <a href="#amocrm-settings" class="tab-link active" data-tab="amocrm-settings">AmoCRM</a>
        <a href="#yandex-metrika-settings" class="tab-link" data-tab="yandex-metrika-settings">Яндекс.Метрика</a>
        <a href="#integration-status" class="tab-link" data-tab="integration-status">Статус интеграции</a>
    </div>

    <div id="tab-content-container">
        <!-- Таб: Настройки AmoCRM -->
        <div id="amocrm-settings" class="tab-content active">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Настройки AmoCRM</h2>
                    <p class="card-subtitle">Простая интеграция с AmoCRM для передачи лидов из чата</p>
                </div>
                <div class="card-body">
                    <form action="index.php?page=crm&action=updateAmoCrmSettings" method="POST" class="settings-form" id="amocrm-form">
                        <!-- Информация о простой интеграции -->
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Простая интеграция с AmoCRM</strong> - быстрая настройка для передачи лидов из чата.
                                        <br><strong>Что нужно:</strong>
                                        <ol class="mt-2 mb-0">
                                            <li>Поддомен вашего AmoCRM (например: <code>mycompany</code> из <code>mycompany.amocrm.ru</code>)</li>
                                            <li>Access Token - получите его в настройках AmoCRM → API → Токены доступа</li>
                                            <li>Включите интеграцию в разделе "Статус интеграции"</li>
                                        </ol>
                                        <br><strong>Как работает:</strong> Когда пользователь отправляет сообщение с телефоном, автоматически создается лид в AmoCRM с историей переписки.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Простые настройки -->
                        <div class="auth-section">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="amocrm_subdomain" class="form-label">Поддомен AmoCRM <span class="required">*</span></label>
                                        <input type="text"
                                               id="amocrm_subdomain"
                                               name="amocrm_subdomain"
                                               class="form-control crm-field"
                                               value="<?= htmlspecialchars($settings['amocrm_subdomain'] ?? 'advokatpushkarev') ?>"
                                               placeholder="mycompany"
                                               autocomplete="off"
                                               autocorrect="off"
                                               autocapitalize="off"
                                               spellcheck="false"
                                               data-lpignore="true"
                                               data-form-type="other"
                                               required>
                                        <small class="form-text">Только поддомен без .amocrm.ru (например: <code>mycompany</code>)</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="amocrm_access_token" class="form-label">Access Token <span class="required">*</span></label>
                                        <div class="input-group">
                                            <input type="text"
                                                   id="amocrm_access_token"
                                                   name="amocrm_access_token"
                                                   class="form-control token-field"
                                                   value="<?= htmlspecialchars($settings['amocrm_access_token'] ?? '') ?>"
                                                   placeholder="Введите Access Token из AmoCRM"
                                                   autocomplete="off"
                                                   autocorrect="off"
                                                   autocapitalize="off"
                                                   spellcheck="false"
                                                   data-lpignore="true"
                                                   data-form-type="other"
                                                   style="font-family: monospace; letter-spacing: 1px;"
                                                   required>
                                            <button type="button" class="btn btn-outline-secondary toggle-visibility" data-target="amocrm_access_token">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text">Токен доступа из AmoCRM → Настройки → API → Токены доступа</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="amocrm_pipeline_id" class="form-label">ID воронки (необязательно)</label>
                                        <div class="input-group">
                                            <input type="number"
                                                   id="amocrm_pipeline_id"
                                                   name="amocrm_pipeline_id"
                                                   class="form-control"
                                                   value="<?= htmlspecialchars($settings['amocrm_pipeline_id'] ?? '') ?>"
                                                   placeholder="Оставьте пустым для основной воронки">
                                            <button type="button" class="btn btn-outline-info" onclick="loadPipelines()" title="Получить список воронок">
                                                <i class="fas fa-list"></i>
                                            </button>
                                        </div>
                                        <small class="form-text">ID воронки для создания сделок (по умолчанию - основная воронка)</small>
                                        <div id="pipelines-list" class="mt-2" style="display: none;">
                                            <small class="text-muted">Доступные воронки:</small>
                                            <div id="pipelines-content" class="mt-1"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="amocrm_responsible_user_id" class="form-label">ID ответственного (необязательно)</label>
                                        <input type="number"
                                               id="amocrm_responsible_user_id"
                                               name="amocrm_responsible_user_id"
                                               class="form-control"
                                               value="<?= htmlspecialchars($settings['amocrm_responsible_user_id'] ?? '') ?>"
                                               placeholder="Оставьте пустым для автоматического назначения">
                                        <small class="form-text">ID пользователя AmoCRM, который будет ответственным за лиды</small>
                                    </div>
                                </div>
                            </div>

                            <?php if (!empty($settings['amocrm_access_token'])): ?>
                            <div class="row">
                                <div class="col-12">
                                    <div class="oauth-status-card">
                                        <div class="alert alert-success d-flex align-items-center">
                                            <div class="status-icon">
                                                <i class="fas fa-check-circle fa-2x text-success"></i>
                                            </div>
                                            <div class="status-content ms-3">
                                                <h6 class="mb-1"><strong>AmoCRM настроен</strong></h6>
                                                <p class="mb-0">
                                                    Токен доступа сохранен. Интеграция готова к работе.
                                                    <br><small class="text-muted">
                                                        <i class="fas fa-link"></i> Подключение к: <strong><?= htmlspecialchars($settings['amocrm_subdomain'] ?? '') ?>.amocrm.ru</strong>
                                                    </small>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="row">
                                <div class="col-12">
                                    <div class="oauth-status-card">
                                        <div class="alert alert-warning d-flex align-items-center">
                                            <div class="status-icon">
                                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                                            </div>
                                            <div class="status-content ms-3">
                                                <h6 class="mb-1"><strong>Требуется настройка</strong></h6>
                                                <p class="mb-0">
                                                    Укажите поддомен и Access Token для подключения к AmoCRM
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="form-actions">
                            <div class="btn-group-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Сохранить настройки
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="testConnection('amocrm-form')">
                                    <i class="fas fa-plug"></i> Тестировать подключение
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Таб: Настройки Яндекс.Метрики -->
        <div id="yandex-metrika-settings" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Настройки Яндекс.Метрики</h2>
                    <p class="card-subtitle">Параметры для отправки событий в Яндекс.Метрику</p>
                </div>
                <div class="card-body">
                    <form action="index.php?page=crm&action=updateYandexMetrikaSettings" method="POST" class="settings-form" id="yandex-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="yandex_metrika_counter_id" class="form-label">ID счётчика Яндекс.Метрики</label>
                                    <input type="number" 
                                           id="yandex_metrika_counter_id" 
                                           name="yandex_metrika_counter_id" 
                                           class="form-control" 
                                           value="<?= htmlspecialchars($settings['yandex_metrika_counter_id'] ?? '100128474') ?>"
                                           placeholder="100128474">
                                    <small class="form-text">Номер счётчика из кода Яндекс.Метрики</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="yandex_metrika_goal_name" class="form-label">Название цели</label>
                                    <input type="text" 
                                           id="yandex_metrika_goal_name" 
                                           name="yandex_metrika_goal_name" 
                                           class="form-control" 
                                           value="<?= htmlspecialchars($settings['yandex_metrika_goal_name'] ?? 'chat_contact_got') ?>"
                                           placeholder="chat_contact_got">
                                    <small class="form-text">Название цели для события "контакт оставлен"</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Сохранить настройки Яндекс.Метрики
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Таб: Статус интеграции -->
        <div id="integration-status" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Статус интеграции</h2>
                    <p class="card-subtitle">Информация о состоянии интеграции с внешними сервисами</p>
                </div>
                <div class="card-body">
                    <form action="index.php?page=crm&action=updateIntegrationStatus" method="POST" class="settings-form" id="status-form">
                        <!-- Скрытое поле для обеспечения отправки значения 0 когда чекбокс не отмечен -->
                        <input type="hidden" name="is_active" value="0">

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox"
                                               id="is_active"
                                               name="is_active"
                                               class="form-check-input"
                                               value="1"
                                               <?= isset($settings['is_active']) && (int)$settings['is_active'] === 1 ? 'checked' : '' ?>>
                                        <label for="is_active" class="form-check-label">
                                            Включить интеграцию с CRM
                                        </label>
                                    </div>
                                    <small class="form-text">При включении будет происходить автоматическое создание лидов в AmoCRM</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Сохранить статус интеграции
                            </button>
                        </div>
                    </form>
                    <div class="status-grid">
                        <div class="status-item">
                            <div class="status-icon <?= isset($settings['is_active']) && $settings['is_active'] ? 'status-active' : 'status-inactive' ?>">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="status-info">
                                <h4>Интеграция CRM</h4>
                                <p><?= isset($settings['is_active']) && $settings['is_active'] ? 'Активна' : 'Неактивна' ?></p>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-icon status-unknown">
                                <i class="fas fa-link"></i>
                            </div>
                            <div class="status-info">
                                <h4>Подключение к AmoCRM</h4>
                                <p>Не проверено</p>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-icon status-unknown">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="status-info">
                                <h4>Яндекс.Метрика</h4>
                                <p>Готова к работе</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require __DIR__ . '/partials/footer.php'; ?>

<script>
// Переключение табов
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.tab-link');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();

            // Удаляем активный класс у всех табов
            tabs.forEach(t => t.classList.remove('active'));

            // Скрываем все контенты табов
            tabContents.forEach(content => content.classList.remove('active'));

            // Добавляем активный класс к текущему табу
            this.classList.add('active');

            // Показываем соответствующий контент
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Сохраняем исходные значения полей формы для отслеживания изменений
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.dataset.originalValue = input.value;
        });
    });
});

// Функция для тестирования подключения к AmoCRM
function testConnection(formId) {
    const button = event.target;
    const originalText = button.innerHTML;

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Тестирование...';

    // Получаем данные из формы
    const form = document.getElementById(formId);
    const formData = new FormData(form);
    const formDataObj = {};
    formData.forEach((value, key) => {
        formDataObj[key] = value;
    });

    // Предварительная валидация
    if (!formDataObj.amocrm_subdomain) {
        showNotification('🌐 Укажите поддомен AmoCRM для тестирования', 'error');
        button.disabled = false;
        button.innerHTML = originalText;
        return;
    }

    if (!formDataObj.amocrm_access_token) {
        showNotification('🔑 Для тестирования необходим Access Token', 'error');
        button.disabled = false;
        button.innerHTML = originalText;
        return;
    }

    fetch('index.php?page=crm&action=testConnection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(formDataObj)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showNotification(data.message || '🎉 Подключение к AmoCRM успешно!', 'success');
        } else {
            showNotification(data.message || '❌ Ошибка подключения к AmoCRM', 'error');
        }
    })
    .catch(error => {
        console.error('Test connection error:', error);
        showNotification('🚫 Произошла ошибка при проверке подключения: ' + error.message, 'error');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// Функция для загрузки списка воронок
function loadPipelines() {
    const button = event.target;
    const originalHtml = button.innerHTML;
    const pipelinesList = document.getElementById('pipelines-list');
    const pipelinesContent = document.getElementById('pipelines-content');

    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Получаем данные из формы для аутентификации
    const subdomainInput = document.getElementById('amocrm_subdomain');
    const tokenInput = document.getElementById('amocrm_access_token');

    if (!subdomainInput.value) {
        showNotification('🌐 Сначала укажите поддомен AmoCRM', 'error');
        button.disabled = false;
        button.innerHTML = originalHtml;
        return;
    }

    if (!tokenInput.value) {
        showNotification('🔑 Сначала укажите Access Token', 'error');
        button.disabled = false;
        button.innerHTML = originalHtml;
        return;
    }

    fetch('index.php?page=crm&action=getPipelines', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success' && data.pipelines) {
            let html = '';
            data.pipelines.forEach(pipeline => {
                const isMain = pipeline.is_main ? ' <span class="badge badge-primary">Основная</span>' : '';
                html += `
                    <div class="pipeline-item" style="margin-bottom: 5px;">
                        <button type="button" class="btn btn-sm btn-outline-secondary"
                                onclick="selectPipeline(${pipeline.id}, '${pipeline.name}')"
                                style="margin-right: 5px;">
                            Выбрать
                        </button>
                        <strong>ID: ${pipeline.id}</strong> - ${pipeline.name}${isMain}
                    </div>
                `;
            });
            pipelinesContent.innerHTML = html;
            pipelinesList.style.display = 'block';
            showNotification('📋 Список воронок загружен', 'success');
        } else {
            showNotification(data.message || '❌ Не удалось загрузить список воронок', 'error');
        }
    })
    .catch(error => {
        console.error('Load pipelines error:', error);
        showNotification('🚫 Ошибка загрузки воронок: ' + error.message, 'error');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalHtml;
    });
}

// Функция для выбора воронки
function selectPipeline(pipelineId, pipelineName) {
    const pipelineInput = document.getElementById('amocrm_pipeline_id');
    pipelineInput.value = pipelineId;
    showNotification(`✅ Выбрана воронка: ${pipelineName} (ID: ${pipelineId})`, 'success');

    // Скрываем список воронок
    document.getElementById('pipelines-list').style.display = 'none';
}

</script>

<script>
// Защита от автозаполнения браузером и функциональность показа/скрытия токенов
document.addEventListener('DOMContentLoaded', function() {
    // Функция для переключения видимости токена
    function setupTokenVisibilityToggle() {
        const toggleButtons = document.querySelectorAll('.toggle-visibility');

        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const targetInput = document.getElementById(targetId);
                const icon = this.querySelector('i');

                if (targetInput.type === 'text') {
                    // Скрываем токен
                    targetInput.type = 'password';
                    icon.className = 'fas fa-eye';
                    this.title = 'Показать токен';
                } else {
                    // Показываем токен
                    targetInput.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                    this.title = 'Скрыть токен';
                }
            });
        });
    }

    // Защита от автозаполнения браузером
    function preventAutofill() {
        const protectedFields = document.querySelectorAll('.token-field, .crm-field');

        protectedFields.forEach(field => {
            // Сохраняем оригинальное значение
            const originalValue = field.value;

            // Очищаем поле при фокусе, если значение изменилось неожиданно
            field.addEventListener('focus', function() {
                // Небольшая задержка, чтобы браузер успел автозаполнить
                setTimeout(() => {
                    // Если поле было автозаполнено и это не оригинальное значение
                    if (this.value !== originalValue && this.value.length > 0 && originalValue.length > 0) {
                        // Восстанавливаем оригинальное значение
                        this.value = originalValue;
                        console.log('Предотвращено автозаполнение токена браузером');
                    }
                }, 100);
            });

            // Обновляем оригинальное значение при изменении пользователем
            field.addEventListener('input', function() {
                // Обновляем только если пользователь действительно вводит данные
                if (document.activeElement === this) {
                    field.dataset.userModified = 'true';
                }
            });

            // Дополнительная защита - очистка при загрузке страницы
            field.addEventListener('change', function() {
                if (!this.dataset.userModified && this.value !== originalValue) {
                    this.value = originalValue;
                }
            });
        });
    }

    // Дополнительная защита - создание фейкового поля для обмана браузера
    function createDecoyFields() {
        const forms = document.querySelectorAll('form');

        forms.forEach(form => {
            // Создаем скрытые поля-приманки для браузера
            const decoyUsername = document.createElement('input');
            decoyUsername.type = 'text';
            decoyUsername.name = 'fake_username';
            decoyUsername.style.position = 'absolute';
            decoyUsername.style.left = '-9999px';
            decoyUsername.style.opacity = '0';
            decoyUsername.tabIndex = -1;
            decoyUsername.autocomplete = 'username';

            const decoyPassword = document.createElement('input');
            decoyPassword.type = 'password';
            decoyPassword.name = 'fake_password';
            decoyPassword.style.position = 'absolute';
            decoyPassword.style.left = '-9999px';
            decoyPassword.style.opacity = '0';
            decoyPassword.tabIndex = -1;
            decoyPassword.autocomplete = 'current-password';

            // Вставляем приманки в начало формы
            form.insertBefore(decoyUsername, form.firstChild);
            form.insertBefore(decoyPassword, form.firstChild);
        });
    }

    // Инициализируем все функции защиты
    setupTokenVisibilityToggle();
    preventAutofill();
    createDecoyFields();

    // Дополнительная защита - отключаем автозаполнение для всей формы
    const crmForms = document.querySelectorAll('#amocrm-form, #metrika-form');
    crmForms.forEach(form => {
        form.setAttribute('autocomplete', 'off');
    });
});
</script>

<script>
// Функция для отображения всплывающего уведомления
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : (type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle')}"></i>
        </div>
        <div class="notification-content">${message}</div>
        <button type="button" class="notification-close" onclick="this.parentElement.remove()">&times;</button>
    `;
    
    document.body.appendChild(notification);
    
    // Автоматически скрываем уведомление через 5 секунд
    setTimeout(() => {
        notification.classList.add('notification-hide');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

// Проверяем параметры URL при загрузке страницы
document.addEventListener('DOMContentLoaded', function() {
    // Получаем параметры из URL
    const urlParams = new URLSearchParams(window.location.search);
    
    // Проверяем параметр success
    if (urlParams.has('success')) {
        let message = '';
        switch (urlParams.get('success')) {
            case '1':
            case 'settings_saved':
                message = '✅ Настройки AmoCRM успешно сохранены! Теперь можно авторизоваться.';
                break;
            case 'test_success':
                message = '🎉 Тестирование подключения к AmoCRM прошло успешно! Интеграция работает.';
                break;
            case 'oauth_success':
                message = '🔑 OAuth авторизация завершена успешно! Токены доступа получены и сохранены.';
                break;
            case 'status_updated':
                message = '⚙️ Статус интеграции успешно обновлен.';
                break;
        }
        
        if (message) {
            showNotification(message, 'success');
            
            // Удаляем параметр из URL без перезагрузки страницы
            const url = new URL(window.location.href);
            url.searchParams.delete('success');
            window.history.replaceState({}, document.title, url.toString());
        }
    }
    
    // Проверяем параметр error
    if (urlParams.has('error')) {
        let message = '';
        switch (urlParams.get('error')) {
            case 'settings_save':
                message = '❌ Не удалось сохранить настройки AmoCRM. Попробуйте еще раз.';
                break;
            case 'test_failed':
                message = '🔌 Тестирование подключения к AmoCRM не удалось. Проверьте настройки.';
                break;
            case 'empty_fields':
                message = '📝 Заполните все обязательные поля: URL портала, Client ID, Client Secret и Redirect URI.';
                break;
            case 'empty_oauth_fields':
                message = '🔑 Для OAuth авторизации необходимо заполнить URL портала AmoCRM, Client ID и Client Secret.';
                break;
            case 'oauth_no_code':
                message = '❌ OAuth ошибка: AmoCRM не вернул код авторизации. Попробуйте авторизоваться заново.';
                break;
            case 'oauth_not_configured':
                message = '⚙️ OAuth не настроен. Сначала сохраните Client ID и Client Secret, затем нажмите "Авторизоваться".';
                break;
            case 'oauth_token_failed':
                message = '🔐 Не удалось получить токены доступа. Проверьте правильность Client ID и Client Secret.';
                break;
            case 'oauth_save_failed':
                message = '💾 Ошибка при сохранении токенов OAuth в базу данных. Обратитесь к администратору.';
                break;
            case 'oauth_error':
                message = '⚠️ Произошла ошибка при OAuth авторизации. Проверьте настройки интеграции в AmoCRM.';
                break;
            case 'oauth_failed':
                message = '🚫 OAuth авторизация не удалась: ' + (urlParams.get('message') || 'Неизвестная ошибка');
                break;
        }
        
        if (message) {
            showNotification(message, 'error');
            
            // Удаляем параметр из URL без перезагрузки страницы
            const url = new URL(window.location.href);
            url.searchParams.delete('error');
            window.history.replaceState({}, document.title, url.toString());
        }
    }
    


    // Добавляем обработчики событий для форм с AJAX отправкой
    const forms = document.querySelectorAll('.settings-form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // Предотвращаем стандартную отправку формы

            const submitBtn = this.querySelector('button[type="submit"]');
            if (!submitBtn) return;

            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Сохранение...';

            // Получаем данные формы
            const formData = new FormData(this);

            // Отправляем AJAX запрос
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Восстанавливаем кнопку
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;

                // Показываем уведомление
                showNotification(data.message, data.status);

                // Если сохранение успешно, обновляем оригинальные значения
                if (data.status === 'success') {
                    const inputs = this.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        input.dataset.originalValue = input.value;
                    });
                }
            })
            .catch(error => {
                console.error('Ошибка при отправке формы:', error);

                // Восстанавливаем кнопку
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;

                // Показываем ошибку
                showNotification('Произошла ошибка при сохранении настроек.', 'error');
            });
        });
    });
});

// Функция для показа уведомлений
function showNotification(message, type = 'info') {
    // Удаляем существующие уведомления
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // Создаем новое уведомление
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas ${getNotificationIcon(type)} notification-icon"></i>
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Добавляем уведомление в начало страницы
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(notification, container.firstChild);
    }

    // Автоматически скрываем уведомление через 5 секунд
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success':
            return 'fa-check-circle';
        case 'error':
            return 'fa-exclamation-circle';
        case 'warning':
            return 'fa-exclamation-triangle';
        default:
            return 'fa-info-circle';
    }
}


</script>

<style>
.required {
    color: #dc3545;
}

/* Стили для Redirect URI */
.redirect-uri-container {
    position: relative;
}

.btn-icon {
    padding: 0.375rem 0.75rem;
    border-radius: 0;
    border-left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 42px;
}

.btn-icon:first-of-type {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-icon:last-of-type {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.input-group-append {
    display: flex;
}

.input-group-append .btn-icon {
    border-left: 1px solid #ced4da;
}

.input-group-append .btn-icon:hover {
    z-index: 2;
}

/* Стили для группы кнопок действий */
.btn-group-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: center;
}

.btn-group-actions .btn {
    white-space: nowrap;
}

/* Улучшенные стили для уведомлений */
.form-text.text-info {
    color: #17a2b8 !important;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.form-text.text-info i {
    margin-right: 0.25rem;
}

/* Стили для OAuth статуса */
.oauth-status-card {
    margin: 1rem 0;
}

.oauth-status-card .alert {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem;
}

.oauth-status-card .status-icon {
    flex-shrink: 0;
}

.oauth-status-card .status-content {
    flex-grow: 1;
}

.oauth-status-card h6 {
    color: inherit;
    margin-bottom: 0.5rem;
}

.oauth-status-card p {
    margin-bottom: 0;
    line-height: 1.4;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
    .btn-group-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-group-actions .btn {
        margin-bottom: 0.25rem;
    }

    .input-group-append {
        flex-direction: column;
    }

    .btn-icon {
        border-radius: 0.375rem !important;
        border-left: 1px solid #ced4da !important;
        margin-top: 0.25rem;
    }

    .oauth-status-card .alert {
        flex-direction: column;
        text-align: center;
    }

    .oauth-status-card .status-icon {
        margin-bottom: 0.5rem;
    }

    .oauth-status-card .status-content {
        margin-left: 0 !important;
    }
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    background: var(--card-bg);
}

.status-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-unknown {
    background: #fff3cd;
    color: #856404;
}

.status-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.status-info p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-muted);
}
</style>

<style>
/* Стили для всплывающих уведомлений */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    padding: 15px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    animation: notification-slide-in 0.3s ease-out forwards;
    background-color: #fff;
    border-left: 4px solid #2196F3;
}

.notification-success {
    border-left-color: #4CAF50;
}

.notification-error {
    border-left-color: #F44336;
}

.notification-icon {
    margin-right: 15px;
    font-size: 20px;
}

.notification-success .notification-icon {
    color: #4CAF50;
}

.notification-error .notification-icon {
    color: #F44336;
}

.notification-content {
    flex-grow: 1;
    font-size: 14px;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
}

.notification-close:hover {
    color: #333;
}

.notification-hide {
    animation: notification-slide-out 0.3s ease-in forwards;
}

@keyframes notification-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes notification-slide-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Стили для переключателя типа аутентификации */
.auth-type-switcher {
    margin-bottom: 1rem;
}

.auth-type-switcher .form-check-inline {
    margin-right: 2rem;
}

.auth-type-switcher .form-check-label {
    font-size: 1rem;
    cursor: pointer;
}

.auth-section {
    margin-top: 1.5rem;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    background-color: #f8f9fa;
}

.oauth-status {
    margin-bottom: 1rem;
}

.oauth-status .alert {
    margin-bottom: 0;
}

/* Скрытие секций по умолчанию */
#legacy-settings {
    display: none;
}

#oauth-settings {
    display: block;
}

/* Стили для полей токенов и CRM */
.token-field {
    font-family: 'Courier New', Consolas, monospace !important;
    letter-spacing: 1px;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.crm-field {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.token-field:focus,
.crm-field:focus {
    background-color: #ffffff;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.toggle-visibility {
    border-left: none;
    background-color: #f8f9fa;
    border-color: #ced4da;
    color: #6c757d;
    transition: all 0.3s ease;
}

.toggle-visibility:hover {
    background-color: #e9ecef;
    color: #495057;
}

.toggle-visibility:focus {
    box-shadow: none;
    border-color: #80bdff;
}

/* Дополнительная защита от автозаполнения */
.token-field:-webkit-autofill,
.token-field:-webkit-autofill:hover,
.token-field:-webkit-autofill:focus,
.crm-field:-webkit-autofill,
.crm-field:-webkit-autofill:hover,
.crm-field:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px #f8f9fa inset !important;
    -webkit-text-fill-color: #495057 !important;
    transition: background-color 5000s ease-in-out 0s;
}

/* Скрытие полей-приманок от пользователя */
input[name="fake_username"],
input[name="fake_password"] {
    position: absolute !important;
    left: -9999px !important;
    opacity: 0 !important;
    pointer-events: none !important;
    tab-index: -1 !important;
}
</style>


</style>
</div>