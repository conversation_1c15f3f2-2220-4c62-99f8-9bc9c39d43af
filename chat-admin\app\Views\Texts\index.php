<?php include_once __DIR__ . '/../partials/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-font me-2"></i>Управление текстами интерфейса
                    </h4>
                    <p class="text-muted mb-0">Редактирование текстов, отображаемых в чате</p>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <?php if (empty($texts)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-font fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Тексты не найдены</h5>
                            <p class="text-muted">Инициализируйте тексты для начала работы</p>
                            <button class="btn btn-primary" onclick="initializeTexts()">
                                <i class="fas fa-plus me-2"></i>Инициализировать тексты
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <input type="text" class="form-control" id="searchTexts" placeholder="Поиск по ключу или тексту...">
                            </div>
                            <div class="col-md-6 mb-3 text-end">
                                <button class="btn btn-success" onclick="initializeTexts()">
                                    <i class="fas fa-sync me-2"></i>Обновить тексты
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped" id="textsTable">
                                <thead>
                                    <tr>
                                        <th>Ключ</th>
                                        <th>Текст</th>
                                        <th>Язык</th>
                                        <th>Обновлен</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($texts as $text): ?>
                                        <tr data-key="<?= htmlspecialchars($text['key']) ?>">
                                            <td>
                                                <code><?= htmlspecialchars($text['key']) ?></code>
                                            </td>
                                            <td>
                                                <div class="text-preview" style="max-width: 300px;">
                                                    <?= htmlspecialchars(mb_substr($text['text'], 0, 100)) ?>
                                                    <?php if (mb_strlen($text['text']) > 100): ?>
                                                        <span class="text-muted">...</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= htmlspecialchars($text['language']) ?></span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('d.m.Y H:i', strtotime($text['updated_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="editText('<?= htmlspecialchars($text['key']) ?>', '<?= htmlspecialchars($text['language']) ?>')">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно для редактирования текста -->
<div class="modal fade" id="editTextModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Редактировать текст
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editTextForm">
                    <div class="mb-3">
                        <label class="form-label">Ключ</label>
                        <input type="text" class="form-control" id="editKey" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Язык</label>
                        <input type="text" class="form-control" id="editLanguage" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Текст</label>
                        <textarea class="form-control" id="editText" rows="5" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-primary" onclick="saveText()">
                    <i class="fas fa-save me-2"></i>Сохранить
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Поиск по текстам
document.getElementById('searchTexts').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('#textsTable tbody tr');
    
    rows.forEach(row => {
        const key = row.querySelector('code').textContent.toLowerCase();
        const text = row.querySelector('.text-preview').textContent.toLowerCase();
        
        if (key.includes(searchTerm) || text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Редактирование текста
function editText(key, language) {
    // Получаем текущий текст
    fetch(`index.php?page=api&action=getText&key=${encodeURIComponent(key)}&language=${encodeURIComponent(language)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('editKey').value = key;
                document.getElementById('editLanguage').value = language;
                document.getElementById('editText').value = data.text;
                
                new bootstrap.Modal(document.getElementById('editTextModal')).show();
            } else {
                alert('Ошибка загрузки текста: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Ошибка загрузки текста');
        });
}

// Сохранение текста
function saveText() {
    const key = document.getElementById('editKey').value;
    const language = document.getElementById('editLanguage').value;
    const text = document.getElementById('editText').value;
    
    if (!text.trim()) {
        alert('Текст не может быть пустым');
        return;
    }
    
    const formData = new FormData();
    formData.append('key', key);
    formData.append('language', language);
    formData.append('text', text);
    
    fetch('index.php?page=texts&action=updateText', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Текст обновлен успешно!');
            bootstrap.Modal.getInstance(document.getElementById('editTextModal')).hide();
            location.reload();
        } else {
            alert('Ошибка обновления: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Ошибка обновления текста');
    });
}

// Инициализация текстов
function initializeTexts() {
    if (confirm('Инициализировать/обновить тексты? Это может перезаписать существующие изменения.')) {
        fetch('index.php?page=api&action=initializeTexts', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Тексты успешно инициализированы!');
                location.reload();
            } else {
                alert('Ошибка инициализации: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Ошибка инициализации текстов');
        });
    }
}
</script>

<?php include_once __DIR__ . '/../partials/footer.php'; ?>
