<?php

namespace App\Services;

// Подключаем классы из Azure TTS SDK
use Microsoft\CognitiveServices\Speech\SpeechConfig;
use Microsoft\CognitiveServices\Speech\Audio\AudioConfig;
use Microsoft\CognitiveServices\Speech\SpeechSynthesizer;
use Microsoft\CognitiveServices\Speech\ResultReason;

class TtsService {
    private $speechKey;
    private $speechRegion;
    private $isConfigured = false;

    public function __construct() {
        // Получаем ключи из переменных окружения (более безопасный способ)
        // TODO: Убедиться, что переменные MS_TTS_KEY и MS_TTS_REGION установлены на сервере
        $this->speechKey = getenv('MS_TTS_KEY');
        $this->speechRegion = getenv('MS_TTS_REGION') ?: 'eastus'; // Регион по умолчанию

        if (!empty($this->speechKey)) {
            $this->isConfigured = true;
        } else {
             error_log("TTS Service initialized but MS_TTS_KEY environment variable is not set.");
        }
    }

    /**
     * Проверяет, настроен ли сервис TTS.
     * @return bool
     */
    public function isConfigured(): bool {
        return $this->isConfigured;
    }

    /**
     * Синтезирует речь из текста.
     *
     * @param string $text Текст для синтеза.
     * @param string $voiceName Имя голоса (например, 'ru-RU-SvetlanaNeural').
     * @return string|null Бинарные аудиоданные или null в случае ошибки.
     */
    public function synthesizeSpeech(string $text, string $voiceName = 'ru-RU-SvetlanaNeural'): ?string {
        if (!$this->isConfigured()) {
            error_log("TTS Service synthesizeSpeech called but service is not configured.");
            return null;
        }

        try {
            $speechConfig = SpeechConfig::fromSubscription($this->speechKey, $this->speechRegion);
            // Устанавливаем голос
            $speechConfig->setSpeechSynthesisVoiceName($voiceName);
            // Устанавливаем формат вывода (можно выбрать другие, например, Riff24Khz16BitMonoPcm)
            // $speechConfig->setSpeechSynthesisOutputFormat(SpeechSynthesisOutputFormat::Audio16Khz32KBitRateMonoMp3);

            // Используем null для AudioConfig, чтобы получить аудиоданные в память
            $audioConfig = null; // AudioConfig::fromDefaultSpeakerOutput(); // Для вывода в динамик
            $synthesizer = new SpeechSynthesizer($speechConfig, $audioConfig);

            $result = $synthesizer->SpeakTextAsync($text)->get(); // Используем get() для ожидания результата

            if ($result->getReason() == ResultReason::SynthesizingAudioCompleted) {
                return $result->getAudioData();
            } else {
                $errorDetails = $result->getErrorDetails();
                error_log("TTS synthesis failed. Reason: " . $result->getReason() . ". Details: " . $errorDetails);
                return null;
            }
        } catch (\Exception $e) {
            error_log("TTS Service Exception: " . $e->getMessage());
            return null;
        }
    }
}
