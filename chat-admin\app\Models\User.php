<?php

namespace App\Models;

use App\Models\Database;

class User {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Получает информацию о пользователе по его ID.
     *
     * @param int $userId ID пользователя.
     * @return array|false Ассоциативный массив с данными пользователя (username, email) или false, если пользователь не найден.
     */
    public function getUserById(int $userId): array|false {
        try {
            $stmt = $this->db->prepare("SELECT username, email FROM users WHERE id = :id");
            $stmt->bindValue(':id', $userId, \SQLITE3_INTEGER);
            $result = $stmt->execute();
            // fetchArray(SQLITE3_ASSOC) возвращает false, если нет строк
            return $result->fetchArray(\SQLITE3_ASSOC);
        } catch (\Exception $e) {
            error_log("Error fetching user by ID ($userId): " . $e->getMessage());
            return false; // Возвращаем false в случае ошибки
        }
    }

    /**
     * Проверяет текущий пароль пользователя.
     *
     * @param int $userId ID пользователя.
     * @param string $currentPassword Текущий пароль для проверки.
     * @return bool True, если пароль верный, иначе false.
     */
    public function verifyPassword(int $userId, string $currentPassword): bool {
        try {
            $stmt = $this->db->prepare("SELECT password FROM users WHERE id = :id");
            $stmt->bindValue(':id', $userId, \SQLITE3_INTEGER);
            $result = $stmt->execute();
            $dbPasswordHash = $result->fetchArray(\SQLITE3_NUM)[0] ?? null;

            return $dbPasswordHash && password_verify($currentPassword, $dbPasswordHash);
        } catch (\Exception $e) {
            error_log("Error verifying password for user ID ($userId): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Обновляет пароль пользователя.
     *
     * @param int $userId ID пользователя.
     * @param string $newPassword Новый пароль (не хешированный).
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function updatePassword(int $userId, string $newPassword): bool {
        try {
            $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("UPDATE users SET password = :password WHERE id = :id");
            $stmt->bindValue(':password', $newPasswordHash, \SQLITE3_TEXT);
            $stmt->bindValue(':id', $userId, \SQLITE3_INTEGER);
            return (bool)$stmt->execute();
        } catch (\Exception $e) {
            error_log("Error updating password for user ID ($userId): " . $e->getMessage());
            return false;
        }
    }

     /**
     * Обновляет email пользователя.
     *
     * @param int $userId ID пользователя.
     * @param string $newEmail Новый email.
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function updateEmail(int $userId, string $newEmail): bool {
         // Дополнительная проверка на уникальность email перед обновлением (опционально, но рекомендуется)
         /*
         try {
             $checkStmt = $this->db->prepare("SELECT id FROM users WHERE email = :email AND id != :id");
             $checkStmt->bindValue(':email', $newEmail, \SQLITE3_TEXT);
             $checkStmt->bindValue(':id', $userId, \SQLITE3_INTEGER);
             $existingUser = $checkStmt->execute()->fetchArray();
             if ($existingUser) {
                 error_log("Attempt to update email to an existing one for user ID ($userId). Email: $newEmail");
                 return false; // Email уже используется другим пользователем
             }
         } catch (\Exception $e) {
              error_log("Error checking email uniqueness for user ID ($userId): " . $e->getMessage());
              return false; // Ошибка при проверке
         }
         */

        try {
            $stmt = $this->db->prepare("UPDATE users SET email = :email WHERE id = :id");
            $stmt->bindValue(':email', $newEmail, \SQLITE3_TEXT);
            $stmt->bindValue(':id', $userId, \SQLITE3_INTEGER);
            return (bool)$stmt->execute();
        } catch (\Exception $e) {
            // Обработка ошибки UNIQUE constraint, если она есть в схеме БД
            if ($this->db->lastErrorCode() === 19) { // 19 - SQLITE_CONSTRAINT (включая UNIQUE)
                 error_log("Error updating email for user ID ($userId): Email '$newEmail' likely already exists.");
            } else {
                 error_log("Error updating email for user ID ($userId): " . $e->getMessage());
            }
            return false;
        }
    }

    // Другие методы...
    // - getUserByUsername(string $username)
    // - createUser(string $username, string $password, string $email)
    // - deleteUser(int $userId)

    /**
     * Удаляет всех пользователей из базы данных.
     */
    public function deleteAllUsers(): void
    {
        $this->db->exec("DELETE FROM users");
    }
    /**
     * Получает информацию о пользователе по email.
     *
     * @param string $email Email пользователя.
     * @return array|false Ассоциативный массив с данными пользователя (id, username, email) или false, если пользователь не найден.
     */
    public function getUserByEmail(string $email): array|false {
        try {
            $stmt = $this->db->prepare("SELECT id, username, email FROM users WHERE email = :email");
            $stmt->bindValue(':email', $email, \SQLITE3_TEXT);
            $result = $stmt->execute();
            return $result->fetchArray(\SQLITE3_ASSOC);
        } catch (\Exception $e) {
            error_log("Error fetching user by email ($email): " . $e->getMessage());
            return false;
        }
    }
}
