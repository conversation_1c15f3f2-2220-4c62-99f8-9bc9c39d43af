<?php

namespace App\Controllers;

use App\Models\User;

class ProfileController extends BaseController {

    /**
     * Отображает страницу профиля пользователя.
     */
    public function index(): void {
        $this->checkAuth();
        $userId = $this->getUserId();
        $userModel = new User();
        $user = $userModel->getUserById($userId);

        if (!$user) {
            // Маловероятно, но на всякий случай
            session_destroy();
            header('Location: index.php?page=login&error=user_not_found');
            exit;
        }

        // Обработка сообщений из GET-параметров
        $message = null;
        $messageType = 'info';
        if (isset($_GET['success'])) {
            $messageType = 'success';
            if ($_GET['success'] === 'password') {
                $message = 'Пароль успешно изменен.';
            } elseif ($_GET['success'] === 'email') {
                $message = 'Email успешно изменен.';
            }
        } elseif (isset($_GET['error'])) {
             $messageType = 'error';
             // Можно добавить коды ошибок для большей детализации
             $message = 'Ошибка обновления профиля.';
             if ($_GET['error'] === 'password_mismatch') $message = 'Новые пароли не совпадают.';
             if ($_GET['error'] === 'current_password_invalid') $message = 'Неверный текущий пароль.';
             if ($_GET['error'] === 'email_invalid') $message = 'Некорректный email.';
             if ($_GET['error'] === 'update_failed') $message = 'Ошибка сохранения данных.';
             if ($_GET['error'] === 'missing_fields') $message = 'Все поля обязательны для заполнения.';
        }


        $this->loadView('profile', [
            'user' => $user, // Данные для header и самой страницы
            'message' => $message,
            'messageType' => $messageType,
            'pageName' => 'profile' // Для подсветки меню
        ]);
    }

    /**
     * Обрабатывает запрос на смену пароля (POST).
     */
    public function changePassword(): void {
        $this->checkAuth();
        $userId = $this->getUserId();
        $userModel = new User();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=profile');
            exit;
        }

        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';

        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            header('Location: index.php?page=profile&error=missing_fields');
            exit;
        }

        if ($newPassword !== $confirmPassword) {
            header('Location: index.php?page=profile&error=password_mismatch');
            exit;
        }

        // Проверяем текущий пароль
        if (!$userModel->verifyPassword($userId, $currentPassword)) {
             header('Location: index.php?page=profile&error=current_password_invalid');
             exit;
        }

        // Обновляем пароль
        if ($userModel->updatePassword($userId, $newPassword)) {
            header('Location: index.php?page=profile&success=password');
            exit;
        } else {
            header('Location: index.php?page=profile&error=update_failed');
            exit;
        }
    }

    /**
     * Обрабатывает запрос на смену email (POST).
     */
    public function changeEmail(): void {
        $this->checkAuth();
        $userId = $this->getUserId();
        $userModel = new User();

         if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=profile');
            exit;
        }

        $newEmail = trim($_POST['new_email'] ?? '');
        $currentPassword = $_POST['password'] ?? ''; // Пароль для подтверждения

        if (empty($newEmail) || empty($currentPassword)) {
            header('Location: index.php?page=profile&error=missing_fields');
            exit;
        }

        if (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
            header('Location: index.php?page=profile&error=email_invalid');
            exit;
        }

        // Проверяем текущий пароль
        if (!$userModel->verifyPassword($userId, $currentPassword)) {
             header('Location: index.php?page=profile&error=current_password_invalid');
             exit;
        }

        // Обновляем email
        if ($userModel->updateEmail($userId, $newEmail)) {
            header('Location: index.php?page=profile&success=email');
            exit;
        } else {
            // Ошибка может быть из-за уникальности email или другой проблемы БД
            header('Location: index.php?page=profile&error=update_failed');
            exit;
        }
    }
}
