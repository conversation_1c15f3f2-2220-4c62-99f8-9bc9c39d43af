<?php
// Ожидаем переменные из контроллера
$user = $user ?? ['username' => 'Unknown', 'email' => ''];
$message = $message ?? null;
$messageType = $messageType ?? 'info';
$pageName = $pageName ?? 'profile'; // Для header.php

require __DIR__ . '/partials/header.php';
?>

<div class="content-header">
    <h1 class="content-title">Профиль администратора</h1>
</div>

<div class="container">

    <!-- Сообщение об успехе/ошибке -->
    <?php if ($message): ?>
        <div class="alert alert-<?= htmlspecialchars($messageType) ?>">
            <i class="fas <?= $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle' ?>"></i>
            <?= htmlspecialchars($message) ?>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body">
            <div class="user-info">
                <div class="user-avatar">
                    <?= strtoupper(substr($user['username'] ?? 'A', 0, 1)) ?>
                </div>
                <div class="user-details">
                    <h2 class="user-name"><?= htmlspecialchars($user['username'] ?? 'Admin') ?></h2>
                    <p class="user-email"><?= htmlspecialchars($user['email'] ?? '<EMAIL>') ?></p>
                </div>
            </div>

            <!-- Форма смены пароля -->
            <form method="POST" action="index.php?page=profile&action=changePassword">
                <div class="form-group">
                    <h3>Смена пароля</h3>
                </div>
                <div class="form-group">
                    <label for="current_password" class="form-label">Текущий пароль</label>
                    <input type="password" id="current_password" name="current_password" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="new_password" class="form-label">Новый пароль</label>
                    <input type="password" id="new_password" name="new_password" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="confirm_password" class="form-label">Подтвердите новый пароль</label>
                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                </div>
                <button type="submit" class="btn">
                    <i class="fas fa-key"></i> Сменить пароль
                </button>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <!-- Форма смены email -->
            <form method="POST" action="index.php?page=profile&action=changeEmail">
                 <div class="form-group">
                    <h3>Смена email</h3>
                </div>
                <div class="form-group">
                    <label for="new_email" class="form-label">Новый email</label>
                    <input type="email" id="new_email" name="new_email" class="form-control"
                           value="<?= htmlspecialchars($user['email'] ?? '') ?>" required>
                </div>
                <div class="form-group">
                    <label for="password_email" class="form-label">Текущий пароль (для подтверждения)</label>
                    <input type="password" id="password_email" name="password" class="form-control" required>
                </div>
                <button type="submit" class="btn">
                    <i class="fas fa-envelope"></i> Сменить email
                </button>
            </form>
        </div>
    </div>
</div>

<?php require __DIR__ . '/partials/footer.php'; ?>
