<?php

namespace App\Controllers;

use App\Models\TextModel;

class TextsController extends BaseController
{
    private $textModel;

    public function __construct()
    {
        $this->textModel = new TextModel();
    }

    /**
     * Главная страница управления текстами
     */
    public function index()
    {
        $this->checkAuth();

        try {
            // Получаем все тексты
            $texts = $this->textModel->getAllTexts();

            $this->loadView('Texts/index', [
                'texts' => $texts,
                'pageName' => 'texts'
            ]);
        } catch (\Exception $e) {
            error_log("TextsController: Error in index: " . $e->getMessage());
            $this->loadView('Texts/index', [
                'texts' => [],
                'pageName' => 'texts',
                'error' => 'Ошибка загрузки текстов'
            ]);
        }
    }

    /**
     * Обновление текста
     */
    public function updateText()
    {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendJsonResponse(['success' => false, 'message' => 'Метод не поддерживается'], 405);
            return;
        }

        $key = trim($_POST['key'] ?? '');
        $text = trim($_POST['text'] ?? '');
        $language = trim($_POST['language'] ?? 'ru');

        if (!$key || !$text) {
            $this->sendJsonResponse(['success' => false, 'message' => 'Ключ и текст обязательны']);
            return;
        }

        try {
            $this->textModel->updateText($key, $text, $language);
            $this->generateJsFile();

            $this->sendJsonResponse(['success' => true, 'message' => 'Текст обновлен']);
        } catch (\Exception $e) {
            error_log("TextsController: Error updating text: " . $e->getMessage());
            $this->sendJsonResponse(['success' => false, 'message' => 'Ошибка обновления текста']);
        }
    }

    public function initializeTexts()
    {
        // Основные тексты интерфейса
        $this->addOrUpdateText('mic_active_text', 'Говорите');
        
        // Уведомления
        $this->addOrUpdateText('notification_copied', 'Скопировано!');
        $this->addOrUpdateText('notification_chat_title_updated', 'Название чата обновлено!');
        $this->addOrUpdateText('notification_edited', 'Отредактировано!');
        $this->addOrUpdateText('notification_new_chat_created', 'Новый чат создан!');
        
        // Сообщения в чате
        $this->addOrUpdateText('chat_deleted_message', 'Чат удален. Выберите или создайте новый.');
        $this->addOrUpdateText('no_chats_message', 'Создайте новый чат!');
        $this->addOrUpdateText('initial_greeting', 'Привет! Я ваш AI-ассистент. Чем могу помочь?');
        $this->addOrUpdateText('empty_chat_message', 'Чат пуст. Начните общение!');
        $this->addOrUpdateText('no_assistant_response', '(Нет ответа от ассистента)');
        
        // Алерты и ошибки
        $this->addOrUpdateText('error_speech_not_supported', 'Распознавание речи не поддерживается вашим браузером.');
        $this->addOrUpdateText('error_mic_activation', 'Не удалось активировать микрофон: ');
        $this->addOrUpdateText('error_recognition', 'Ошибка распознавания');
        $this->addOrUpdateText('error_chat_title_empty', 'Название чата не может быть пустым.');
        $this->addOrUpdateText('error_update_chat_title', 'Не удалось обновить название чата: ');
        $this->addOrUpdateText('error_delete_chat', 'Ошибка удаления чата: ');
        $this->addOrUpdateText('error_init', 'Произошла ошибка при инициализации интерфейса ассистента. Пожалуйста, обновите страницу или обратитесь в поддержку.');
        $this->addOrUpdateText('error_copy_failed', 'Не удалось скопировать текст.');
        $this->addOrUpdateText('error_message_empty', 'Сообщение не может быть пустым.');
        
        // Другие тексты
        $this->addOrUpdateText('interrupt_response', 'Прервать ответ');
        $this->addOrUpdateText('confirm_delete_chat', 'Вы уверены, что хотите удалить этот чат? Вся история будет потеряна.');
        $this->addOrUpdateText('preview_message_1', 'Здравствуйте. Скажите, вам ясно, кто виноват в заливе - соседи или УК?');
        $this->addOrUpdateText('preview_message_2', 'Главное получить корректный Акт о заливе, рассказать вам, как он должен выглядеть?');
        
        // Генерируем JS-файл с текстами
        $this->generateJsFile();
        
        return ['status' => 'success', 'message' => 'Тексты успешно инициализированы'];
    }
    
    private function addOrUpdateText($key, $text, $language = 'ru')
    {
        // Проверяем, существует ли текст
        $existingText = $this->textModel->getTextByKey($key, $language);
        
        if ($existingText) {
            // Если текст существует, обновляем его
            $this->textModel->updateText($key, $text, $language);
        } else {
            // Если текст не существует, добавляем его
            $this->textModel->addText($key, $text, $language);
        }
    }
    
    private function generateJsFile()
    {
        $texts = $this->textModel->getAllTextsForJs();
        
        // Формируем содержимое JS-файла
        $jsContent = "/**\n";
        $jsContent .= " * Автоматически сгенерированный файл с текстами для языка: ru\n";
        $jsContent .= " * Дата генерации: " . date('Y-m-d H:i:s') . "\n";
        $jsContent .= " */\n\n";
        $jsContent .= "export const texts = " . json_encode($texts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . ";\n";
        
        // Add the new preview messages to ensure they're always included
        $jsContent .= "\n// Добавлены тексты для превью беседы\n";
        $jsContent .= "texts['preview_message_1'] = '" . addslashes('Здравствуйте. Скажите, вам ясно, кто виноват в заливе - соседи или УК?') . "';\n";
        $jsContent .= "texts['preview_message_2'] = '" . addslashes('Главное получить корректный Акт о заливе, рассказать вам, как он должен выглядеть?') . "';\n";
        
        // Путь к файлу
        $filePath = dirname(__DIR__, 2) . '/chat-js/texts_ru.js';
        
        // Сохраняем файл
        file_put_contents($filePath, $jsContent);
    }
}
