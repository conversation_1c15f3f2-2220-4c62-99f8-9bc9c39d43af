:root {
  --czn-chat-primary: #4361ee;
  --czn-chat-primary-dark: #3a56d4;
  --czn-chat-secondary: #3f37c9;
  --czn-chat-text: #79818f;
  --czn-chat-text-light: #8d99ae;
  --czn-chat-bg: #f8f9fa;
  --czn-color-textarea-edited: #f8f9fa;
  --czn-chat-card: #ffffff;
  --czn-chat-error: #ef233c;
  --czn-chat-error-gentle: #ff7f50;
  --czn-chat-error-gentle-dark: #e57373;
  --czn-chat-success: #4cc9f0;
  --czn-chat-border: rgba(0, 0, 0, 0.1);
  --czn-chat-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --czn-chat-shadow-soft: 0 8px 30px rgba(0, 0, 0, 0.1);
  --chat-font: "<PERSON><PERSON>", -apple-system, Blink<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
    <PERSON><PERSON>, sans-serif;
}

[data-theme="dark"] {
  --czn-chat-primary: #4895ef;
  --czn-chat-primary-dark: #3a7bc8;
  --czn-chat-secondary: #560bad;
  --czn-chat-text: #f8f9fa;
  --czn-chat-text-light: #adb5bd;
  --czn-chat-bg: #121212;
  --czn-chat-card: #1e1e1e;
  --czn-chat-error: #f72585;
  --czn-chat-error-gentle: #f08080;
  --czn-chat-error-gentle-dark: #e57373;
  --czn-chat-success: #4cc9f0;
  --czn-chat-border: rgba(255, 255, 255, 0.1);
  --czn-chat-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  --czn-chat-shadow-soft: 0 8px 30px rgba(0, 0, 0, 0.35);
}

.czn-chat-container *,
.czn-chat-window *,
.czn-chat-modal * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  transition: background-color 0.3s, color 0.2s, border-color 0.2s,
    box-shadow 0.2s;
}

.czn-chat-container-wrapper {
  font-family: var(--chat-font);
  z-index: 100001 !important;
}

button.czn-chat-action-btn.czn-chat-new-chat-call-btn {
  animation: czn-chat-ring-ring 1.5s ease-in-out infinite;
}

.czn-chat-container,
.czn-chat-window,
.czn-chat-modal {
  font-family: inherit, system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: var(--czn-chat-text);
}

.czn-chat-container-wrapper {
  position: fixed;
  bottom: 2rem;
  right: 10px;
  z-index: 9900;
}

.czn-chat-container {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
  gap: 0.75rem;
  padding: 8px 10px;
  box-shadow: var(--czn-chat-shadow);
  transition: all 1.3s ease;
  border-radius: 12px;
  background: #fff;
  border: 1px solid var(--czn-chat-primary);
  cursor: pointer;
}

.czn-chat-container:hover {
  border: 1px solid var(--czn-chat-primary-dark);
}

[data-theme="dark"] .czn-chat-container {
  background: var(--czn-chat-card);
}

.czn-chat-markdown-body ul,
.czn-chat-markdown-body ol {
  list-style: none;
  padding-left: 1.5em;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.czn-chat-markdown-body ul > li::before {
  content: "â€¢";
  margin-right: 0.5em;
  color: var(--czn-chat-primary);
}
.czn-chat-markdown-body ol {
  counter-reset: list-counter;
}
.czn-chat-markdown-body ol > li {
  counter-increment: list-counter;
}
.czn-chat-markdown-body ol > li::before {
  content: counter(list-counter) ".";
  margin-right: 0.5em;
  color: var(--czn-chat-primary);
  font-weight: bold;
}

.czn-chat-markdown-body h1,
.czn-chat-markdown-body h2,
.czn-chat-markdown-body h3,
.czn-chat-markdown-body h4,
.czn-chat-markdown-body h5,
.czn-chat-markdown-body h6 {
  margin-top: 0.8em;
  margin-bottom: 0.4em;
  line-height: 1.3;
  font-weight: 600;
  color: var(--czn-chat-text);
}

.czn-chat-markdown-body h1 {
  font-size: 1.5em;
}
.czn-chat-markdown-body h2 {
  font-size: 1.3em;
}
.czn-chat-markdown-body h3 {
  font-size: 1.15em;
}
.czn-chat-markdown-body h4 {
  font-size: 1em;
}
.czn-chat-markdown-body h5 {
  font-size: 0.9em;
}
.czn-chat-markdown-body h6 {
  font-size: 0.85em;
}

.czn-chat-markdown-body p {
  margin-bottom: 0.6em;
}

.czn-chat-markdown-body blockquote {
  border-left: 3px solid var(--czn-chat-border);
  padding-left: 1em;
  margin-left: 0;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: var(--czn-chat-text-light);
  font-style: italic;
}

.czn-chat-markdown-body code {
  background-color: var(--czn-chat-border);
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  border-radius: 3px;
  font-family: monospace;
}

.czn-chat-markdown-body pre {
  background-color: var(--czn-chat-border);
  padding: 1em;
  overflow: auto;
  border-radius: 6px;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.czn-chat-markdown-body pre code {
  padding: 0;
  margin: 0;
  font-size: inherit;
  background-color: transparent;
  border-radius: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.czn-chat-markdown-body hr {
  border: none;
  border-top: 1px solid var(--czn-chat-border);
  margin-top: 1em;
  margin-bottom: 1em;
}

.czn-chat-markdown-body a {
  color: var(--czn-chat-primary);
  text-decoration: none;
}
.czn-chat-markdown-body a:hover {
  text-decoration: underline;
}

.czn-chat-markdown-body table {
  border-collapse: collapse;
  margin: 1em 0;
  display: block;
  overflow-x: auto;
}
.czn-chat-markdown-body th,
.czn-chat-markdown-body td {
  border: 1px solid var(--czn-chat-border);
  padding: 0.5em 0.8em;
}
.czn-chat-markdown-body th {
  background-color: var(--czn-chat-bg);
  font-weight: bold;
}

.czn-chat-input {
  min-height: 40px;
  max-height: 300px;
  overflow-y: auto;
  resize: none;
  box-sizing: border-box;
  width: 100%;
  font-size: 1rem;
  line-height: 1.4;
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid var(--czn-chat-border);
  transition: border-color 0.2s;
  background: var(--czn-chat-card);
  color: var(--czn-chat-text);
  font-family: inherit;
}

.czn-chat-input:focus {
  border-color: var(--czn-chat-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.czn-chat-input::placeholder {
  color: var(--czn-chat-text-light);
  opacity: 0.7;
}

.czn-chat-hint {
  background: linear-gradient(
    135deg,
    var(--czn-chat-primary),
    var(--czn-chat-secondary)
  );
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  pointer-events: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  order: 1;
  position: relative;
  overflow: hidden;
}

.czn-chat-container:hover .czn-chat-hint {
  background: linear-gradient(
    135deg,
    var(--czn-chat-primary-dark),
    var(--czn-chat-secondary)
  );
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.czn-chat-container.czn-chat-initial-hint .czn-chat-hint {
  opacity: 0;
  transform: translateY(10px);
}

.czn-chat-container.czn-chat-initial-hint:hover .czn-chat-hint {
  opacity: 1;
  transform: translateY(0);
  animation: czn-chat-gradientPulse 1.5s ease infinite;
  background-size: 400% 400%;
  background-image: linear-gradient(
    90deg,
    var(--czn-chat-error),
    var(--czn-chat-primary),
    var(--czn-chat-secondary),
    var(--czn-chat-success)
  );
}

.czn-chat-hint::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  animation: czn-chat-shine 5s infinite ease-in-out;
  animation-delay: 2.7s;
}

@keyframes czn-chat-gradientPulse {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes czn-chat-gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes czn-chat-shine {
  0% {
    left: -100%;
  }
  20% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes czn-chat-rotate {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes czn-chat-pulseMic {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes czn-chat-ring-ring {
  0% {
    transform: rotate(0);
  }
  5% {
    transform: rotate(15deg);
  }
  15% {
    transform: rotate(-10deg);
  }
  25% {
    transform: rotate(8deg);
  }
  35% {
    transform: rotate(-5deg);
  }
  45% {
    transform: rotate(2deg);
  }
  50% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(0);
  }
}

@keyframes czn-chat-avatarPop {
  0% {
    transform: scale(0.7);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes czn-chat-pulse-border {
  0% {
    transform: scale(1);
    opacity: 0.7;
    border-color: var(--czn-chat-primary);
  }
  70% {
    transform: scale(1.25);
    opacity: 0;
    border-color: rgba(72, 149, 239, 0);
  }
  100% {
    transform: scale(1.25);
    opacity: 0;
  }
}

@keyframes czn-chat-fadeInStatus {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 0.9;
    transform: translateY(0);
  }
}

@keyframes czn-chat-fadeInOut {
  0% {
    opacity: 0;
    transform: translate(-50%, 10px) scale(0.9);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, 0) scale(1);
  }
  80% {
    opacity: 1;
    transform: translate(-50%, 0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -10px) scale(0.9);
  }
}

@keyframes czn-chat-fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes czn-chat-messageAppear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes czn-chat-blink {
  from,
  to {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.czn-chat-button-wrapper {
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  width: auto;
  height: auto;
}

.czn-chat-button {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: transparent;
  color: var(--czn-chat-primary);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.czn-chat-button i {
  background: linear-gradient(
    135deg,
    var(--czn-chat-primary),
    var(--czn-chat-secondary)
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
}

.czn-chat-button:hover {
  transform: scale(1.1);
}

.czn-chat-button:active {
  transform: scale(0.95);
}

.czn-chat-window {
  position: absolute;
  bottom: calc(100% + 10px);
  right: 0;
  width: calc(100vw - 20px);
  max-width: 370px;
  height: calc(90vh - 50px);
  max-height: 601px;
  background: var(--czn-chat-card);
  border-radius: 1rem;
  border: 1px solid var(--czn-chat-border);
  box-shadow: var(--czn-chat-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px) scale(0.97);
  transform-origin: bottom right;
  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.35s cubic-bezier(0.4, 0, 0.2, 1), visibility 0s linear 0.35s;
  z-index: 9999;
}

.czn-chat-window.czn-chat-active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.35s cubic-bezier(0.4, 0, 0.2, 1), visibility 0s linear 0s;
  border: 1px solid var(--czn-chat-border);
}

.czn-chat-header {
  padding: 9px 4px 9px 18px;
  background: linear-gradient(
    90deg,
    var(--czn-chat-primary),
    var(--czn-chat-secondary)
  );
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
  cursor: default;
  border: none;
}

.czn-chat-actions {
  display: flex;
  gap: 4px;
}

.czn-chat-action-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  padding: 0.5rem;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.czn-chat-action-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.czn-chat-sessions {
  padding: 0;
  border-bottom: 1px solid var(--czn-chat-border);
  max-height: 50vh;
  overflow: hidden;
}

.czn-chat-sessions-container {
  display: block;
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease, padding 0.3s ease;
  padding: 0 1rem;
  will-change: max-height;
}

.czn-chat-sessions-container.czn-chat-active {
  max-height: 300px;
  padding: 10px 1rem;
}

.czn-chat-sessions-header {
  padding: 0.5rem 1rem;
  cursor: pointer;
  border-bottom: none;
}

.czn-chat-sessions-toggle-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--czn-chat-text);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
}

.czn-chat-sessions-toggle-link:hover {
  color: var(--czn-chat-primary);
}

.czn-chat-sessions-toggle-link i {
  font-size: 0.8rem;
  transition: transform 0.2s;
  margin-left: auto;
  display: flex;
  align-items: center;
}

.czn-chat-sessions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.czn-chat-sessions-list h3 {
  display: none;
}

.czn-chat-sessions-list li {
  border-radius: 6px;
  margin-bottom: 0.25rem;
}

.czn-chat-session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s, padding-left 0.2s;
  width: 100%;
  position: relative;
}

.czn-chat-session-item:hover {
  background-color: var(--czn-chat-border);
}

.czn-chat-session-item.czn-chat-active {
  background-color: rgba(67, 97, 238, 0.15);
  padding-left: 14px;
}

.czn-chat-session-name {
  flex: 1;
  padding-right: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle;
}

.czn-chat-session-edit-input {
  position: relative;
  width: calc(100% - 5rem);
  height: 100%;
  padding: 0.5rem 0.5rem;
  margin-right: 0.5rem;
  border: none;
  background: var(--czn-chat-card);
  color: var(--czn-chat-text);
  border-radius: 0.5rem;
  display: none;
  flex: 1;
  min-width: 0;
}

.czn-chat-session-item.czn-chat-editing .czn-chat-session-name {
  display: none;
}

.czn-chat-session-item.czn-chat-editing .czn-chat-session-edit-input {
  display: block;
}

.czn-chat-edit-session-btn {
  background: none;
  border: none;
  color: var(--czn-chat-text-light);
  cursor: pointer;
  padding: 0.25rem;
  margin-left: 0.5rem;
  transition: color 0.2s;
}
.czn-chat-edit-session-btn:hover {
  color: var(--czn-chat-primary);
}
.czn-chat-edit-session-btn.czn-chat-confirm {
  color: var(--czn-chat-success);
}

.czn-chat-delete-session-btn {
  background: none;
  border: none;
  color: var(--czn-chat-text-light);
  cursor: pointer;
  padding: 0.25rem;
  margin-left: 0.5rem;
  font-size: 0.8rem;
}

.czn-chat-delete-session-btn:hover {
  color: var(--czn-chat-error);
}

.czn-chat-title {
  font-weight: 600;
  font-size: 1rem;
}

.czn-chat-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
}

.czn-chat-messages {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
  max-height: calc(90vh - 200px);
  scrollbar-width: thin;
  scrollbar-color: var(--czn-chat-primary) var(--czn-chat-border);
  cursor: default;
}

.czn-chat-messages::-webkit-scrollbar {
  width: 8px;
}
.czn-chat-messages::-webkit-scrollbar-track {
  background: var(--czn-chat-border);
  border-radius: 10px;
  margin: 5px 0;
}
.czn-chat-messages::-webkit-scrollbar-thumb {
  background-color: var(--czn-chat-primary);
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: background-color 0.3s;
}
.czn-chat-messages::-webkit-scrollbar-thumb:hover {
  background-color: var(--czn-chat-secondary);
}

.czn-chat-message {
  max-width: 85%;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  font-size: 0.9rem;
  line-height: 1.4;
  position: relative;
  transition: all 0.3s ease;
  word-wrap: break-word;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  animation: czn-chat-messageAppear 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  opacity: 0;
  transform: translateY(10px);
}

.czn-chat-message-user {
  align-self: flex-end;
  min-width: 103px;
  background: linear-gradient(
    135deg,
    var(--czn-chat-primary),
    var(--czn-chat-secondary)
  );
  color: white;
  border-bottom-right-radius: 0.25rem;
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.2);
}

.czn-chat-message-user::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border-radius: 1rem;
  z-index: -1;
}

.czn-chat-message-assistant {
  align-self: flex-start;
  background: var(--czn-chat-card);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid var(--czn-chat-border);
  color: var(--czn-chat-text);
  border-bottom-left-radius: 0.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.czn-chat-message-assistant:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.czn-chat-message:nth-child(1) {
  animation-delay: 0.1s;
}
.czn-chat-message:nth-child(2) {
  animation-delay: 0.2s;
}
.czn-chat-message:nth-child(3) {
  animation-delay: 0.3s;
}
.czn-chat-message:nth-child(4) {
  animation-delay: 0.4s;
}

.czn-chat-message-body {
  width: 100%;
}

.czn-chat-message-content {
  flex-grow: 1;
  line-height: 1.4;
  overflow-wrap: break-word;
}

.czn-chat-assistant-reply {
  display: block;
  width: 100%;
  padding: 5px 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: var(--czn-chat-text-light);
  font-style: italic;
}

.czn-chat-message-footer {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-top: 4px;
  margin-top: 4px;
}

.czn-chat-message-actions {
  display: flex;
  align-items: center;
  opacity: 0.6;
  transition: opacity 0.2s ease-in-out;
  gap: 5px;
}

.czn-chat-message:hover .czn-chat-message-actions {
  opacity: 1;
}

.czn-chat-message-actions button {
  background: none;
  border: none;
  color: var(--czn-chat-text-light);
  cursor: pointer;
  padding: 2px 4px;
  font-size: 10px;
  line-height: 1;
  z-index: 1;
}

.czn-chat-message-actions button:hover {
  color: var(--czn-chat-primary);
}

.czn-chat-message-user .czn-chat-message-actions button {
  color: rgba(255, 255, 255, 0.6);
}

.czn-chat-message-user .czn-chat-message-actions button:hover {
  color: rgba(255, 255, 255, 1);
}

.czn-chat-message-time {
  color: var(--czn-chat-text-light);
  white-space: nowrap;
  line-height: 1.4;
  font-size: 9px;
  align-self: flex-end;
}

.czn-chat-message-user .czn-chat-message-time {
  color: rgba(255, 255, 255, 0.6);
}

.czn-chat-message-edit-textarea {
  flex-grow: 1;
  min-height: 40px;
  padding: 8px;
  border-radius: 8px;
  border: 1px solid var(--czn-chat-border);
  resize: vertical;
  font-family: inherit;
  font-size: inherit;
  line-height: 1.4;
  width: 100%;
  box-sizing: border-box;
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--czn-chat-card);
}
[data-theme="dark"] .czn-chat-message-edit-textarea {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--czn-chat-text);
  border: 1px solid var(--czn-chat-border);
}

.czn-chat-message-edit-textarea:focus {
  outline: none;
  background-color: rgba(0, 0, 0, 0.08);
  border-color: var(--czn-chat-card);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}
[data-theme="dark"] .czn-chat-message-edit-textarea:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: var(--czn-chat-primary);
}

.czn-chat-message.czn-chat-editing .czn-chat-message-actions button {
  color: var(--czn-chat-card);
}
[data-theme="dark"]
  .czn-chat-message.czn-chat-editing
  .czn-chat-message-actions
  button {
  color: var(--czn-chat-text);
}

.czn-chat-copy-notification {
  position: fixed;
  background: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  z-index: 10001;
  font-size: 0.85em;
  animation: czn-chat-fadeInOut 1.5s ease-in-out;
  white-space: nowrap;
  pointer-events: none;
}

.czn-chat-typing-indicator {
  display: none;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  align-self: flex-start;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 1.5rem;
  margin-left: 0.5rem;
  margin-bottom: 0.5rem;
  opacity: 0;
  transform: translateY(10px);
  animation: czn-chat-fadeIn 0.3s ease-out forwards;
}

.czn-chat-typing-dot {
  width: 10px;
  height: 10px;
  background-color: var(--czn-chat-primary);
  border-radius: 50%;
  animation: czn-chat-typingAnimation 1.6s infinite ease-in-out;
  position: relative;
}

.czn-chat-typing-dot::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 14px;
  height: 14px;
  background: rgba(67, 97, 238, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.czn-chat-typing-dot:nth-child(1) {
  animation-delay: 0s;
}
.czn-chat-typing-dot:nth-child(2) {
  animation-delay: 0.3s;
}
.czn-chat-typing-dot:nth-child(3) {
  animation-delay: 0.6s;
}

.czn-chat-input-container {
  padding: 1rem;
  padding-right: 68px;
  border-top: 1px solid var(--czn-chat-border);
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
  position: relative;
  margin-top: auto;
}

.czn-chat-window .czn-chat-input {
  flex: 1;
  padding: 0.75rem 1rem;
  padding-right: 32px;
  padding-top: 8px;
  border-radius: 0.75rem;
  background: var(--czn-chat-bg);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  color: var(--czn-chat-text);
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.czn-chat-window .czn-chat-input:focus {
  outline: none;
  background: rgb(0 0 0 / 1%);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.3);
}
[data-theme="dark"] .czn-chat-window .czn-chat-input {
  background: rgba(255, 255, 255, 0.1);
}
[data-theme="dark"] .czn-chat-window .czn-chat-input:focus {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(72, 149, 239, 0.3);
}

.czn-chat-send-btn {
  position: absolute;
  right: 1rem;
  bottom: 1rem;
  min-width: 40px;
  height: 40px;
  padding: 0 15px;
  background: linear-gradient(
    135deg,
    var(--czn-chat-primary),
    var(--czn-chat-secondary)
  );
  color: white;
  border: none;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.czn-chat-send-btn .czn-chat-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: czn-chat-rotate 0.8s linear infinite;
  display: none;
}

.czn-chat-send-btn.czn-chat-pending-ansv i {
  display: none;
}
.czn-chat-send-btn.czn-chat-pending-ansv .czn-chat-loader {
  display: block;
}

.czn-chat-send-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.czn-chat-send-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.czn-chat-send-btn:hover::before {
  left: 100%;
}

.czn-chat-send-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(67, 97, 238, 0.3);
}

.czn-chat-send-btn:disabled {
  opacity: 0.89;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
.czn-chat-send-btn:disabled::before {
  display: none;
}

.czn-chat-mic-button {
  width: 37px;
  height: 37px;
  position: absolute;
  right: calc(1rem + 46px + 0.5rem);
  bottom: calc(1rem + (40px - 37px) / 2);
  border: 1px solid transparent;
  border-radius: 50%;
  background-color: transparent;
  color: var(--czn-chat-text-light);
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  transition: color 0.2s, border-color 0.2s;
}

.czn-chat-mic-hint {
  position: absolute;
  bottom: calc(100% + 8px);
  left: 0px;
  right: 0px;
  margin: 0 auto;
  width: 86%;
  /* transform: translateX(-50%); */
  background-color: var(--czn-chat-text);
  color: var(--czn-chat-bg);
  padding: 0.5rem 0.8rem;
  border-radius: 0.5rem;
  font-size: 0.85rem;
  white-space: nowrap;
  box-shadow: var(--czn-chat-shadow);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  pointer-events: none;
  z-index: 3;
}

.czn-chat-mic-button:hover + .czn-chat-mic-hint,
.czn-chat-mic-button.czn-chat-recording + .czn-chat-mic-hint {
  opacity: 1;
  visibility: visible;
}

.czn-chat-mic-button.czn-chat-recording {
  color: var(--czn-chat-error);
  border: 1px solid var(--czn-chat-error);
  animation: czn-chat-pulseMic 1.5s infinite ease-in-out;
}

.czn-chat-call-ai-btn-wrapper {
  position: absolute;
  width: 100%;
  right: 0;
  top: -52px;
  z-index: 101;
  display: flex;
  justify-content: flex-end;
  padding-right: 10px;
  pointer-events: none;
}

.czn-chat-call-ai-btn {
  pointer-events: auto;
  width: 100%;
  background: linear-gradient(
    135deg,
    var(--czn-chat-error-gentle),
    var(--czn-chat-error-gentle-dark)
  );
  color: white;
  padding: 10px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
}

.czn-chat-call-ai-btn.czn-chat-active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.czn-chat-call-ai-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.czn-chat-call-ai-btn .czn-chat-phone-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20.01 15.38c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z'/%3E%3C/svg%3E");
  background-size: contain;
  animation: czn-chat-ring-ring 1.5s ease-in-out infinite;
}

.czn-chat-call-ai-btn > span {
}

.czn-chat-modal {
  display: none;
  position: fixed;
  z-index: 100002;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.55);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  padding-top: 60px;
  box-sizing: border-box;
  font-family: var(--chat-font);
}

.czn-chat-modal.czn-chat-show {
  display: block;
  opacity: 1;
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.czn-chat-modal.czn-chat-show::-webkit-scrollbar {
  display: none;
}

.czn-chat-modal-content {
  margin: auto;
  padding: 52px 2rem 2rem 2rem;
  max-width: 359px !important;
  width: calc(100% - 20px);
  min-height: 465px;
  border-radius: 1.25rem;
  position: relative;
  box-sizing: border-box;
  box-shadow: var(--czn-chat-shadow-soft);
  transform: scale(0.97) translateY(15px);
  opacity: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--czn-chat-card);
  gap: 0rem;
  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

.czn-chat-modal.czn-chat-show .czn-chat-modal-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.czn-chat-modal-header {
  position: absolute;
  top: 0px;
  left: 0;
  width: 100%;
  padding: 7px 7px 20px 18px;

  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 1.25rem 1.25rem 0 0;
  min-height: 52px;
  overflow: hidden;
  box-sizing: border-box;
  border-bottom: none;
}

.czn-chat-modal-header::after {
  display: none;
}

.czn-chat-modal-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--czn-chat-text-light);
  margin: 0;
  letter-spacing: 0.01em;
  text-shadow: none;
}

.czn-chat-modal-actions {
  display: flex;
  gap: 0px;
  align-items: center;
  color: var(--czn-chat-text-light);
}

.czn-chat-modal-actions > button {
  color: var(--czn-chat-text-light);
  transition: all 0.3s ease;
}

.czn-chat-refresh-btn {
  transition: transform 0.4s ease-in-out;
}

.czn-chat-refresh-btn:hover {
  color: var(--czn-chat-primary);
}

.czn-chat-refresh-btn:active {
  transform: rotate(360deg);
}

@keyframes czn-chat-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.czn-chat-refresh-btn.czn-chat-refreshing i {
  animation: czn-chat-spin 1s linear infinite;
}

.czn-chat-close-modal-btn {
  font-size: 1.5rem;
}

.czn-chat-chat-modal-btn i,
.czn-chat-close-modal-btn i {
  font-size: 1.1rem;
}

.czn-chat-modal-header::before {
  content: "";
  position: absolute;
  width: 88%;
  top: 50px;
  left: 0px;
  right: 0px;
  margin: 0 auto;
  height: 1px;
  background: var(--czn-chat-text-light);
}

.czn-chat-avatar {
  position: relative;
  width: 129px;
  height: 129px;
  margin: 2.5rem auto 0.5rem auto;
  border: 2px solid var(--czn-chat-text-light);
  border-radius: 50%;

  padding: 5px;
  box-sizing: border-box;
  box-shadow: 0 5px 18px 0 rgba(67, 97, 238, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.2s;
  animation: czn-chat-avatarPop 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.czn-chat-avatar img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.1);
}

.czn-chat-voice-animation {
  position: absolute;
  top: -4px;
  left: -4px;
  width: calc(100% + 8px);
  height: calc(100% + 8px);
  border-radius: 50%;
  border: 2.5px solid transparent;
  box-sizing: border-box;
  opacity: 0;
  pointer-events: none;
}

.czn-chat-avatar.czn-chat-is-speaking .czn-chat-voice-animation {
  opacity: 1;
  animation: czn-chat-pulse-border 1.7s infinite ease-out;
}

.czn-chat-voice-status {
  position: relative;
  font-weight: 500;
  min-height: 1.5em;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: var(--czn-chat-text);
  opacity: 0.9;
  letter-spacing: 0.01em;
  text-align: center;
  transition: color 0.3s, opacity 0.3s;
  animation: czn-chat-fadeInStatus 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.czn-chat-stop-bot-btn {
  position: absolute;
  top: 296px;
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--czn-chat-text-light);
  text-align: center;
  padding: 8px;
  width: 70px;
  height: 70px;
  transition: color 0.2s ease;
  z-index: 1;
  margin-top: -0.8rem;
  margin-bottom: 0.8rem;
}

.czn-chat-stop-bot-btn i {
  font-size: 1.6em;
  display: block;
  margin-bottom: 3px;
}

.czn-chat-stop-bot-btn span {
  font-size: 0.8em;
  display: block;
}

.czn-chat-stop-bot-btn:hover {
  color: var(--czn-chat-primary);
}

.czn-chat-end-call-btn {
  background: linear-gradient(
    135deg,
    var(--czn-chat-error-gentle),
    var(--czn-chat-error-gentle-dark)
  );
  color: #fff;
  border: none;
  border-radius: 25px;
  width: auto;
  min-width: 140px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0 20px;
  box-shadow: 0 3px 10px rgba(255, 127, 80, 0.3);
  margin: 0px auto 0 auto;
  transition: background-color 0.2s, box-shadow 0.2s, transform 0.1s ease-out;
}

[data-theme="dark"] .czn-chat-end-call-btn {
  box-shadow: 0 3px 10px rgba(240, 128, 128, 0.3);
}

.czn-chat-end-call-btn:hover {
  background-color: var(--czn-chat-error-gentle-dark);
  box-shadow: 0 5px 15px rgba(255, 127, 80, 0.4);
  transform: translateY(-1px);
}
[data-theme="dark"] .czn-chat-end-call-btn:hover {
  box-shadow: 0 5px 15px rgba(240, 128, 128, 0.4);
}

.czn-chat-end-call-btn:active {
  transform: translateY(0px);
  box-shadow: 0 2px 8px rgba(255, 127, 80, 0.3);
}

.czn-chat-end-call-btn .czn-chat-phone-icon {
  margin-left: 5px;
  order: 2;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20.01 15.38c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z'/%3E%3C/svg%3E");
  animation: czn-chat-ring-ring 1.5s ease-in-out infinite;
  display: inline-block;
  width: 16px;
  height: 16px;
  background-size: contain;
}

@media (max-width: 768px) {
  .czn-chat-window {
    height: calc(
      100vh - 20px - env(safe-area-inset-bottom) - env(safe-area-inset-top)
    );
    max-height: 100%;
    bottom: 0 !important;
    right: 10px;
    position: fixed;
    width: calc(100% - 20px);
    max-width: 368px;
    border-radius: 1rem 1rem 1rem 1rem;
    transform-origin: bottom center;
  }
  .czn-chat-call-ai-btn-wrapper {
    position: absolute;
    right: 60px;
    top: 7px;
    width: 100%;
    min-width: 188px;
    z-index: 101;
  }
  .czn-chat-hint {
    display: none;
  }
}

@media (max-width: 450px) {
  .czn-chat-modal-content {
    width: 95%;
    padding: 50px 15px 20px 15px;
    max-width: none;
  }
  .czn-chat-modal-title {
    font-size: 1.1em;
  }
  .czn-chat-avatar {
    width: 100px;
    height: 100px;
    padding: 5px;
  }
  .czn-chat-voice-animation {
    top: -3px;
    left: -3px;
    width: calc(100% + 6px);
    height: calc(100% + 6px);
    border-width: 2px;
  }
  .czn-chat-end-call-btn {
    height: 40px;
    font-size: 0.95rem;
    padding: 0 15px;
    min-width: 120px;
    margin-top: 0.8rem;
  }
  .czn-chat-stop-bot-btn {
    margin-top: -0.5rem;
    margin-bottom: 0.6rem;
  }
  .czn-chat-voice-status {
    margin-bottom: 1rem;
    font-size: 1rem;
  }
}

.czn-chat-message-assistant .czn-chat-typing-active {
  position: relative;
}
.czn-chat-message-assistant .czn-chat-typing-cursor {
  display: inline-block;
  width: 8px;
  height: 1.1em;
  background-color: var(--czn-chat-text);
  margin-left: 2px;
  opacity: 0;
  animation: czn-chat-blink 1s step-end infinite;
  vertical-align: text-bottom;
}
.czn-chat-message-assistant.czn-chat-cursor-visible .czn-chat-typing-cursor {
  opacity: 1;
}

.czn-chat-message-assistant .czn-chat-message-content {
  transition: opacity 0.1s ease;
}
.czn-chat-message-assistant .czn-chat-message-content.czn-chat-updating {
  opacity: 0.7;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6,
.markdown-content p,
.markdown-content ul,
.markdown-content ol,
.markdown-content blockquote,
.markdown-content pre,
.markdown-content hr {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 0;
}

div.message-content > div > pre > code > h1 {
  word-wrap: break-word;
  margin: 0;
  white-space: normal;
}

div.message-content > div > pre > code {
  white-space: normal;
}

.markdown-body {
  overflow: hidden;
}

.czn-chat-action-control {
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  margin-top: -0.5rem;
  margin-bottom: 1rem;
  border-radius: 12px;
  min-height: 60px;
  transition: background-color 0.2s, color 0.2s;
  cursor: default;
  width: 100%;
  max-width: 200px;
  margin-left: auto;
  margin-right: auto;
}

.czn-chat-action-icon {
  width: 60px;
  height: 60px;
  background-color: var(--czn-chat-text-light);
  /* mask-image будет установлен динамически через JavaScript */
  mask-size: contain;
  mask-repeat: no-repeat;
  mask-position: center;
  transition: background-color 0.2s ease, mask-image 0.1s step-end;
  margin-bottom: 5px;
}

.czn-chat-action-text {
  font-size: 0.9em;
  color: var(--czn-chat-text-light);
  margin: 0;
  transition: color 0.2s ease;
  text-align: center;
  font-weight: 500;
}

.czn-chat-action-control.czn-chat-interrupt {
  cursor: pointer;
}
.czn-chat-action-control.czn-chat-interrupt:hover {
  background-color: rgba(0, 0, 0, 0.06);
}
[data-theme="dark"] .czn-chat-action-control.czn-chat-interrupt {
  background-color: rgba(255, 255, 255, 0.05);
}
[data-theme="dark"] .czn-chat-action-control.czn-chat-interrupt:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.czn-chat-action-control.czn-chat-interrupt .czn-chat-action-icon {
  background-color: var(--czn-chat-error-gentle) !important;
  /* mask-image будет установлен динамически через JavaScript */
}

.czn-chat-action-control.czn-chat-interrupt .czn-chat-action-text {
  color: var(--czn-chat-error-gentle) !important;
}

.czn-chat-mic-button:focus,
.czn-chat-mic-button:active {
  outline: none;
  box-shadow: none;
}

.czn-chat-mic-button.czn-chat-recording {
  color: var(--czn-chat-error);
  border: 1px solid var(--czn-chat-error);
  animation: czn-chat-pulseMic 1.5s infinite ease-in-out;

  outline: none;
  box-shadow: none;
}

@keyframes czn-chat-pulseMic {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.czn-chat-typing-indicator {
  display: none;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  align-self: flex-start;
  background-color: var(--czn-chat-border);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 1.5rem;
  margin-left: 1rem;
  margin-bottom: 0.75rem;
  opacity: 0;
  transform: translateY(10px);
  animation: czn-chat-fadeIn 0.3s ease-out forwards;
}

.czn-chat-typing-dot {
  width: 8px;
  height: 8px;
  background-color: var(--czn-chat-primary);
  border-radius: 50%;
  animation: czn-chat-typingAnimation 1.6s infinite ease-in-out;
  position: relative;
}

.czn-chat-typing-dot:nth-child(1) {
  animation-delay: 0s;
}
.czn-chat-typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.czn-chat-typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes czn-chat-typingAnimation {
  0%,
  60%,
  100% {
    transform: scale(0.8);
    background-color: var(--czn-chat-text-light);
    opacity: 0.6;
  }
  30% {
    transform: scale(1.1);
    background-color: var(--czn-chat-primary);
    opacity: 1;
  }
}

@keyframes czn-chat-fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.czn-chat-refresh-btn {
  transition: transform 0.4s ease-in-out;
}

.czn-chat-refresh-btn:active {
  transform: rotate(360deg);
}

/* Add spin animation for the refresh icon */
@keyframes czn-chat-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.czn-chat-refresh-btn.czn-chat-refreshing i {
  animation: czn-chat-spin 1s linear infinite;
}

.czn-chat-title {
  line-height: 1.1;
}

.czn-chat-window.czn-chat-active {
  border: none;
}

/* Стили пагинации для админ-панели */
.pagination-nav-simple {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.pagination-simple {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 4px;
  align-items: center;
}

.page-item-simple {
  display: flex;
}

.page-link-simple {
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  color: #007bff;
  text-decoration: none;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.page-link-simple:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #0056b3;
  text-decoration: none;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Активная страница пагинации - более яркая и заметная */
.page-item-simple.active .page-link-simple,
.page-link-simple.active {
  background: linear-gradient(135deg, #007bff, #0056b3) !important;
  color: #ffffff !important;
  border-color: #007bff !important;
  font-weight: bold !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
  transform: scale(1.1) !important;
  position: relative !important;
  z-index: 2 !important;
}

/* Дополнительный эффект для активной кнопки */
.page-item-simple.active .page-link-simple::before,
.page-link-simple.active::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 6px;
  z-index: -1;
  opacity: 0.3;
  animation: pulse-active 2s infinite;
}

@keyframes pulse-active {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

/* Убираем эффекты при наведении на активную кнопку */
.page-item-simple.active .page-link-simple:hover,
.page-link-simple.active:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
}

.page-item-simple.disabled .page-link-simple {
  color: #6c757d;
  background: #ffffff;
  border-color: #dee2e6;
  cursor: default;
}

.page-item-simple.disabled .page-link-simple:hover {
  background: #ffffff;
  border-color: #dee2e6;
  color: #6c757d;
  transform: none;
  box-shadow: none;
}

/* Стили для лейблов цифровых данных */
.digital-data-label {
  display: inline-block;
  padding: 4px 8px;
  margin: 2px 4px 2px 0;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Лейбл для телефонов - крупный и зеленый */
.digital-data-label.phones {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  font-size: 14px !important;
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
}

.digital-data-label.phones:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

/* Лейблы для других типов данных */
.digital-data-label.numbers {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.digital-data-label.codes {
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
}

.digital-data-label.dates {
  background: linear-gradient(135deg, #fd7e14, #e8590c);
  color: white;
}

.digital-data-label.time {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: white;
}

.digital-data-label.money {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
}

/* Эффект при наведении на лейблы */
.digital-data-label:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

/* Совместимость со старыми классами */
.data-badge-compact {
  display: inline-block;
  padding: 4px 8px;
  margin: 2px 4px 2px 0;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-badge-success {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  font-size: 14px !important;
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
}

.data-badge-primary {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.data-badge-warning {
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
}

.data-badge-info {
  background: linear-gradient(135deg, #fd7e14, #e8590c);
  color: white;
}

.data-badge-secondary {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: white;
}

.data-badge-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

/* Стили для AJAX лоадера */
.pagination-loader {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.pagination-loader.show {
  display: flex;
  align-items: center;
  gap: 15px;
}

.loader-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loader-text {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

/* Overlay для лоадера */
.pagination-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 9998;
}

.pagination-overlay.show {
  display: block;
}

/* Стили для контейнера с данными во время загрузки */
.phone-chats-container.loading {
  opacity: 0.6;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

/* Улучшенные стили для активной пагинации */
.page-link-simple.active-current,
.page-item-simple.active .page-link-simple {
  background: linear-gradient(135deg, #007bff, #0056b3) !important;
  color: #ffffff !important;
  border-color: #007bff !important;
  font-weight: bold !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
  transform: scale(1.05) !important;
  position: relative !important;
  z-index: 2 !important;
}

.page-link-simple.active-current:hover,
.page-item-simple.active .page-link-simple:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4) !important;
  background: linear-gradient(135deg, #0056b3, #004085) !important;
}

/* Компактные карточки */
.chat-card-compact {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.chat-card-compact:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.card-header-compact {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 1rem;
  border: none;
}

.card-body-compact {
  padding: 1rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-footer-compact {
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.chat-title-compact {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  color: white;
}

.chat-meta-compact {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.7rem;
}

.messages-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
}

.date-info-compact {
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 8px;
  border-left: 3px solid #2196f3;
}

.data-compact-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
}

.data-badge-compact {
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
}

.action-buttons-compact {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.btn-compact {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.75rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  flex: 1;
}

.btn-view-compact {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.btn-view-compact:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  color: white;
  text-decoration: none;
}

.btn-delete-compact {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.btn-delete-compact:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
  color: white;
}
