<?php

namespace App\Services;

use App\Models\Settings;

class MistralApiService {
    // Добавляем константу для URL по умолчанию
    private const DEFAULT_MISTRAL_API_URL = 'https://api.mistral.ai/v1/chat/completions';

    private $apiKey;
    private $model;
    private $temperature;
    private $maxTokens; // Это поле теперь будет специфичным для Mistral
    private $apiUrl; // URL будет загружаться из настроек

    public function __construct() {
        $settingsModel = new Settings();
        $settings = $settingsModel->getSettings();

        if (!$settings || empty($settings['api_key']) || empty($settings['api_model']) || !$settings['is_active']) {
            error_log("Mistral API Service initialized but API is inactive or not configured.");
            $this->apiKey = null;
        } else {
            $this->apiKey = $settings['api_key'];
            $this->model = $settings['api_model'];
            $this->temperature = (float)($settings['temperature'] ?? 0.7);
            // Используем mistral_max_tokens из настроек, если есть, иначе значение по умолчанию
            $this->maxTokens = (int)($settings['mistral_max_tokens'] ?? 2048);
            // Используем mistral_api_url из настроек, если есть и не пустое, иначе URL по умолчанию из константы
            $this->apiUrl = !empty($settings['mistral_api_url']) ? trim($settings['mistral_api_url']) : self::DEFAULT_MISTRAL_API_URL;
        }
    }

    public function isConfigured(): bool {
        return !empty($this->apiKey) && !empty($this->model);
    }

    // Добавляем метод для получения URL по умолчанию
    public static function getDefaultApiUrl(): string {
        return self::DEFAULT_MISTRAL_API_URL;
    }

    public function getChatCompletion(array $messages): ?string {
        if (!$this->isConfigured()) {
            throw new \Exception('Mistral API Service is not configured or inactive.');
        }

        $payload = json_encode([
            'model' => $this->model,
            'messages' => $messages,
            'temperature' => $this->temperature,
            'max_tokens' => $this->maxTokens // Добавляем max_tokens в запрос
        ]);

        $ch = curl_init($this->apiUrl); // Используем URL из конструктора
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ],
            CURLOPT_TIMEOUT => 60
        ]);

        $responseRaw = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            throw new \Exception('Curl error calling Mistral API: ' . $curlError);
        }

        $responseData = json_decode($responseRaw, true);

        if ($httpCode !== 200) {
            $errorMessage = $responseData['error']['message'] ?? $responseData['message'] ?? 'Unknown API error';
            throw new \Exception('Mistral API error (HTTP ' . $httpCode . '): ' . $errorMessage);
        }

        $reply = $responseData['choices'][0]['message']['content'] ?? null;

        if ($reply === null) {
            error_log("Mistral API response missing content. Full response: " . $responseRaw);
            throw new \Exception('Mistral API did not return content in the expected format.');
        }

        return $reply;
    }

    /**
     * Выполняет стриминговый запрос к Mistral API и возвращает генератор событий.
     * События: ['type' => 'chunk', 'data' => string], ['type' => 'done'], ['type' => 'error', 'message' => string]
     *
     * @param array $payloadData Данные для payload (model, messages, temperature, etc., stream=true будет добавлен).
     * @return \Generator
     * @throws \Exception Если API не настроен или произошла ошибка cURL до начала стриминга.
     */
    public function streamChatCompletion(array $payloadData): \Generator {
        if (!$this->isConfigured()) {
            throw new \Exception('Mistral API Service is not configured or inactive.');
        }

        // Ensure stream parameter is set
        $payloadData['stream'] = true;
        // Use service's configured model and temperature if not provided in payload
        if (!isset($payloadData['model'])) {
            $payloadData['model'] = $this->model;
        }
        if (!isset($payloadData['temperature'])) {
             $payloadData['temperature'] = $this->temperature;
        }
         // Используем maxTokens из конструктора (уже специфичный для Mistral)
         if (!isset($payloadData['max_tokens'])) {
             $payloadData['max_tokens'] = $this->maxTokens;
         }


        $payload = json_encode($payloadData);
        if (json_last_error() !== JSON_ERROR_NONE) {
             throw new \Exception('Failed to encode payload for Mistral API: ' . json_last_error_msg());
        }

        $ch = curl_init($this->apiUrl); // Используем URL из конструктора
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: text/event-stream',
                'Authorization: Bearer ' . $this->apiKey
            ],
            CURLOPT_WRITEFUNCTION => function ($curl, $chunk) use (&$buffer) {
                // Append chunk to buffer for processing by the generator
                $buffer .= $chunk;
                return strlen($chunk); // Indicate chunk was processed
            },
            CURLOPT_TIMEOUT => 0, // No timeout for the stream itself
            CURLOPT_CONNECTTIMEOUT => 10, // Timeout for establishing connection
            CURLOPT_RETURNTRANSFER => false, // We handle output via WRITEFUNCTION
            CURLOPT_HEADER => false,
            CURLOPT_SSL_VERIFYPEER => true, // Security
            CURLOPT_SSL_VERIFYHOST => 2,
        ]);

        // Execute curl (this starts the process, data is handled in WRITEFUNCTION and processed below)
        curl_exec($ch);

        $curlError = curl_error($ch);
        $curlErrno = curl_errno($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // Process the buffered data after curl_exec finishes or during execution (if WRITEFUNCTION yielded)
        // In this setup, WRITEFUNCTION just buffers, processing happens after curl_exec completes.
        // This is simpler but less real-time than processing within WRITEFUNCTION.
        // For true real-time, WRITEFUNCTION would need to parse and yield.
        // Let's stick to post-processing the buffer for now.

        $lines = explode("\n", trim($buffer));
        $hasSentContent = false;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            if (strpos($line, 'data: ') === 0) {
                $jsonPart = substr($line, 6);

                if ($jsonPart === '[DONE]') {
                    yield ['type' => 'done'];
                    $hasSentContent = true; // Mark as finished
                    return; // Stop the generator
                }

                $dataObj = json_decode($jsonPart, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                     error_log("MistralApiService: JSON decode error in stream: " . json_last_error_msg() . " | Line: " . $line);
                     // Optionally yield an error event or just skip
                     // yield ['type' => 'error', 'message' => 'JSON decode error: ' . json_last_error_msg()];
                     continue;
                }

                if (isset($dataObj['choices'][0]['delta']['content'])) {
                    $token = $dataObj['choices'][0]['delta']['content'];
                    if (!empty($token)) {
                         yield ['type' => 'chunk', 'data' => $token];
                         $hasSentContent = true;
                    }
                } elseif (isset($dataObj['error'])) {
                     // Handle API errors reported within the stream
                     $errorMessage = $dataObj['error']['message'] ?? 'Unknown API error in stream';
                     error_log("MistralApiService: API error in stream: " . $errorMessage);
                     yield ['type' => 'error', 'message' => $errorMessage];
                     return; // Stop generator on stream error
                }
                // Ignore other potential data structures for now
            }
            // Ignore non-data lines (like comments or empty lines)
        }

        // After processing all lines, check for errors or unexpected end
        if ($curlErrno !== CURLE_OK && $curlErrno !== CURLE_RECV_ERROR) { // Ignore RECV_ERROR as stream might just end
             $errorMessage = "Curl error during streaming Mistral API ($curlErrno): $curlError";
             error_log("MistralApiService: " . $errorMessage);
             yield ['type' => 'error', 'message' => $errorMessage];
        } elseif ($httpCode !== 200 && !$hasSentContent) { // Report HTTP error only if no content was ever sent
             $errorMessage = "Mistral API HTTP error ($httpCode) before stream start.";
             error_log("MistralApiService: " . $errorMessage);
             yield ['type' => 'error', 'message' => $errorMessage];
        } elseif (!$hasSentContent) {
             // If curl finished without errors but no DONE or content was processed
             error_log("MistralApiService: Stream ended unexpectedly without [DONE] marker or content.");
             yield ['type' => 'error', 'message' => 'Stream ended unexpectedly.'];
        }
        // If loop finishes and no error/done yielded, generator implicitly ends.
    }
}
