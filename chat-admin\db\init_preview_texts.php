<?php
require_once __DIR__ . '/../autoload.php';

use App\Database\Database;
use App\Models\TextModel;

try {
    $db = Database::getInstance();
    $textModel = new TextModel($db);

    // Add preview texts if they don't exist
    $texts = [
        'preview_message_1' => 'Здравствуйте. Скажите, вам ясно, кто виноват в заливе - соседи или УК?',
        'preview_message_2' => 'Главное получить корректный Акт о заливе, рассказать вам, как он должен выглядеть?'
    ];

    foreach ($texts as $key => $value) {
        $existingText = $textModel->getTextByKey($key);
        if (!$existingText) {
            $textModel->addText($key, $value);
            echo "Added text: $key\n";
        } else {
            echo "Text already exists: $key\n";
        }
    }

    echo "Preview texts initialization complete!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}