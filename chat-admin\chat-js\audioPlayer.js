// chat-js/audioPlayer.js

const AudioContext = window.AudioContext || window.webkitAudioContext;
let audioContext = null;
let scheduledSources = []; // Массив для хранения запланированных/играющих источников { source: AudioBufferSourceNode, endTime: number }
let nextStartTime = 0;     // Время начала следующего фрагмента в AudioContext
let endStreamCallback = null; // Колбэк, вызываемый после завершения *всех* фрагментов потока
let isStreamEnded = false;   // Флаг, что получен сигнал конца потока (markEndOfStream вызван)
let isPlayingInternal = false; // Внутренний флаг: есть ли активные или запланированные источники
let endStreamCheckTimer = null; // Таймер для отложенной проверки в markEndOfStream

/**
 * Инициализирует или возобновляет AudioContext.
 * @returns {Promise<boolean>} - Promise разрешается с true, если контекст готов, или false при ошибке.
 */
export async function ensureAudioContext() {
    if (!AudioContext) {
        console.error("AudioPlayer: Web Audio API is not supported.");
        return false;
    }
    if (!audioContext || audioContext.state === 'closed') {
        try {
            audioContext = new AudioContext();
            nextStartTime = 0;
            console.log("AudioPlayer: AudioContext created/recreated. State:", audioContext.state);
        } catch (e) {
            console.error("AudioPlayer: Failed to create AudioContext.", e);
            audioContext = null;
            return false;
        }
    }
    if (audioContext.state === 'suspended') {
        try {
            console.log("AudioPlayer: AudioContext suspended, resuming...");
            await audioContext.resume();
            console.log("AudioPlayer: AudioContext resumed. State:", audioContext.state);
        } catch (err) {
            console.error("AudioPlayer: Failed to resume AudioContext:", err);
            return false;
        }
    }
    return audioContext.state === 'running';
}

/**
 * Декодирует аудиоданные из base64 и планирует их воспроизведение в очереди.
 * @param {string} base64Audio - Аудио в формате base64.
 */
export async function scheduleChunk(base64Audio) {
    if (!await ensureAudioContext()) {
        console.error("AudioPlayer: Cannot schedule chunk, AudioContext not available.");
        if (endStreamCallback) { _handlePlaybackEnded(new Error("AudioContext not available")); }
        return;
    }
    // Отменяем таймер отложенной проверки конца потока, если он был, т.к. мы точно добавляем чанк
    clearTimeout(endStreamCheckTimer); endStreamCheckTimer = null;

    try {
        let binaryString;
        try {
            if (!base64Audio || base64Audio.length < 50) { console.warn("AudioPlayer: Skipping invalid/empty base64."); return; }
            binaryString = atob(base64Audio);
        } catch (e) { console.error("AudioPlayer: Invalid Base64. Skipping chunk.", e); return; }

        const len = binaryString.length; const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) { bytes[i] = binaryString.charCodeAt(i); }
        const audioData = bytes.buffer;

        const audioBuffer = await audioContext.decodeAudioData(audioData);
        const source = audioContext.createBufferSource();
        source.buffer = audioBuffer; source.connect(audioContext.destination);
        const currentTime = audioContext.currentTime;
        const startTime = Math.max(currentTime, nextStartTime);
        source.start(startTime);
        isPlayingInternal = true; // Важно: ставим флаг, что что-то запланировано
        const estimatedEndTime = startTime + audioBuffer.duration;
        console.log(`AudioPlayer: Chunk scheduled. Start: ${startTime.toFixed(3)}s. End: ${estimatedEndTime.toFixed(3)}s.`);
        nextStartTime = estimatedEndTime;
        const scheduledItem = { source: source, endTime: estimatedEndTime };
        scheduledSources.push(scheduledItem);

        source.onended = () => {
            scheduledSources = scheduledSources.filter(item => item.source !== source);
            // console.log(`AudioPlayer: Chunk ended. Remaining: ${scheduledSources.length}`); // Debug
            // Проверяем условия завершения ВСЕГО потока
            if (scheduledSources.length === 0 && isStreamEnded) {
                console.log("AudioPlayer: Last scheduled chunk finished AND end of stream marked.");
                _handlePlaybackEnded(null); // Естественное завершение
            }
        };
    } catch (error) {
        console.error("AudioPlayer: Error decoding/scheduling chunk:", error);
        _handlePlaybackEnded(error); // Завершаем поток с ошибкой
    }
}

/**
 * Сигнализирует плееру, что больше аудио-фрагментов не будет.
 * Устанавливает колбэк, который будет вызван после завершения последнего фрагмента.
 * ИСПРАВЛЕНО: Добавлена задержка перед проверкой на пустую очередь.
 * @param {function(Error | null): void} callback - Колбэк для вызова после окончания всего воспроизведения.
 */
export function markEndOfStream(callback) {
    console.log("AudioPlayer: End of stream marked.");
    isStreamEnded = true;
    endStreamCallback = callback;
    clearTimeout(endStreamCheckTimer); // Очищаем предыдущий таймер, если был

    // Если В ДАННЫЙ МОМЕНТ очередь пуста, НЕ вызываем колбэк сразу.
    // Запускаем таймер с небольшой задержкой.
    if (scheduledSources.length === 0) {
        console.log("AudioPlayer: End marked, queue currently empty. Setting timer to re-check...");
        endStreamCheckTimer = setTimeout(() => {
            // Внутри таймера: Перепроверяем, действительно ли очередь ВСЕ ЕЩЕ пуста
            // и флаг конца потока все еще установлен (его могли сбросить при stopPlayback)
            if (scheduledSources.length === 0 && isStreamEnded) {
                console.log("AudioPlayer: Timer check - queue STILL empty & stream end marked. Calling end callback.");
                _handlePlaybackEnded(null); // Вызываем обработчик завершения
            } else {
                 console.log("AudioPlayer: Timer check - chunk was scheduled or stream was stopped. Timer cancelled.");
            }
            endStreamCheckTimer = null; // Сбрасываем таймер
        }, 150); // Задержка ~150мс (можно настроить)
    } else {
        // Если очередь НЕ пуста в момент вызова, просто ждем onended последнего чанка.
        console.log(`AudioPlayer: End marked. Waiting for ${scheduledSources.length} scheduled chunk(s) to finish.`);
    }
}

/**
 * Останавливает все текущие и запланированные воспроизведения.
 */
export function stopPlayback() {
    console.log("AudioPlayer: stopPlayback requested.");
    clearTimeout(endStreamCheckTimer); endStreamCheckTimer = null; // Отменяем таймер проверки

    if (scheduledSources.length > 0) {
        console.log(`AudioPlayer: Stopping ${scheduledSources.length} scheduled/playing source(s).`);
        const sourcesToStop = [...scheduledSources];
        scheduledSources = []; nextStartTime = 0; isPlayingInternal = false;
        sourcesToStop.forEach(item => {
            try {
                item.source.onended = null; // Убираем обработчик!
                item.source.stop(0); item.source.disconnect();
            } catch (e) { if (e.name !== 'InvalidStateError') console.warn("AudioPlayer: Error stopping source:", e); }
        });
        console.log("AudioPlayer: Sources stopped.");
        _handlePlaybackEnded(new Error("Playback stopped by user"));
    } else {
        console.log("AudioPlayer: stopPlayback called but nothing was playing/scheduled.");
        if (isStreamEnded) { // Если конец был отмечен, но ничего не играло
             console.log("AudioPlayer: Ensuring callback called for marked end stream with no audio.");
             _handlePlaybackEnded(new Error("Playback stopped by user (no audio was playing)"));
        } else { // Просто сбрасываем состояние
             nextStartTime = 0; isPlayingInternal = false; isStreamEnded = false; endStreamCallback = null;
        }
    }
}

/**
 * Внутренний обработчик завершения ВСЕГО потока воспроизведения.
 * @param {Error | null} error - Объект ошибки или null.
 */
function _handlePlaybackEnded(error) {
    // Сбрасываем таймер проверки на всякий случай
    clearTimeout(endStreamCheckTimer); endStreamCheckTimer = null;

    const wasPlaying = isPlayingInternal;
    console.log("AudioPlayer: Handling playback ended. Resetting state.");
    isPlayingInternal = false; nextStartTime = 0; scheduledSources = []; isStreamEnded = false;

    if (error) {
        const errorMsg = error.message || "";
        if (!errorMsg.includes("Playback stopped by user")) console.error("AudioPlayer: Playback ended with error:", error.message);
        else console.log(`AudioPlayer: Playback ended due to stop: ${errorMsg}`);
    } else {
        console.log("AudioPlayer: Playback finished naturally.");
    }

    if (typeof endStreamCallback === 'function') {
        console.log("AudioPlayer: Calling end stream callback.");
        try { endStreamCallback(error); } // Передаем ошибку (или null)
        catch (e) { console.error("AudioPlayer: Error executing callback:", e); }
    } else {
         if (error && wasPlaying) console.warn("AudioPlayer: Playback ended (error/stop), but no callback registered.");
         else if (!error && wasPlaying) console.log("AudioPlayer: Playback ended naturally, no callback registered.");
    }
    endStreamCallback = null; // Сбрасываем колбэк
}

/**
 * Закрывает AudioContext.
 */
export async function closeAudioContext() {
    console.log("AudioPlayer: Closing AudioContext.");
    stopPlayback(); // Stop first
    if (audioContext && audioContext.state !== 'closed') {
        try { await audioContext.close(); console.log("AudioPlayer: AudioContext closed."); }
        catch (e) { console.warn("AudioPlayer: Error closing AudioContext:", e); }
        finally { audioContext = null; /* Сброс переменных не нужен, т.к. stopPlayback их сбросил */ }
    } else { console.log("AudioPlayer: AudioContext already closed or null."); }
}