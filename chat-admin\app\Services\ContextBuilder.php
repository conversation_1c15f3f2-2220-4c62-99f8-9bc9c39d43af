<?php

namespace App\Services;

use App\Models\Settings;
use App\Models\UploadedFile;
use App\Models\Message;
use Smalot\PdfParser\Parser as PdfParser; // Используем псевдоним

class ContextBuilder {
    private $settingsModel;
    private $uploadedFileModel;
    private $messageModel;
    private $pdfParser;

    public function __construct() {
        $this->settingsModel = new Settings();
        $this->uploadedFileModel = new UploadedFile(); // Модель для получения списка файлов
        $this->messageModel = new Message();
        // Инициализируем парсер PDF лениво, только при необходимости
        $this->pdfParser = null;
    }

    /**
     * Формирует полный контекст для отправки в API чата.
     *
     * @param int $sessionId ID текущей сессии чата.
     * @param int $historyLimit Лимит сообщений истории чата.
     * @return array Массив сообщений контекста.
     * @throws \Exception Если настройки API не найдены.
     */
    public function build(int $sessionId, int $historyLimit = 10): array {
        $finalContext = [];

        // 1. Получаем настройки API для системного сообщения
        $apiSettings = $this->settingsModel->getSettings();
        if (!$apiSettings) {
            // Можно выбросить исключение или вернуть пустой контекст с ошибкой
            throw new \Exception('API settings not found.');
        }
        if (!empty($apiSettings['system_message'])) {
            $systemMessage = $apiSettings['system_message'];
            error_log("System message from DB: " . $systemMessage); // Логируем системное сообщение
            $finalContext[] = ['role' => 'system', 'content' => $systemMessage];
        } else {
            error_log("No system message found in API settings");
        }
 
         // 2. Получаем и обрабатываем файлы контекста (TXT и PDF)
         // Используем getAllFiles(), так как getContextFiles() не существует
         $contextFiles = $this->uploadedFileModel->getAllFiles(); 
         $fileContextContent = $this->buildFileContext($contextFiles);
 
         if (!empty($fileContextContent)) {
             // Добавляем обертку для файлового контекста
             if (empty($finalContext)) { // Если не было системного сообщения
                 $finalContext[] = ['role' => 'system', 'content' => 'You are a helpful assistant.'];
             }
             $finalContext[] = ['role' => 'user', 'content' => "Use the following context to answer the user's question:\n\n" . $fileContextContent];
             $finalContext[] = ['role' => 'assistant', 'content' => 'Okay, I have read the context. How can I help?'];
        }

        // 3. Получаем историю сообщений чата
        $chatHistory = $this->messageModel->getContextMessages($sessionId, $historyLimit);

        // 4. Объединяем все части контекста
        $finalContext = array_merge($finalContext, $chatHistory);

        // Удаляем лишние поля (например, id), оставляем только role и content
        $finalContext = array_map(function($msg) {
            return [
                'role' => $msg['role'],
                'content' => $msg['content']
            ];
        }, $finalContext);

        // Если последнее сообщение ассистента — удаляем его
        while (!empty($finalContext) && $finalContext[count($finalContext) - 1]['role'] === 'assistant') {
            array_pop($finalContext);
        }

        return $finalContext;
    }

    /**
     * Собирает текстовое содержимое из файлов контекста.
     *
     * @param array $files Массив файлов из UploadedFile::getContextFiles().
     * @return string Объединенное текстовое содержимое файлов.
     */
    private function buildFileContext(array $files): string {
        $fullContent = '';
        foreach ($files as $file) {
            $fileContent = '';
            
            // 1. Используем processed_content из базы, если он есть
            if (!empty($file['processed_content'])) {
                $fileContent = $file['processed_content'];
            } 
            // 2. Если нет processed_content, пробуем прочитать файл
            elseif (!empty($file['file_path']) && file_exists($file['file_path'])) {
                try {
                    if ($file['file_type'] === 'text/plain') {
                        $fileContent = file_get_contents($file['file_path']);
                    } elseif ($file['file_type'] === 'application/pdf') {
                        if ($this->pdfParser === null) {
                            $this->pdfParser = new PdfParser();
                        }
                        $pdf = $this->pdfParser->parseFile($file['file_path']);
                        $fileContent = $pdf->getText();
                    }
                } catch (\Exception $e) {
                    error_log("Error processing context file {$file['file_path']}: " . $e->getMessage());
                }
            }

            if (!empty(trim($fileContent))) {
                $fullContent .= "--- Context from file: " . basename($file['file_name']) . " ---\n";
                $fullContent .= trim($fileContent) . "\n\n";
            }
        }
        return trim($fullContent);
    }
}
