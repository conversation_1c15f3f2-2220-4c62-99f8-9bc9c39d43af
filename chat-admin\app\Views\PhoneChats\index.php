<?php 
$pageTitle = 'Переписки с телефонами';
$currentUser = $currentUser ?? ['name' => 'Администратор', 'email' => '<EMAIL>'];
require __DIR__ . '/../partials/header.php'; 
?>

<style>
/* Критически важные стили пагинации */
.pagination-nav-simple {
    display: flex !important;
    justify-content: center !important;
    margin: 20px 0 !important;
}

.pagination-simple {
    display: flex !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    gap: 4px !important;
    align-items: center !important;
}

.page-item-simple {
    display: flex !important;
}

.page-link-simple {
    padding: 8px 12px !important;
    background: #ffffff !important;
    border: 1px solid #dee2e6 !important;
    color: #007bff !important;
    text-decoration: none !important;
    font-weight: 400 !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 40px !important;
    font-size: 14px !important;
    border-radius: 4px !important;
}

.page-link-simple:hover {
    background: #e9ecef !important;
    border-color: #adb5bd !important;
    color: #0056b3 !important;
    text-decoration: none !important;
}

.page-item-simple.active .page-link-simple {
    background: #007bff !important;
    color: #ffffff !important;
    border-color: #007bff !important;
}

.page-item-simple.disabled .page-link-simple {
    color: #6c757d !important;
    background: #ffffff !important;
    border-color: #dee2e6 !important;
    cursor: default !important;
}

.page-item-simple.disabled .page-link-simple:hover {
    background: #ffffff !important;
    border-color: #dee2e6 !important;
    color: #6c757d !important;
}

/* Компактные карточки */
.chat-card-compact {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    margin-bottom: 1rem !important;
}

.card-header-compact {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 0.75rem 1rem !important;
    border: none !important;
}

.card-body-compact {
    padding: 1rem !important;
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

.btn-compact {
    padding: 0.4rem 0.8rem !important;
    border-radius: 20px !important;
    font-weight: 600 !important;
    font-size: 0.75rem !important;
    text-decoration: none !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 80px !important;
    flex: 1 !important;
}

.btn-view-compact {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    color: white !important;
}

.btn-delete-compact {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    color: white !important;
}
</style>

<div class="main-content">
    <div class="content-header">
        <h1><i class="fas fa-phone-alt me-2"></i> Переписки с телефонами</h1>
        <p>Управление переписками, содержащими номера телефонов</p>
    </div>

    <!-- Фильтры -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter me-2"></i> Фильтры</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="index.php">
                <input type="hidden" name="page" value="phone_chats">
                <div class="row">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Поиск по тексту</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?= htmlspecialchars($search ?? '') ?>" 
                               placeholder="Поиск в сообщениях...">
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">Дата от</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="<?= htmlspecialchars($dateFrom ?? '') ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">Дата до</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="<?= htmlspecialchars($dateTo ?? '') ?>">
                    </极>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Поиск
                            </button>
                        </div>
                    </div>
                </div>
                <?php if (($search ?? '') || ($dateFrom ?? '') || ($dateTo ?? '')): ?>
                <div class="mt-3">
                    <a href="index.php?page=phone_chats" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Сбросить фильтры
                    </a>
                </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Информация и настройки отображения -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="text-muted mb-2 mb-md-0">
                    <i class="fas fa-info-circle"></i>
                    Найдено: <strong><?= $totalCount ?? 0 ?></strong> переписок с телефонами
                </div>
                <div class="d-flex align-items-center gap-3 flex-wrap">
                    <div class="text-muted">
                        Страница <strong><?= $currentPage ?? 1 ?></strong> из <strong><?= $totalPages ?? 1 ?></strong>
                    </div>
                    <div class="d-flex align-items-center">
                        <label for="per_page" class="form-label me-2 mb-0 text-muted">Показать:</label>
                        <select id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                            <option value="5" <?= ($perPage ?? 10) == 5 ? 'selected' : '' ?>>5</option>
                            <option value="10" <?= ($perPage ?? 10) == 10 ? 'selected' : '' ?>>10</option>
                            <option value="20" <?= ($perPage ?? 10) == 20 ? 'selected' : '' ?>>20</option>
                            <option value="50" <?= ($perPage ?? 10) == 50 ? 'selected' : '' ?>>50</option>
                            <option value="100" <?= ($perPage ?? 10) == 100 ? 'selected' : '' ?>>100</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Список переписок -->
    <div class="phone-chats-container">
        <?php if (empty($phoneChats)): ?>
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-phone-alt fa-3x text-muted mb-3"></i>
                    <h4>Переписки с телефонами не найдены</h4>
                    <p class="text-muted">Переписки с телефонами появятся здесь автоматически, когда пользователи будут указывать номера телефонов в чате</p>
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger mt-3">
                            <strong>Ошибка:</strong> <?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <!-- Компактные карточки -->
            <div class="row">
            <?php foreach ($phoneChats as $chat): ?>
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                <div class="chat-card-compact">
                    <!-- Заголовок карточки -->
                    <div class="card-header-compact">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="chat-info">
                                <h6 class="chat-title-compact">
                                    <i class="fas fa-comments me-1"></i>
                                    <?= htmlspecialchars(mb_substr($chat['title'] ?? 'Переписка #' . ($chat['id'] ?? 'N/A'), 0, 20)) ?><?= mb_strlen($chat['title'] ?? '') > 20 ? '...' : '' ?>
                                </h6>
                                <small class="chat-meta-compact">
                                    ID: <?= $chat['id'] ?? 'N/A' ?> | <?= htmlspecialchars(substr($chat['session_uuid'] ?? '', 0, 6)) ?>...
                                </small>
                            </div>
                            <span class="messages-badge">
                                <?= $chat['message_count'] ?? 0 ?>
                            </span>
                        </div>
                    </div>

                    <!-- Тело карточки -->
                    <div class="card-body-compact">
                        <!-- Информация о дате -->
                        <div class="date-info-compact">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?= isset($chat['created_at']) ? date('d.m.y H:i', strtotime($chat['created_at'])) : 'N/A' ?>
                            </small>
                        </div>

                        <!-- Цифровые данные -->
                        <div class="digital-data-compact-section">
                            <?php
                            $allData = [];

                            // Собираем телефоны
                            if (!empty($chat['phones'])) {
                                foreach ($chat['phones'] as $phone) {
                                    $allData[] = ['type' => 'phones', 'value' => $phone, 'icon' => 'phone', 'color' => 'success'];
                                }
                            }

                            // Собираем остальные данные
                            if (!empty($chat['digital_data']) && is_array($chat['digital_data'])) {
                                foreach ($chat['digital_data'] as $data) {
                                    if (is_array($data) && isset($data['type']) && isset($data['value']) && $data['type'] !== 'phones') {
                                        $color = match($data['type']) {
                                            'numbers' => 'primary',
                                            'codes' => 'warning',
                                            'dates' => 'info',
                                            'time' => 'secondary',
                                            'money' => 'success',
                                            default => 'dark'
                                        };
                                        $allData[] = [
                                            'type' => $data['type'],
                                            'value' => $data['value'],
                                            'icon' => $controller->getIconForType($data['type']),
                                            'color' => $color
                                        ];
                                    }
                                }
                            }
                            ?>

                            <?php if (!empty($allData)): ?>
                                <div class="data-compact-grid">
                                    <?php
                                    // Показываем больше элементов для лучшего отображения данных
                                    $displayData = array_slice($allData, 0, 8);
                                    $remainingCount = count($allData) - 8;
                                    ?>

                                    <?php foreach ($displayData as $item): ?>
                                        <span class="digital-data-label <?= $item['type'] ?>"
                                              <?php
                                              $styles = [
                                                  'phones' => 'background: linear-gradient(135deg, #28a745, #20c997) !important; color: white !important; font-size: 14px !important; font-weight: bold !important; padding: 6px 12px !important; border-radius: 16px !important; box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3) !important;',
                                                  'numbers' => 'background: linear-gradient(135deg, #17a2b8, #138496) !important; color: white !important;',
                                                  'codes' => 'background: linear-gradient(135deg, #6f42c1, #5a32a3) !important; color: white !important;',
                                                  'dates' => 'background: linear-gradient(135deg, #fd7e14, #e8590c) !important; color: white !important;',
                                                  'time' => 'background: linear-gradient(135deg, #6c757d, #545b62) !important; color: white !important;',
                                                  'money' => 'background: linear-gradient(135deg, #ffc107, #e0a800) !important; color: #212529 !important;'
                                              ];
                                              if (isset($styles[$item['type']])):
                                              ?>
                                              style="<?= $styles[$item['type']] ?>"
                                              <?php endif; ?>>
                                            <i class="fas fa-<?= $item['icon'] ?> me-1"></i>
                                            <?= htmlspecialchars(mb_substr($item['value'], 0, 10)) ?><?= mb_strlen($item['value']) > 10 ? '...' : '' ?>
                                        </span>
                                    <?php endforeach; ?>

                                    <?php if ($remainingCount > 0): ?>
                                        <span class="digital-data-label" style="background: linear-gradient(135deg, #6c757d, #495057); color: white;">
                                            +<?= $remainingCount ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="no-data-compact">
                                    <small class="text-muted">
                                        <i class="fas fa-search me-1"></i>Данные не найдены
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Футер карточки с действиями -->
                    <div class="card-footer-compact">
                        <div class="action-buttons-compact">
                            <a href="index.php?page=phone_chats&action=view&session_id=<?= $chat['id'] ?? 0 ?>"
                               class="btn-compact btn-view-compact">
                                <i class="fas fa-eye me-1"></i>Просмотр
                            </a>
                            <button type="button"
                                    class="btn-compact btn-delete-compact delete-chat-btn"
                                    data-session-id="<?= $chat['id'] ?? 0 ?>">
                                <i class="fas fa-trash me-1"></i>Удалить
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Пагинация -->
        <div class="pagination-container">
            <div class="pagination-info-modern">
                <div class="results-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Показано <strong><?= count($phoneChats) ?></strong> из <strong><?= $totalCount ?></strong> записей
                    <?php if ($totalPages > 1): ?>
                        <span class="page-info">(страница <?= $currentPage ?> из <?= $totalPages ?>)</span>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Отладочная информация убрана -->

            <?php if ($totalPages > 1): ?>
            <nav aria-label="Навигация по страницам" class="pagination-nav-simple">
                <ul class="pagination-simple">
                    <?php
                    // Простая логика отображения страниц
                    $showPages = [];

                    if ($totalPages <= 7) {
                        // Если страниц мало, показываем все
                        for ($i = 1; $i <= $totalPages; $i++) {
                            $showPages[] = $i;
                        }
                    } else {
                        // Всегда показываем первую страницу
                        $showPages[] = 1;

                        if ($currentPage <= 4) {
                            // Если в начале, показываем 1-5 ... последняя
                            for ($i = 2; $i <= 5; $i++) {
                                $showPages[] = $i;
                            }
                            $showPages[] = '...';
                            $showPages[] = $totalPages;
                        } elseif ($currentPage >= $totalPages - 3) {
                            // Если в конце, показываем 1 ... последние 5
                            $showPages[] = '...';
                            for ($i = $totalPages - 4; $i <= $totalPages; $i++) {
                                $showPages[] = $i;
                            }
                        } else {
                            // В середине, показываем 1 ... текущая-2 текущая-1 текущая текущая+1 текущая+2 ... последняя
                            $showPages[] = '...';
                            for ($i = $currentPage - 2; $i <= $currentPage + 2; $i++) {
                                $showPages[] = $i;
                            }
                            $showPages[] = '...';
                            $showPages[] = $totalPages;
                        }
                    }
                    ?>

                    <?php foreach ($showPages as $page): ?>
                        <?php if ($page === '...'): ?>
                            <li class="page-item-simple disabled">
                                <span class="page-link-simple">...</span>
                            </li>
                        <?php else: ?>
                            <li class="page-item-simple <?= $page == $currentPage ? 'active' : '' ?>">
                                <a class="page-link-simple <?= $page == $currentPage ? 'active-current' : '' ?>"
                                   href="?<?= http_build_query(array_merge($_GET, ['page_num' => $page])) ?>"
                                   onclick="return handlePageClick(this, <?= $page ?>);"
                                   data-page="<?= $page ?>">
                                    <?= $page ?>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    </div> <!-- Закрываем phone-chats-container -->


</div>

<script>
// Удаление отдельной переписки
document.querySelectorAll('.delete-chat-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const sessionId = this.dataset.sessionId;

        if (confirm('Удалить эту переписку? Это действие нельзя отменить.')) {
            // Показываем индикатор загрузки
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            this.disabled = true;

            fetch('index.php?page=phone_chats&action=deleteChat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `session_id=${sessionId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Плавно удаляем элемент
                    this.closest('.chat-card-compact').parentElement.style.opacity = '0';
                    this.closest('.chat-card-compact').parentElement.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        location.reload();
                    }, 300);
                } else {
                    // Восстанавливаем кнопку
                    this.innerHTML = '<i class="fas fa-trash"></i>';
                    this.disabled = false;
                    showNotification('Ошибка: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Восстанавливаем кнопку
                this.innerHTML = '<i class="fas fa-trash"></i>';
                this.disabled = false;
                showNotification('Произошла ошибка при удалении', 'error');
            });
        }
    });
});

// Функция для показа уведомлений
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Показываем уведомление
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Скрываем уведомление через 3 секунды
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Функция для изменения количества элементов на странице
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page_num', '1'); // Сбрасываем на первую страницу
    window.location.href = url.toString();
}

// AJAX пагинация
function loadPage(pageNum) {
    console.log('Loading page:', pageNum);

    // Показываем лоадер
    showLoader();

    // Сразу обновляем активную кнопку
    updateActiveButton(pageNum);

    // Получаем текущие параметры URL
    const url = new URL(window.location);
    url.searchParams.set('page_num', pageNum);

    console.log('AJAX URL:', url.toString());

    // Делаем AJAX запрос
    fetch(url.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'text/html'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.text();
    })
    .then(html => {
        console.log('Received HTML length:', html.length);
        console.log('First 200 chars:', html.substring(0, 200));

        // Находим контейнер и обновляем его
        const currentContent = document.querySelector('.phone-chats-container');
        if (currentContent && html.trim()) {
            currentContent.innerHTML = html;

            // Выполняем скрипты из нового контента
            const scripts = currentContent.querySelectorAll('script');
            scripts.forEach(script => {
                try {
                    eval(script.textContent);
                } catch (e) {
                    console.error('Script execution error:', e);
                }
            });

            // Обновляем активную кнопку в новом контенте
            updateActiveButton(pageNum);
        } else {
            throw new Error('Empty or invalid response');
        }

        // Обновляем URL без перезагрузки страницы
        window.history.pushState({}, '', url.toString());

        // Скрываем лоадер
        hideLoader();

        console.log('Page loaded successfully');
    })
    .catch(error => {
        console.error('Ошибка загрузки страницы:', error);
        hideLoader();
        showNotification('Ошибка загрузки данных: ' + error.message, 'error');

        // Возвращаем активный класс обратно
        const currentPageNum = new URLSearchParams(window.location.search).get('page_num') || '1';
        updateActiveButton(currentPageNum);
    });
}

// Функция для обработки клика по пагинации
function handlePageClick(link, pageNum) {
    try {
        loadPage(pageNum);
        return false; // Предотвращаем обычный переход
    } catch (error) {
        console.error('AJAX failed, using normal navigation:', error);
        return true; // Разрешаем обычный переход по ссылке
    }
}

// Функция для обновления активной кнопки
function updateActiveButton(pageNum) {
    // Убираем активный класс со всех кнопок
    document.querySelectorAll('.page-link-simple').forEach(link => {
        link.classList.remove('active-current');
    });

    // Добавляем активный класс к нужной кнопке
    const targetLink = document.querySelector(`[data-page="${pageNum}"]`);
    if (targetLink) {
        targetLink.classList.add('active-current');
        console.log('Active button updated for page:', pageNum);
    } else {
        console.log('Button not found for page:', pageNum);
    }
}

function showLoader() {
    // Удаляем старые элементы если есть
    const oldOverlay = document.querySelector('.pagination-overlay');
    const oldLoader = document.querySelector('.pagination-loader');
    if (oldOverlay) oldOverlay.remove();
    if (oldLoader) oldLoader.remove();

    // Создаем overlay
    const overlay = document.createElement('div');
    overlay.className = 'pagination-overlay show';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        z-index: 9998;
        display: block;
    `;
    document.body.appendChild(overlay);

    // Создаем лоадер
    const loader = document.createElement('div');
    loader.className = 'pagination-loader show';
    loader.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 12px;
        padding: 20px 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 15px;
    `;
    loader.innerHTML = `
        <div style="width: 24px; height: 24px; border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        <div style="font-size: 14px; color: #495057; font-weight: 500;">Загрузка данных...</div>
    `;
    document.body.appendChild(loader);

    // Добавляем анимацию спиннера
    const style = document.createElement('style');
    style.textContent = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
}

function hideLoader() {
    const overlay = document.querySelector('.pagination-overlay');
    const loader = document.querySelector('.pagination-loader');

    if (overlay) overlay.remove();
    if (loader) loader.remove();

    console.log('Loader hidden');
}

// Обработка кнопки "Назад" в браузере
window.addEventListener('popstate', function(event) {
    location.reload(); // Простое решение - перезагружаем страницу
});

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', function() {
    console.log('PhoneChats page loaded');

    // Устанавливаем активный класс для текущей страницы
    const currentPageNum = new URLSearchParams(window.location.search).get('page_num') || '1';
    console.log('Current page number:', currentPageNum);

    const currentLink = document.querySelector(`[data-page="${currentPageNum}"]`);
    if (currentLink) {
        // Убираем активный класс со всех кнопок
        document.querySelectorAll('.page-link-simple').forEach(link => {
            link.classList.remove('active-current');
        });
        // Добавляем активный класс к текущей кнопке
        currentLink.classList.add('active-current');
        console.log('Active button set for page:', currentPageNum);
    } else {
        console.log('No button found for page:', currentPageNum);
    }

    // Тестируем AJAX
    console.log('Testing fetch availability:', typeof fetch !== 'undefined');
});
</script>

<?php require __DIR__ . '/../partials/footer.php'; ?>
