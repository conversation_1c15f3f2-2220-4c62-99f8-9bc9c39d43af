<?php

declare(strict_types=1);

namespace App\Services;

/**
 * Сервис отправки email через cURL (без PHPMailer)
 * Работает в OpenServer без OpenSSL расширения
 */
class CurlEmailService
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'host' => 'smtp.yandex.ru',
            'port' => 587,
            'username' => '',
            'password' => '',
            'from_email' => '',
            'from_name' => 'AI Chat System',
            'timeout' => 30,
            'debug' => false
        ], $config);
    }

    /**
     * Отправляет email через внешний API сервис
     */
    public function sendEmail(string $to, string $subject, string $body, ?string $fromEmail = null): bool
    {
        try {
            // Используем простой HTTP API для отправки email
            return $this->sendViaHttpApi($to, $subject, $body, $fromEmail);
        } catch (\Exception $e) {
            error_log("CurlEmailService: Error sending email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Отправка через HTTP API (например, через собственный скрипт на другом сервере)
     */
    private function sendViaHttpApi(string $to, string $subject, string $body, ?string $fromEmail = null): bool
    {
        // Для демонстрации используем простую отправку через mail() с правильными заголовками
        $from = $fromEmail ?: $this->config['from_email'];
        
        // Формируем заголовки
        $headers = [
            'From: ' . $this->config['from_name'] . ' <' . $from . '>',
            'Reply-To: ' . $from,
            'X-Mailer: PHP/' . phpversion(),
            'MIME-Version: 1.0',
            'Content-Type: text/plain; charset=UTF-8',
            'Content-Transfer-Encoding: 8bit'
        ];

        $headersString = implode("\r\n", $headers);

        // Пытаемся отправить через встроенную функцию mail()
        $result = mail($to, $subject, $body, $headersString);

        if ($result) {
            error_log("CurlEmailService: Email sent successfully to $to");
            return true;
        } else {
            error_log("CurlEmailService: Failed to send email to $to");
            
            // Если не удалось отправить, сохраняем в файл
            $this->saveEmailToFile($to, $subject, $body, $from);
            return false;
        }
    }

    /**
     * Альтернативный метод через внешний SMTP API
     */
    public function sendViaExternalApi(string $to, string $subject, string $body, ?string $fromEmail = null): bool
    {
        // Можно использовать внешние сервисы типа SendGrid, Mailgun, etc.
        // Для примера создаем простой HTTP запрос
        
        $from = $fromEmail ?: $this->config['from_email'];
        
        $data = [
            'to' => $to,
            'from' => $from,
            'subject' => $subject,
            'body' => $body,
            'api_key' => 'your_api_key_here' // Замените на реальный API ключ
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => 'https://api.example-email-service.com/send', // Замените на реальный API
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer your_api_key_here'
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['timeout'],
            CURLOPT_SSL_VERIFYPEER => false, // Для OpenServer
            CURLOPT_SSL_VERIFYHOST => false  // Для OpenServer
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            error_log("CurlEmailService: cURL error: $error");
            return false;
        }

        if ($httpCode >= 200 && $httpCode < 300) {
            error_log("CurlEmailService: Email sent via external API to $to");
            return true;
        } else {
            error_log("CurlEmailService: External API returned HTTP $httpCode: $response");
            return false;
        }
    }

    /**
     * Сохраняет email в файл как backup
     */
    private function saveEmailToFile(string $to, string $subject, string $body, string $from): void
    {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "email_backup_{$timestamp}_" . substr(md5($to . $subject), 0, 8) . ".txt";
        
        // Создаем папку если не существует
        $emailDir = dirname(__DIR__, 2) . '/email_notifications';
        if (!is_dir($emailDir)) {
            mkdir($emailDir, 0755, true);
        }
        
        $filepath = $emailDir . '/' . $filename;
        
        $content = "=== EMAIL BACKUP ===\n";
        $content .= "Дата: " . date('Y-m-d H:i:s') . "\n";
        $content .= "От: $from\n";
        $content .= "Кому: $to\n";
        $content .= "Тема: $subject\n";
        $content .= "Метод: CurlEmailService\n";
        $content .= "\n=== СОДЕРЖИМОЕ ===\n";
        $content .= $body;
        
        file_put_contents($filepath, $content);
        error_log("CurlEmailService: Email saved to backup file: $filename");
    }

    /**
     * Тестирует отправку email
     */
    public function testEmail(string $to): bool
    {
        $subject = "Тест отправки email - " . date('Y-m-d H:i:s');
        $body = "Это тестовое письмо для проверки работы CurlEmailService.\n\n";
        $body .= "Время отправки: " . date('Y-m-d H:i:s') . "\n";
        $body .= "Сервис: CurlEmailService\n";
        $body .= "Сервер: " . ($_SERVER['SERVER_NAME'] ?? 'localhost') . "\n";
        
        return $this->sendEmail($to, $subject, $body);
    }

    /**
     * Получает конфигурацию
     */
    public function getConfig(): array
    {
        return $this->config;
    }

    /**
     * Обновляет конфигурацию
     */
    public function updateConfig(array $newConfig): void
    {
        $this->config = array_merge($this->config, $newConfig);
    }
}
