<?php

namespace App\Models;

use App\Models\Database;
// Добавляем импорт класса UploadedFile, если он в том же неймспейсе или укажите полный путь
use App\Models\UploadedFile; // Предполагаем, что UploadedFile находится здесь

class Message {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Получает сообщения с учетом фильтров и пагинации.
     *
     * @param array $filters Ассоциативный массив фильтров ('userId', 'sessionId', 'search', 'dateFrom', 'dateTo').
     * @param int $page Номер текущей страницы.
     * @param int $perPage Количество сообщений на странице.
     * @return array Массив с ключами 'messages' (массив сообщений) и 'totalMessages' (общее количество).
     */
    public function getMessages(array $filters = [], int $page = 1, int $perPage = 20): array {
        $offset = ($page - 1) * $perPage;
        $params = [];
        $whereClauses = [];

        // Базовый запрос
        $baseSql = "FROM messages m
                    LEFT JOIN users u ON (CAST(m.user_id AS TEXT) = CAST(u.id AS TEXT)) -- Сравнение как текст для user_id/id
                    LEFT JOIN chat_sessions cs ON m.session_id = cs.id";
        $selectSql = "SELECT m.id, m.session_id, m.user_id, m.sender, m.content, m.timestamp,
                             u.username, cs.title as session_title ";
        $countSql = "SELECT COUNT(m.id) ";

        // Применение фильтров
        if (isset($filters['userId']) && $filters['userId'] !== '') {
            $whereClauses[] = "m.user_id = :user_id";
            $params[':user_id'] = $filters['userId']; // Тип будет определен при биндинге
        }
        if (!empty($filters['sessionId'])) {
            $whereClauses[] = "m.session_id = :session_id";
            $params[':session_id'] = (int)$filters['sessionId']; // Приводим к int
        }
        if (isset($filters['search']) && $filters['search'] !== '') {
            $whereClauses[] = "m.content LIKE :search";
            $params[':search'] = '%' . $filters['search'] . '%';
        }
        if (!empty($filters['dateFrom'])) {
            $whereClauses[] = "m.timestamp >= :date_from";
            // Валидация формата даты перед использованием
            $dateFrom = \DateTime::createFromFormat('Y-m-d', $filters['dateFrom']);
            if ($dateFrom) {
                $params[':date_from'] = $dateFrom->format('Y-m-d') . ' 00:00:00';
            } else {
                 unset($filters['dateFrom']); // Убираем некорректный фильтр
                 error_log("Invalid date format for dateFrom: " . $filters['dateFrom']);
            }
        }
        if (!empty($filters['dateTo'])) {
             $whereClauses[] = "m.timestamp <= :date_to";
             $dateTo = \DateTime::createFromFormat('Y-m-d', $filters['dateTo']);
             if ($dateTo) {
                 $params[':date_to'] = $dateTo->format('Y-m-d') . ' 23:59:59';
             } else {
                 unset($filters['dateTo']); // Убираем некорректный фильтр
                 error_log("Invalid date format for dateTo: " . $filters['dateTo']);
             }
        }

        $whereSql = !empty($whereClauses) ? " WHERE " . implode(" AND ", $whereClauses) : "";

        // Получаем общее количество
        $totalMessages = 0;
        $countStmt = null;
        try {
            // Оптимизация COUNT: JOIN'ы нужны только если есть фильтры по связанным таблицам
            $countNeedsUserJoin = isset($filters['userId']); // Фильтр по user_id требует JOIN с users
            $countNeedsSessionJoin = isset($filters['sessionId']); // Фильтр по session_id требует JOIN с chat_sessions

            $countBaseSql = "FROM messages m";
             // Добавляем JOIN'ы только при необходимости
             if ($countNeedsUserJoin) {
                 // Примечание: Если фильтра по userId нет, JOIN с users для COUNT не нужен
                  $countBaseSql .= " LEFT JOIN users u ON (CAST(m.user_id AS TEXT) = CAST(u.id AS TEXT))";
             }
             if ($countNeedsSessionJoin) {
                  // Примечание: Если фильтра по sessionId нет, JOIN с chat_sessions для COUNT не нужен
                  $countBaseSql .= " LEFT JOIN chat_sessions cs ON m.session_id = cs.id";
             }


            $countQuery = $countSql . $countBaseSql . $whereSql;
            $countStmt = $this->db->prepare($countQuery);

            if ($countStmt === false) {
                 error_log("Error preparing count messages query: " . $this->db->lastErrorMsg());
                 throw new \Exception("Failed to prepare count statement.");
            }

            // Биндинг параметров для COUNT
            foreach ($params as $key => $value) {
                $type = \SQLITE3_TEXT;
                if (is_int($value)) {
                    $type = \SQLITE3_INTEGER;
                } elseif (is_null($value)) {
                    $type = \SQLITE3_NULL;
                } elseif ($key === ':user_id') { // user_id может быть строкой или числом
                    $type = ctype_digit((string)$value) ? \SQLITE3_INTEGER : \SQLITE3_TEXT;
                    if($type == \SQLITE3_INTEGER) $value = (int)$value;
                }

                if (!$countStmt->bindValue($key, $value, $type)) {
                     error_log("Error binding parameter '$key' for count messages query.");
                     // Можно добавить throw new \Exception(...) если критично
                }
            }

            $countResult = $countStmt->execute();
            if ($countResult) {
                $countRow = $countResult->fetchArray(\SQLITE3_NUM);
                $totalMessages = $countRow ? (int)$countRow[0] : 0;
                $countResult->finalize();
            } else {
                 error_log("Error executing count messages query: " . $this->db->lastErrorMsg());
            }
            $countStmt->close();

        } catch (\Exception $e) {
             error_log("Exception counting messages: " . $e->getMessage());
             if ($countStmt instanceof \SQLite3Stmt) $countStmt->close();
             return ['messages' => [], 'totalMessages' => 0]; // Возвращаем пустой результат при ошибке
        }

        // Получаем сообщения для текущей страницы
        $messages = [];
        $stmt = null;
        try {
            // Для выборки сообщений используем полный $baseSql с JOIN'ами
            $messagesQuery = $selectSql . $baseSql . $whereSql . " ORDER BY m.timestamp DESC LIMIT :limit OFFSET :offset";
            $stmt = $this->db->prepare($messagesQuery);

            if ($stmt === false) {
                 error_log("Error preparing fetch messages query: " . $this->db->lastErrorMsg());
                 throw new \Exception("Failed to prepare select statement.");
            }

            // Биндинг параметров для SELECT (аналогично COUNT)
            foreach ($params as $key => $value) {
                 $type = \SQLITE3_TEXT;
                 if (is_int($value)) {
                     $type = \SQLITE3_INTEGER;
                 } elseif (is_null($value)) {
                     $type = \SQLITE3_NULL;
                 } elseif ($key === ':user_id') {
                     $type = ctype_digit((string)$value) ? \SQLITE3_INTEGER : \SQLITE3_TEXT;
                     if($type == \SQLITE3_INTEGER) $value = (int)$value;
                 }

                  if (!$stmt->bindValue($key, $value, $type)) {
                      error_log("Error binding parameter '$key' for fetch messages query.");
                  }
            }
            // Биндинг пагинации
            $stmt->bindValue(':limit', $perPage, \SQLITE3_INTEGER);
            $stmt->bindValue(':offset', $offset, \SQLITE3_INTEGER);

            $result = $stmt->execute();
            if ($result) {
                while ($row = $result->fetchArray(\SQLITE3_ASSOC)) {
                    $messages[] = $row;
                }
                $result->finalize();
            } else {
                 error_log("Error executing fetch messages query: " . $this->db->lastErrorMsg());
            }
             $stmt->close();

        } catch (\Exception $e) {
             error_log("Exception fetching messages: " . $e->getMessage());
             if ($stmt instanceof \SQLite3Stmt) $stmt->close();
             // Возвращаем то, что успели посчитать, но пустой список сообщений
             return ['messages' => [], 'totalMessages' => $totalMessages];
        }

        return [
            'messages' => $messages,
            'totalMessages' => $totalMessages
        ];
    }

    /**
     * Получает данные для фильтров на странице сообщений.
     *
     * @param int|string|null $adminUserId ID администратора для исключения из списка пользователей (может быть числом или строкой).
     * @return array Массив с ключами 'users' и 'sessions'.
     */
    public function getFilterData($adminUserId = null): array {
        $chatUsers = [];
        $sessions = [];
        $stmtUsers = null;
        $resultSessions = null;

        try {
            // Получаем пользователей, которые участвовали в чатах
             $sqlUsers = "
                SELECT DISTINCT COALESCE(u.id, cs.user_id) as user_id_val, u.username
                FROM chat_sessions cs
                LEFT JOIN users u ON CAST(cs.user_id AS TEXT) = CAST(u.id AS TEXT)
                WHERE cs.user_id IS NOT NULL AND cs.user_id != ''";

             $paramsUsers = [];
             // Исключаем админа, если он задан
             if ($adminUserId !== null && $adminUserId !== '') {
                 $sqlUsers .= " AND CAST(cs.user_id AS TEXT) != :admin_id";
                 // Приводим adminUserId к строке для сравнения с текстовым полем user_id
                 $paramsUsers[':admin_id'] = (string)$adminUserId;
             }
             $sqlUsers .= " ORDER BY u.username ASC, user_id_val ASC";

             $stmtUsers = $this->db->prepare($sqlUsers);
             if (!$stmtUsers) {
                 error_log("Error preparing filter users query: " . $this->db->lastErrorMsg());
                 throw new \Exception("Failed to prepare filter users statement.");
             }

             // Биндим параметр :admin_id, если он есть
             if (!empty($paramsUsers)) {
                  $stmtUsers->bindValue(':admin_id', $paramsUsers[':admin_id'], \SQLITE3_TEXT);
             }

            $resultUsers = $stmtUsers->execute();
            $addedUserIds = [];
            if ($resultUsers) {
                while ($row = $resultUsers->fetchArray(\SQLITE3_ASSOC)) {
                    $userId = $row['user_id_val'];
                    if (isset($addedUserIds[$userId])) continue; // Пропускаем, если уже добавили

                    $displayName = $row['username'] ? ($row['username'] . ' (ID: ' . htmlspecialchars($userId) . ')') : 'ID: ' . htmlspecialchars($userId);
                    $chatUsers[] = ['id' => $userId, 'display_name' => $displayName];
                    $addedUserIds[$userId] = true;
                }
                $resultUsers->finalize();
            } else {
                 error_log("Error fetching users for filter: " . $this->db->lastErrorMsg());
            }
            $stmtUsers->close();


            // Получаем все сессии
            $resultSessions = $this->db->query("SELECT id, title FROM chat_sessions ORDER BY updated_at DESC");
            if ($resultSessions) {
                while ($row = $resultSessions->fetchArray(\SQLITE3_ASSOC)) {
                    $sessions[] = $row;
                }
                // query() не требует finalize/close для результата, только для statement
                // $resultSessions->finalize(); // Не нужно
            } else {
                error_log("Error fetching sessions for filter: " . $this->db->lastErrorMsg());
            }

        } catch (\Exception $e) {
             error_log("Exception fetching filter data: " . $e->getMessage());
             if ($stmtUsers instanceof \SQLite3Stmt) $stmtUsers->close();
        }

        return [
            'users' => $chatUsers,
            'sessions' => $sessions
        ];
    }

    /**
     * Удаляет сообщение по ID.
     *
     * @param int $messageId ID сообщения.
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function deleteMessage(int $messageId): bool {
        if ($messageId <= 0) return false; // Базовая проверка
        $stmt = null;
         try {
            $stmt = $this->db->prepare("DELETE FROM messages WHERE id = :id");
             if ($stmt === false) {
                 error_log("Failed to prepare statement for deleting message ID $messageId: " . $this->db->lastErrorMsg());
                 return false;
             }
            $stmt->bindValue(':id', $messageId, \SQLITE3_INTEGER);
            $result = $stmt->execute();
             $changes = $this->db->changes();
             if ($result) $result->finalize();
             $stmt->close();
             return $result !== false && $changes > 0; // Успех только если запрос выполнился и что-то удалил
        } catch (\Exception $e) {
            error_log("Error deleting message (ID: $messageId): " . $e->getMessage());
            if ($stmt instanceof \SQLite3Stmt) $stmt->close();
            return false;
        }
    }

    /**
     * Добавляет новое сообщение в базу данных.
     *
     * @param int    $sessionId ID сессии.
     * @param string $sender    'user' или 'assistant'.
     * @param string $content   Содержимое сообщения.
     * @param string|int|null $userId ID пользователя (может быть строка 'anon_...' или число).
     * @return int|false ID вставленного сообщения или false в случае ошибки.
     */
    public function addMessage(int $sessionId, string $sender, string $content, $userId = null): int|false {
         if ($sessionId <= 0 || empty($sender) || $content === null) return false; // Базовая проверка
        $stmt = null;
        try {
            $stmt = $this->db->prepare("
                INSERT INTO messages (session_id, user_id, sender, content)
                VALUES (:session_id, :user_id, :sender, :content)
            ");
            if ($stmt === false) {
                error_log("Failed to prepare statement for adding message to session $sessionId: " . $this->db->lastErrorMsg());
                return false;
            }

            $stmt->bindValue(':session_id', $sessionId, \SQLITE3_INTEGER);

            $userIdType = \SQLITE3_NULL;
            $actualUserId = null;
            if ($sender === 'user' && $userId !== null && $userId !== '') {
                 $actualUserId = $userId;
                 // Определяем тип user_id
                 $userIdType = ctype_digit((string)$userId) ? \SQLITE3_INTEGER : \SQLITE3_TEXT;
                 if ($userIdType === \SQLITE3_INTEGER) {
                     $actualUserId = (int)$userId; // Приводим к числу, если это цифры
                 }
            }
            $stmt->bindValue(':user_id', $actualUserId, $userIdType);
            $stmt->bindValue(':sender', $sender, \SQLITE3_TEXT);
            $stmt->bindValue(':content', $content, \SQLITE3_TEXT);

            $result = $stmt->execute();
            $lastId = false;
            if ($result) {
                $lastId = $this->db->lastInsertRowID();
                $result->finalize();
            } else {
                error_log("Failed to execute add message to session ($sessionId): " . $this->db->lastErrorMsg());
            }
            $stmt->close();
            return $lastId; // Вернет ID (>0) или false

        } catch (\Exception $e) {
            error_log("Exception adding message to session ($sessionId): " . $e->getMessage());
             if ($stmt instanceof \SQLite3Stmt) $stmt->close();
            return false;
        }
    }

     /**
     * Обновляет содержимое сообщения пользователя и его timestamp.
     *
     * @param int $messageId ID сообщения для обновления.
     * @param string $newContent Новое содержимое.
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function updateMessageContent(int $messageId, string $newContent): bool {
         if ($messageId <= 0) return false; // Базовая проверка
        $stmt = null;
        try {
            $stmt = $this->db->prepare("
                UPDATE messages
                SET content = :content, timestamp = CURRENT_TIMESTAMP
                WHERE id = :message_id AND sender = 'user' -- Обновляем только сообщения пользователя
            ");
             if ($stmt === false) {
                 error_log("Failed to prepare statement for updating message ID $messageId: " . $this->db->lastErrorMsg());
                 return false;
             }
            $stmt->bindValue(':content', $newContent, \SQLITE3_TEXT);
            $stmt->bindValue(':message_id', $messageId, \SQLITE3_INTEGER);
            $result = $stmt->execute();
             $changes = $this->db->changes();
             if ($result) $result->finalize();
             $stmt->close();
            return $result !== false && $changes > 0; // Успех, если запрос выполнился и что-то изменил
        } catch (\Exception $e) {
            error_log("Error updating message content (ID: $messageId): " . $e->getMessage());
            if ($stmt instanceof \SQLite3Stmt) $stmt->close();
            return false;
        }
    }

    /**
     * Удаляет сообщения по списку ID в рамках одной сессии.
     *
     * @param int $sessionId ID сессии (ОБЯЗАТЕЛЬНО int > 0).
     * @param array $messageIds Массив ID сообщений для удаления.
     * @return int|false Количество удаленных сообщений или false в случае ошибки.
     */
    public function deleteMessagesByIds(int $sessionId, array $messageIds): int|false {
         // Проверка $sessionId
         if ($sessionId <= 0) {
              error_log("Invalid session ID provided for deletion: " . $sessionId);
              return false;
         }

        if (empty($messageIds)) {
            return 0; // Нечего удалять
        }

        // Валидация ID сообщений
        $validatedIds = array_filter($messageIds, function($id) {
             return (is_int($id) && $id > 0) || (is_string($id) && ctype_digit($id) && (int)$id > 0);
         });
         $validatedIds = array_map('intval', $validatedIds); // Приводим к int

        if (empty($validatedIds)) {
            error_log("No valid positive integer message IDs provided for deletion in session $sessionId.");
            return false;
        }

        $placeholders = implode(',', array_fill(0, count($validatedIds), '?'));
        $stmt = null; // Инициализация

        try {
            $sql = "DELETE FROM messages WHERE id IN ($placeholders) AND session_id = ?";
            $stmt = $this->db->prepare($sql);

            if ($stmt === false) {
                 error_log("Failed to prepare statement for deleting messages by IDs in session ($sessionId): " . $this->db->lastErrorMsg());
                 return false;
            }

            $i = 1;
            foreach ($validatedIds as $id) {
                 if (!$stmt->bindValue($i++, $id, \SQLITE3_INTEGER)) {
                     error_log("Failed to bind message ID ($id) for deletion in session ($sessionId).");
                     $stmt->close();
                     return false;
                 }
            }
            // Биндинг session_id
            if (!$stmt->bindValue($i, $sessionId, \SQLITE3_INTEGER)) {
                  error_log("Failed to bind session ID ($sessionId) for message deletion.");
                  $stmt->close();
                  return false;
            }

            $result = $stmt->execute();
            $changes = 0;
             if ($result) {
                 $changes = $this->db->changes(); // Получаем кол-во удаленных строк
                 $result->finalize();
             } else {
                  error_log("Failed to execute delete messages by IDs in session ($sessionId): " . $this->db->lastErrorMsg());
             }
             $stmt->close();
             return $result !== false ? $changes : false; // Возвращаем кол-во или false при ошибке

        } catch (\Exception $e) {
            error_log("Exception deleting messages by IDs in session ($sessionId): " . $e->getMessage());
            if ($stmt instanceof \SQLite3Stmt) {
                 $stmt->close();
            }
            return false;
        }
    }


    /**
     * Удаляет сообщения по списку ID, независимо от сессии.
     *
     * @param array $messageIds Массив ID сообщений для удаления (строки или числа).
     * @return int|false Количество удаленных сообщений или false в случае ошибки.
     */
    public function deleteMessagesByIdList(array $messageIds): int|false {
        if (empty($messageIds)) {
            return 0;
        }

        // Валидация ID
        $validatedIds = array_filter($messageIds, function($id) {
             return (is_int($id) && $id > 0) || (is_string($id) && ctype_digit($id) && (int)$id > 0);
         });
         $validatedIds = array_map('intval', $validatedIds);

        if (empty($validatedIds)) {
            error_log("No valid positive integer message IDs provided for deletion in the list.");
            return false;
        }

        $placeholders = implode(',', array_fill(0, count($validatedIds), '?'));
        $stmt = null;

        try {
            $sql = "DELETE FROM messages WHERE id IN ($placeholders)";
            $stmt = $this->db->prepare($sql);

            if ($stmt === false) {
                 error_log("Failed to prepare statement for deleting messages by ID list: " . $this->db->lastErrorMsg());
                 return false;
            }

            $i = 1;
            foreach ($validatedIds as $id) {
                 if (!$stmt->bindValue($i++, $id, \SQLITE3_INTEGER)) {
                     error_log("Failed to bind message ID ($id) for deletion by list.");
                     $stmt->close();
                     return false;
                 }
            }

            $result = $stmt->execute();
             $changes = 0;
             if ($result) {
                 $changes = $this->db->changes();
                 $result->finalize();
             } else {
                 error_log("Failed to execute delete messages by ID list: " . $this->db->lastErrorMsg());
             }
             $stmt->close();
             return $result !== false ? $changes : false;

        } catch (\Exception $e) {
            error_log("Exception deleting messages by ID list: " . $e->getMessage());
             if ($stmt instanceof \SQLite3Stmt) {
                 $stmt->close();
             }
            return false;
        }
    }


    /**
     * Получает последние N сообщений для формирования контекста API.
     *
     * @param int $sessionId ID сессии.
     * @param int $limit Количество сообщений.
     * @return array Массив сообщений в формате ['id' => ..., 'role' => ..., 'content' => ...].
     */
    public function getContextMessages(int $sessionId, int $limit = 10): array {
        if ($sessionId <= 0 || $limit <= 0) return []; // Базовая проверка
        $context = [];
        $stmtCtx = null;
        try {
            // Получаем последние N сообщений в правильном хронологическом порядке
            $stmtCtx = $this->db->prepare("
                SELECT id, sender, content
                FROM (
                    SELECT id, sender, content, timestamp
                    FROM messages
                    WHERE session_id = :session_id
                    ORDER BY timestamp DESC
                    LIMIT :limit
                ) AS recent_messages
                ORDER BY timestamp ASC
            ");

             if ($stmtCtx === false) {
                 error_log("Failed to prepare statement for getting context messages (Session: $sessionId): " . $this->db->lastErrorMsg());
                 return [];
             }

            $stmtCtx->bindValue(':session_id', $sessionId, \SQLITE3_INTEGER);
            $stmtCtx->bindValue(':limit', $limit, \SQLITE3_INTEGER);
            $resultCtx = $stmtCtx->execute();

            if ($resultCtx === false) {
                error_log("Failed to execute query for context messages (Session: $sessionId): " . $this->db->lastErrorMsg());
                 $stmtCtx->close();
                return [];
            }

            $tempContext = [];
            while ($row = $resultCtx->fetchArray(\SQLITE3_ASSOC)) {
                $tempContext[] = [
                    'id' => (int)$row['id'], // Приводим ID к int
                    'role' => $row['sender'] === 'user' ? 'user' : 'assistant',
                    'content' => $row['content']
                ];
            }
             $resultCtx->finalize();
             $stmtCtx->close();
             return $tempContext; // Сообщения уже в правильном хронологическом порядке
        } catch (\Exception $e) {
             error_log("Error getting context messages for session ($sessionId): " . $e->getMessage());
              if ($stmtCtx instanceof \SQLite3Stmt) {
                 $stmtCtx->close();
             }
             return [];
        }
    }

    /**
     * Получает ID всех сообщений в сессии, следующих за указанным сообщением.
     *
     * @param int $sessionId ID сессии.
     * @param int $messageId ID сообщения, после которого нужно искать.
     * @return array Массив ID сообщений (int).
     */
    public function getSubsequentMessageIds(int $sessionId, int $messageId): array {
         if ($sessionId <= 0 || $messageId <= 0) return []; // Базовая проверка
        $ids = [];
        $stmt = null;
        try {
            $stmt = $this->db->prepare("
                SELECT id FROM messages
                WHERE session_id = :session_id AND id > :message_id
                ORDER BY id ASC
            ");

             if ($stmt === false) {
                 error_log("Failed to prepare statement for subsequent message IDs (Session: $sessionId, After: $messageId): " . $this->db->lastErrorMsg());
                 return [];
             }

            $stmt->bindValue(':session_id', $sessionId, \SQLITE3_INTEGER);
            $stmt->bindValue(':message_id', $messageId, \SQLITE3_INTEGER);
            $result = $stmt->execute();

            if ($result === false) {
                error_log("Failed to execute query for subsequent message IDs (Session: $sessionId, After: $messageId): " . $this->db->lastErrorMsg());
                $stmt->close();
                return [];
            }

            while ($row = $result->fetchArray(\SQLITE3_ASSOC)) {
                $ids[] = (int)$row['id']; // Сохраняем как int
            }
            $result->finalize();
            $stmt->close();

        } catch (\Exception $e) {
            error_log("Error getting subsequent message IDs for session ($sessionId) after message ($messageId): " . $e->getMessage());
             if ($stmt instanceof \SQLite3Stmt) {
                 $stmt->close();
             }
            return [];
        }
        return $ids;
    }

    /**
     * Удаляет чат-сессию и все связанные с ней сообщения и файлы.
     *
     * @param int $sessionId ID сессии.
     * @return array|false Массив со статистикой удалений ['messages' => count, 'files' => count] или false в случае ошибки.
     */
    public function deleteChatSession(int $sessionId): array|false {
         if ($sessionId <= 0) {
             error_log("Invalid session ID provided for chat session deletion: " . $sessionId);
             return false;
         }

        $deletedStats = ['messages' => 0, 'files' => 0];
        $stmtFiles = null;
        $stmtMessages = null;
        $stmtSession = null;
        $transactionStartedByThisMethod = false; // Флаг, что транзакцию начали мы

        try {
             // --- ИСПРАВЛЕНО: Убрана проверка inTransaction ---
             // Просто пытаемся начать транзакцию
             if (!$this->db->exec('BEGIN')) {
                 // Если BEGIN не удался (возможно, уже в транзакции или другая ошибка)
                 // Продолжаем без явного управления транзакцией здесь, но логируем
                 error_log("Could not start transaction for deleting session $sessionId (maybe already in transaction?): " . $this->db->lastErrorMsg());
             } else {
                  // Транзакцию начали мы
                  $transactionStartedByThisMethod = true;
             }
             // --- КОНЕЦ ИСПРАВЛЕНИЯ ---

             // --- Удаление связанных файлов ---
             if (class_exists(UploadedFile::class)) {
                 $stmtFiles = $this->db->prepare("SELECT id, file_path FROM uploaded_files WHERE session_id = :session_id");
                 if ($stmtFiles) {
                     $stmtFiles->bindValue(':session_id', $sessionId, \SQLITE3_INTEGER);
                     $filesResult = $stmtFiles->execute();
                     $filesToDelete = [];
                     if ($filesResult) {
                         while ($file = $filesResult->fetchArray(\SQLITE3_ASSOC)) {
                             $filesToDelete[] = $file;
                         }
                         $filesResult->finalize();
                     } else {
                         error_log("Could not execute fetch associated files query for session $sessionId: " . $this->db->lastErrorMsg());
                     }
                     $stmtFiles->close();

                     if (!empty($filesToDelete)) {
                         $fileModel = new UploadedFile();
                         foreach ($filesToDelete as $file) {
                             if ($fileModel->deleteFile((int)$file['id'])) { // Убедимся что ID файла - int
                                 $deletedStats['files']++;
                             } else {
                                 error_log("Failed to delete associated file (ID: {$file['id']}) during session deletion (ID: $sessionId). Continuing...");
                             }
                         }
                     }
                 } else {
                      error_log("Failed to prepare statement for fetching associated files for session $sessionId: " . $this->db->lastErrorMsg());
                 }
             } else {
                  error_log("UploadedFile model class not found. Skipping file deletion for session $sessionId.");
             }
             // --- Конец удаления файлов ---


            // 3. Удаляем сообщения сессии
            $stmtMessages = $this->db->prepare("DELETE FROM messages WHERE session_id = :session_id");
             if (!$stmtMessages) {
                 throw new \Exception("Failed to prepare statement for deleting messages in session $sessionId: " . $this->db->lastErrorMsg());
             }
            $stmtMessages->bindValue(':session_id', $sessionId, \SQLITE3_INTEGER);
            $resultMessages = $stmtMessages->execute();
            if ($resultMessages) {
                 $deletedStats['messages'] = $this->db->changes();
                 $resultMessages->finalize();
            } else {
                 $errorMsg = $this->db->lastErrorMsg();
                 $stmtMessages->close();
                 throw new \Exception("Failed to delete messages for session $sessionId. Error: " . $errorMsg);
            }
            $stmtMessages->close();


            // 4. Удаляем саму сессию
            $stmtSession = $this->db->prepare("DELETE FROM chat_sessions WHERE id = :session_id");
             if (!$stmtSession) {
                 throw new \Exception("Failed to prepare statement for deleting chat session $sessionId: " . $this->db->lastErrorMsg());
             }
            $stmtSession->bindValue(':session_id', $sessionId, \SQLITE3_INTEGER);
            $resultSession = $stmtSession->execute();
             $sessionChanges = $this->db->changes();
             if ($resultSession) $resultSession->finalize();
             $stmtSession->close();

             if (!$resultSession || $sessionChanges === 0) {
                  $errorMsg = $this->db->lastErrorMsg();
                  throw new \Exception("Failed to delete chat session record (ID: $sessionId) or session not found. Error: " . $errorMsg);
             }


             // --- ИСПРАВЛЕНО: Коммитим только если мы начали транзакцию ---
             if ($transactionStartedByThisMethod) {
                 if (!$this->db->exec('COMMIT')) {
                     error_log("Failed to commit transaction for deleting session $sessionId: " . $this->db->lastErrorMsg());
                     // Пытаемся откатить, если коммит не удался
                     $this->db->exec('ROLLBACK');
                     throw new \Exception("Failed to commit transaction.");
                 }
             }
             // --- КОНЕЦ ИСПРАВЛЕНИЯ ---
            return $deletedStats;

        } catch (\Exception $e) {
             // --- ИСПРАВЛЕНО: Откатываем только если мы начали транзакцию ---
             if ($transactionStartedByThisMethod) {
                 $this->db->exec('ROLLBACK');
             }
             // --- КОНЕЦ ИСПРАВЛЕНИЯ ---
            error_log("Error deleting chat session (ID: $sessionId): " . $e->getMessage());
             // Закрываем statements, если они были открыты
             if ($stmtFiles instanceof \SQLite3Stmt && !$stmtFiles->close()) {}
             if ($stmtMessages instanceof \SQLite3Stmt && !$stmtMessages->close()) {}
             if ($stmtSession instanceof \SQLite3Stmt && !$stmtSession->close()) {}
            return false;
        }
    }

    /**
     * Удаляет все сообщения и чаты. (Используется в clearAll в контроллере)
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function deleteAllMessagesAndChats(): bool
    {
        try {
            // Используем транзакцию для атомарности
            if (!$this->db->exec('BEGIN')) {
                 error_log("deleteAllMessagesAndChats: Failed to begin transaction: " . $this->db->lastErrorMsg());
                 // Попробуем продолжить без явной транзакции
            }

            // Удаляем сообщения
            if (!$this->db->exec('DELETE FROM messages')) {
                 throw new \Exception('Failed to delete messages: ' . $this->db->lastErrorMsg());
            }
            // Удаляем сессии
            if (!$this->db->exec('DELETE FROM chat_sessions')) {
                 throw new \Exception('Failed to delete chat sessions: ' . $this->db->lastErrorMsg());
            }
            // Удаляем связанные файлы (если есть)
             if (class_exists(UploadedFile::class)) {
                 if (!$this->db->exec('DELETE FROM uploaded_files')) {
                      // Логируем, но не считаем критичной ошибкой для этого метода
                      error_log("deleteAllMessagesAndChats: Could not clear uploaded_files: " . $this->db->lastErrorMsg());
                 }
                 // ВАЖНО: Физическое удаление файлов с диска здесь не происходит!
                 // Это нужно делать отдельно, например, в модели UploadedFile.
            }


            // Коммитим транзакцию, если она была начата
            if (!$this->db->exec('COMMIT')) {
                 // Логируем ошибку коммита, но считаем операцию условно успешной, т.к. DELETE выполнились
                 error_log("deleteAllMessagesAndChats: Failed to commit transaction: " . $this->db->lastErrorMsg());
                 // Можно попытаться откатить, но это уже не так важно
                 $this->db->exec('ROLLBACK');
            }

            return true;
        } catch (\Throwable $e) {
             error_log("Error in deleteAllMessagesAndChats: " . $e->getMessage());
             // Пытаемся откатить транзакцию в случае ошибки
             $this->db->exec('ROLLBACK');
             return false;
        }
    }

    /**
     * Получает все сообщения для конкретной сессии
     *
     * @param int $sessionId ID сессии
     * @return array Массив сообщений с полной информацией
     */
    public function getSessionMessages(int $sessionId): array
    {
        if ($sessionId <= 0) return [];

        $messages = [];
        $stmt = null;

        try {
            $stmt = $this->db->prepare("
                SELECT id, sender, content, timestamp
                FROM messages
                WHERE session_id = :session_id
                ORDER BY timestamp ASC
            ");

            if ($stmt === false) {
                error_log("Failed to prepare statement for getting session messages (Session: $sessionId): " . $this->db->lastErrorMsg());
                return [];
            }

            $stmt->bindValue(':session_id', $sessionId, \SQLITE3_INTEGER);
            $result = $stmt->execute();

            if ($result === false) {
                error_log("Failed to execute query for session messages (Session: $sessionId): " . $this->db->lastErrorMsg());
                $stmt->close();
                return [];
            }

            while ($row = $result->fetchArray(\SQLITE3_ASSOC)) {
                $messages[] = [
                    'id' => (int)$row['id'],
                    'sender' => $row['sender'],
                    'content' => $row['content'],
                    'timestamp' => $row['timestamp']
                ];
            }

            $result->finalize();
            $stmt->close();

            return $messages;
        } catch (\Exception $e) {
            error_log("Error getting session messages for session ($sessionId): " . $e->getMessage());
            if ($stmt) {
                $stmt->close();
            }
            return [];
        }
    }
}