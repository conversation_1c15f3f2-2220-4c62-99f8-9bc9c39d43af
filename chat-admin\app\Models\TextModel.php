<?php

namespace App\Models;

use PDO;
use PDOException;

class TextModel
{
    private $db;

    public function __construct()
    {
        // Используем существующий класс Database для получения соединения
        $this->db = Database::getInstance();
    }

    /**
     * Получает все тексты для указанного языка.
     *
     * @param string $language Код языка (по умолчанию 'ru').
     * @return array Ассоциативный массив [text_key => text_value] или пустой массив в случае ошибки.
     */
    /**
     * Получает все тексты для указанного языка, включая ID.
     *
     * @param string $language Код языка (по умолчанию 'ru').
     * @return array Массив объектов, каждый с полями id, key, text, description. Пустой массив в случае ошибки или отсутствия текстов.
     */
    public function getAllTexts(string $language = 'ru'): array
    {
        try {
            $stmt = $this->db->prepare("SELECT id, key, language, text FROM texts WHERE language = :language ORDER BY key");
            // Используем bindValue с типом SQLITE3_TEXT
            $stmt->bindValue(':language', $language, SQLITE3_TEXT);
            // execute() для SELECT запросов возвращает SQLite3Result
            $result = $stmt->execute();

            $texts = [];
            if ($result) {
                // Используем fetchArray на объекте результата
                while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                    $texts[] = $row;
                }
                $result->finalize(); // Закрываем объект результата
            }
            $stmt->close(); // Закрываем стейтмент
            return $texts;

        } catch (\Exception $e) {
            error_log("Error fetching texts: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Получает все тексты для указанного языка в формате для JS (только ключ-значение).
     *
     * @param string $language Код языка (по умолчанию 'ru').
     * @return array Ассоциативный массив [key => text] или пустой массив в случае ошибки.
     */
    public function getAllTextsForJs(string $language = 'ru'): array
    {
        try {
            $stmt = $this->db->prepare("SELECT key, text FROM texts WHERE language = :language");
            // Используем bindValue с типом SQLITE3_TEXT
            $stmt->bindValue(':language', $language, SQLITE3_TEXT);
            // execute() для SELECT запросов возвращает SQLite3Result
            $result = $stmt->execute();

            $texts = [];
            if ($result) {
                // Используем fetchArray на объекте результата
                while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                    // Формируем ассоциативный массив key => text
                    if (isset($row['key']) && isset($row['text'])) {
                         $texts[$row['key']] = $row['text'];
                    }
                }
                $result->finalize(); // Закрываем объект результата
            }
            $stmt->close(); // Закрываем стейтмент
            return $texts;

        } catch (\Exception $e) {
            error_log("Error fetching texts for JS: " . $e->getMessage());
            return [];
        }
    }


    /**
     * Обновляет значение текста по ключу и языку.
     *
     * @param string $key Ключ текста.
     * @param string $text Новое значение текста.
     * @param string $language Код языка (по умолчанию 'ru').
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function updateText(string $key, string $text, string $language = 'ru'): bool
    {
        try {
            $stmt = $this->db->prepare("UPDATE texts SET text = :text WHERE key = :key AND language = :language");
            // Используем bindValue с типом SQLITE3_TEXT
            $stmt->bindValue(':text', $text, SQLITE3_TEXT);
            $stmt->bindValue(':key', $key, SQLITE3_TEXT);
            $stmt->bindValue(':language', $language, SQLITE3_TEXT);

            // execute() может возвращать SQLite3Result или bool в зависимости от версии
            $result = $stmt->execute();

            // Проверяем тип результата и обрабатываем соответственно
            if ($result === true) {
                // Если execute() вернул true, значит запрос выполнен успешно
                return true;
            } elseif ($result instanceof \SQLite3Result) {
                // Если execute() вернул SQLite3Result, закрываем результат и возвращаем true
                $result->finalize();
                return true;
            } else {
                // Если execute() вернул что-то другое, считаем это ошибкой
                error_log("Unexpected result type from execute() in updateText: " . gettype($result));
                return false;
            }
        } catch (\Exception $e) { // Ловим общее исключение, так как PDOException может не быть
            error_log("Error updating text '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Получает все доступные языки из таблицы texts.
     *
     * @return array Массив строк с кодами языков или пустой массив в случае ошибки.
     */
    public function getAvailableLanguages(): array
    {
        try {
            // query() возвращает SQLite3Result
            $result = $this->db->query("SELECT DISTINCT language FROM texts ORDER BY language");
            $languages = [];
            if ($result) {
                // Используем fetchArray на объекте результата
                while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                    $languages[] = $row['language'];
                }
                $result->finalize(); // Закрываем объект результата
            }
            return $languages;
        } catch (\Exception $e) { // Ловим общее исключение
            error_log("Error fetching available languages: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Обновляет значение текста по его ID.
     *
     * @param int $id ID текстовой записи.
     * @param string $text Новое значение текста.
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function updateTextById(int $id, string $text): bool
    {
        try {
            $stmt = $this->db->prepare("UPDATE texts SET text = :text WHERE id = :id");
            // Используем bindValue с соответствующими типами SQLITE3
            $stmt->bindValue(':text', $text, SQLITE3_TEXT);
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);

            // execute() может возвращать SQLite3Result или bool в зависимости от версии
            $result = $stmt->execute();

            // Проверяем тип результата и обрабатываем соответственно
            if ($result === true) {
                // Если execute() вернул true, значит запрос выполнен успешно
                return true;
            } elseif ($result instanceof \SQLite3Result) {
                // Если execute() вернул SQLite3Result, закрываем результат и возвращаем true
                $result->finalize();
                return true;
            } else {
                // Если execute() вернул что-то другое, считаем это ошибкой
                error_log("Unexpected result type from execute() in updateTextById: " . gettype($result));
                return false;
            }
        } catch (\Exception $e) { // Ловим общее исключение
            error_log("Error updating text ID {$id}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Получает текст по его ID.
     *
     * @param int $id ID текстовой записи.
     * @return array|null Ассоциативный массив с данными текста или null, если текст не найден.
     */
    public function getTextById(int $id): ?array
    {
        try {
            $stmt = $this->db->prepare("SELECT id, key, language, text FROM texts WHERE id = :id");
            $stmt->bindValue(':id', $id, SQLITE3_INTEGER);
            $result = $stmt->execute();

            if ($result) {
                $row = $result->fetchArray(SQLITE3_ASSOC);
                $result->finalize();
                $stmt->close();
                return $row ?: null;
            }

            return null;
        } catch (\Exception $e) {
            error_log("Error fetching text ID {$id}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Получает текст по его ключу и языку.
     *
     * @param string $key Ключ текста.
     * @param string $language Код языка (по умолчанию 'ru').
     * @return array|null Ассоциативный массив с данными текста или null, если текст не найден.
     */
    public function getTextByKey(string $key, string $language = 'ru'): ?array
    {
        try {
            $stmt = $this->db->prepare("SELECT id, key, language, text FROM texts WHERE key = :key AND language = :language");
            $stmt->bindValue(':key', $key, SQLITE3_TEXT);
            $stmt->bindValue(':language', $language, SQLITE3_TEXT);
            $result = $stmt->execute();

            if ($result) {
                $row = $result->fetchArray(SQLITE3_ASSOC);
                $result->finalize();
                $stmt->close();
                return $row ?: null;
            }

            return null;
        } catch (\Exception $e) {
            error_log("Error fetching text by key '{$key}': " . $e->getMessage());
            return null;
        }
    }

    /**
     * Добавляет новый текст.
     *
     * @param string $key Ключ текста.
     * @param string $text Значение текста.
     * @param string $language Код языка (по умолчанию 'ru').
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function addText(string $key, string $text, string $language = 'ru'): bool
    {
        try {
            $stmt = $this->db->prepare("INSERT INTO texts (key, language, text) VALUES (:key, :language, :text)");
            $stmt->bindValue(':key', $key, SQLITE3_TEXT);
            $stmt->bindValue(':language', $language, SQLITE3_TEXT);
            $stmt->bindValue(':text', $text, SQLITE3_TEXT);

            $result = $stmt->execute();

            if ($result === true || $result instanceof \SQLite3Result) {
                if ($result instanceof \SQLite3Result) {
                    $result->finalize();
                }
                return true;
            } else {
                error_log("Unexpected result type from execute() in addText: " . gettype($result));
                return false;
            }
        } catch (\Exception $e) {
            error_log("Error adding text '{$key}': " . $e->getMessage());
            return false;
        }
    }
}
