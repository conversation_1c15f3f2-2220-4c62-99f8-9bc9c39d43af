// chat-js/state.js

// Состояние основного чата
let isChatOpen = false;
let currentSessionId = null;
let messageHistory = []; // { id, sender, text }
let chatSessions = []; // { id, title }
let isTyping = false;
let activeEditingMessageId = null; // ID сообщения, которое редактируется
let isFirstTimeUser = null; // Флаг для отслеживания первого входа пользователя

// Состояние модального окна голосового вызова
let isVoiceModalOpen = false;
let isListening = false; // Идет ли процесс распознавания
let shouldBeListening = false; // Должен ли микрофон быть активен (намерение пользователя)
let isSpeaking = false; // Говорит ли ИИ
let isWaitingForServer = false; // Ожидаем ли ответ от voice_api
let isMicEnabled = true; // Включен ли микрофон пользователем в модалке
let lastFinalTranscript = ""; // Последний финальный результат речи

// Экспортируем функции для получения и изменения состояния
export const getState = () => ({
    isChatOpen,
    currentSessionId,
    messageHistory,
    chatSessions,
    isTyping,
    activeEditingMessageId,
    isFirstTimeUser,
    isVoiceModalOpen,
    isListening,
    shouldBeListening,
    isSpeaking,
    isWaitingForServer,
    isMicEnabled,
    lastFinalTranscript,
});

export const setState = (newState) => {
    if (newState.isChatOpen !== undefined) isChatOpen = newState.isChatOpen;
    if (newState.currentSessionId !== undefined) currentSessionId = newState.currentSessionId;
    if (newState.messageHistory !== undefined) messageHistory = newState.messageHistory;
    if (newState.chatSessions !== undefined) chatSessions = newState.chatSessions;
    if (newState.isTyping !== undefined) isTyping = newState.isTyping;
    if (newState.activeEditingMessageId !== undefined) activeEditingMessageId = newState.activeEditingMessageId;
    if (newState.isFirstTimeUser !== undefined) isFirstTimeUser = newState.isFirstTimeUser;
    if (newState.isVoiceModalOpen !== undefined) isVoiceModalOpen = newState.isVoiceModalOpen;
    if (newState.isListening !== undefined) isListening = newState.isListening;
    if (newState.shouldBeListening !== undefined) shouldBeListening = newState.shouldBeListening;
    if (newState.isSpeaking !== undefined) isSpeaking = newState.isSpeaking;
    if (newState.isWaitingForServer !== undefined) isWaitingForServer = newState.isWaitingForServer;
    if (newState.isMicEnabled !== undefined) isMicEnabled = newState.isMicEnabled;
    if (newState.lastFinalTranscript !== undefined) lastFinalTranscript = newState.lastFinalTranscript;

    // Можно добавить логику сохранения в localStorage для некоторых состояний, если нужно
    if (newState.isChatOpen !== undefined) {
         localStorage.setItem('chatState', JSON.stringify({ isChatOpen: isChatOpen }));
    }

    // Сохраняем флаг первого входа в localStorage
    if (newState.isFirstTimeUser !== undefined) {
         localStorage.setItem('isFirstTimeUser', JSON.stringify(isFirstTimeUser));
    }
};

// Функции для работы с историей сообщений
export const addMessageToHistory = (message) => {
    // Проверяем, нет ли уже сообщения с таким ID (особенно для временных)
    const existingIndex = messageHistory.findIndex(m => m.id === message.id);
    if (existingIndex === -1) {
        messageHistory.push(message);
    } else {
        // Обновляем существующее (например, временное)
        messageHistory[existingIndex] = message;
    }
};

export const updateMessageInHistory = (messageId, newText) => {
    const index = messageHistory.findIndex(m => String(m.id) === String(messageId));
    if (index !== -1) {
        messageHistory[index].text = newText;
    }
};

export const removeMessagesAfter = (messageId) => {
     const index = messageHistory.findIndex(m => String(m.id) === String(messageId));
     if (index !== -1) {
         messageHistory.splice(index + 1);
    }
};

/**
 * Обновляет ID сообщения в истории (заменяет временный ID на постоянный).
 * @param {string} tempId - Временный ID сообщения.
 * @param {string|number} finalId - Постоянный ID, полученный от сервера.
 */
export const updateMessageHistoryId = (tempId, finalId) => {
    const index = messageHistory.findIndex(m => m.id === tempId);
    if (index !== -1) {
        messageHistory[index].id = finalId;
        console.log(`State: Updated message history ID from ${tempId} to ${finalId}`);
    } else {
        console.warn(`State: Could not find message with tempId ${tempId} to update ID.`);
    }
};

export const findMessageInHistory = (messageId) => {
    return messageHistory.find(m => String(m.id) === String(messageId));
};

export const clearMessageHistory = () => {
    messageHistory = [];
};

// Функция для инициализации флага первого входа из localStorage
export const initFirstTimeUser = () => {
    const saved = localStorage.getItem('isFirstTimeUser');
    if (saved !== null) {
        isFirstTimeUser = JSON.parse(saved);
    } else {
        // Если флаг не найден в localStorage, значит это первый вход
        isFirstTimeUser = true;
        localStorage.setItem('isFirstTimeUser', JSON.stringify(true));
    }
    console.log("State: First time user flag initialized:", isFirstTimeUser);
};

// Функция для отметки, что пользователь больше не новичок
export const markUserAsReturning = () => {
    isFirstTimeUser = false;
    localStorage.setItem('isFirstTimeUser', JSON.stringify(false));
    console.log("State: User marked as returning (not first time)");
};
