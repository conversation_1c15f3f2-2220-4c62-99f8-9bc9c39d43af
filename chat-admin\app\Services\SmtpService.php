<?php

namespace App\Services;

/**
 * Простой SMTP сервис для отправки email
 */
class SmtpService
{
    private $host;
    private $port;
    private $username;
    private $password;
    private $encryption;
    private $socket;
    private $debug = false;

    public function __construct($host, $port, $username, $password, $encryption = 'tls')
    {
        $this->host = $host;
        $this->port = $port;
        $this->username = $username;
        $this->password = $password;
        $this->encryption = strtolower($encryption);
    }

    /**
     * Включить/выключить отладку
     */
    public function setDebug($debug = true)
    {
        $this->debug = $debug;
    }

    /**
     * Отправить email
     */
    public function sendMail($to, $subject, $body, $from, $fromName = '')
    {
        try {
            $this->connect();
            $this->authenticate();
            
            // Отправляем команды SMTP
            $this->command("MAIL FROM: <$from>");
            $this->command("RCPT TO: <$to>");
            $this->command("DATA");
            
            // Формируем заголовки
            $headers = $this->buildHeaders($to, $subject, $from, $fromName);
            
            // Отправляем заголовки и тело письма
            $this->send($headers . "\r\n" . $body . "\r\n.");
            
            $this->command("QUIT");
            $this->disconnect();
            
            return true;
            
        } catch (\Exception $e) {
            $this->log("SMTP Error: " . $e->getMessage());
            $this->disconnect();
            return false;
        }
    }

    /**
     * Подключение к SMTP серверу
     */
    private function connect()
    {
        $context = stream_context_create();
        
        if ($this->encryption === 'ssl') {
            $this->socket = stream_socket_client(
                "ssl://{$this->host}:{$this->port}",
                $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context
            );
        } else {
            $this->socket = stream_socket_client(
                "tcp://{$this->host}:{$this->port}",
                $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context
            );
        }

        if (!$this->socket) {
            throw new \Exception("Cannot connect to SMTP server: $errstr ($errno)");
        }

        $this->getResponse(); // Читаем приветствие сервера
        
        $this->command("EHLO " . ($_SERVER['SERVER_NAME'] ?? 'localhost'));
        
        // Если используется TLS, включаем шифрование
        if ($this->encryption === 'tls') {
            $this->command("STARTTLS");
            
            if (!stream_socket_enable_crypto($this->socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                throw new \Exception("Failed to enable TLS encryption");
            }
            
            $this->command("EHLO " . ($_SERVER['SERVER_NAME'] ?? 'localhost'));
        }
    }

    /**
     * Аутентификация
     */
    private function authenticate()
    {
        if (!empty($this->username) && !empty($this->password)) {
            $this->command("AUTH LOGIN");
            $this->command(base64_encode($this->username));
            $this->command(base64_encode($this->password));
        }
    }

    /**
     * Отправка SMTP команды
     */
    private function command($command)
    {
        $this->send($command);
        return $this->getResponse();
    }

    /**
     * Отправка данных в сокет
     */
    private function send($data)
    {
        $this->log("SEND: " . trim($data));
        fwrite($this->socket, $data . "\r\n");
    }

    /**
     * Получение ответа от сервера
     */
    private function getResponse()
    {
        $response = '';
        while (($line = fgets($this->socket, 515)) !== false) {
            $response .= $line;
            if (substr($line, 3, 1) === ' ') {
                break;
            }
        }
        
        $this->log("RECV: " . trim($response));
        
        $code = (int)substr($response, 0, 3);
        if ($code >= 400) {
            throw new \Exception("SMTP Error: $response");
        }
        
        return $response;
    }

    /**
     * Формирование заголовков письма
     */
    private function buildHeaders($to, $subject, $from, $fromName = '')
    {
        $domain = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $messageId = uniqid() . '@' . $domain;
        
        $fromHeader = !empty($fromName) ? "$fromName <$from>" : $from;
        
        $headers = [];
        $headers[] = "Date: " . date('r');
        $headers[] = "From: $fromHeader";
        $headers[] = "To: $to";
        $headers[] = "Subject: =?UTF-8?B?" . base64_encode($subject) . "?=";
        $headers[] = "Message-ID: <$messageId>";
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-Type: text/plain; charset=UTF-8";
        $headers[] = "Content-Transfer-Encoding: 8bit";
        $headers[] = "X-Mailer: PHP SMTP Service";
        $headers[] = "X-Priority: 3";
        
        return implode("\r\n", $headers);
    }

    /**
     * Закрытие соединения
     */
    private function disconnect()
    {
        if ($this->socket) {
            fclose($this->socket);
            $this->socket = null;
        }
    }

    /**
     * Логирование
     */
    private function log($message)
    {
        if ($this->debug) {
            error_log("SmtpService: " . $message);
        }
    }

    /**
     * Статический метод для быстрой отправки с fallback
     */
    public static function quickSend($config, $to, $subject, $body, $from, $fromName = '')
    {
        // Сначала пытаемся SMTP
        try {
            // Проверяем доступность SMTP сервера
            $connection = @fsockopen($config['host'], $config['port'], $errno, $errstr, 5);
            if (!$connection) {
                error_log("SmtpService: Cannot connect to {$config['host']}:{$config['port']} - $errstr ($errno)");
                throw new \Exception("SMTP server unavailable: $errstr");
            }
            fclose($connection);

            $smtp = new self(
                $config['host'],
                $config['port'],
                $config['username'],
                $config['password'],
                $config['encryption'] ?? 'tls'
            );

            if (isset($config['debug'])) {
                $smtp->setDebug($config['debug']);
            }

            $result = $smtp->sendMail($to, $subject, $body, $from, $fromName);

            if ($result) {
                error_log("SmtpService: Email sent successfully via SMTP");
                return true;
            }

        } catch (\Exception $e) {
            error_log("SmtpService: SMTP failed, trying fallback: " . $e->getMessage());
        }

        // Fallback к встроенной функции mail()
        return self::sendViaMailFunction($to, $subject, $body, $from, $fromName);
    }

    /**
     * Fallback отправка через встроенную функцию mail()
     */
    private static function sendViaMailFunction($to, $subject, $body, $from, $fromName = '')
    {
        if (!function_exists('mail')) {
            error_log("SmtpService: mail() function not available");
            return false;
        }

        // Формируем заголовки
        $headers = [];

        if ($fromName) {
            $headers[] = "From: $fromName <$from>";
        } else {
            $headers[] = "From: $from";
        }

        $headers[] = "Reply-To: $from";
        $headers[] = "Content-Type: text/plain; charset=UTF-8";
        $headers[] = "X-Mailer: PHP/" . phpversion();
        $headers[] = "Date: " . date('r');

        $headerString = implode("\r\n", $headers);

        $result = mail($to, $subject, $body, $headerString);

        if ($result) {
            error_log("SmtpService: Email sent successfully via mail() function");
        } else {
            error_log("SmtpService: Failed to send email via mail() function");
        }

        return $result;
    }
}
