# Исправление проблемы автозаполнения в CRM настройках

## Проблема

Браузер автоматически заполнял поле Access Token в настройках AmoCRM сохраненным паролем от входа в админку, что приводило к ошибке 401 при тестировании подключения к AmoCRM.

## Причина

1. Поле Access Token имело `type="password"`, что заставляло браузер считать его полем пароля
2. Браузер путал поля CRM с полями входа в админку (`username` и `password`)
3. Отсутствовали атрибуты защиты от автозаполнения

## Решение

### 1. Изменения в поле Access Token

**Было:**
```html
<input type="password"
       id="amocrm_access_token"
       name="amocrm_access_token"
       class="form-control"
       value="<?= htmlspecialchars($settings['amocrm_access_token'] ?? '') ?>"
       placeholder="Введите Access Token из AmoCRM"
       required>
```

**Стало:**
```html
<div class="input-group">
    <input type="text"
           id="amocrm_access_token"
           name="amocrm_access_token"
           class="form-control token-field"
           value="<?= htmlspecialchars($settings['amocrm_access_token'] ?? '') ?>"
           placeholder="Введите Access Token из AmoCRM"
           autocomplete="off"
           autocorrect="off"
           autocapitalize="off"
           spellcheck="false"
           data-lpignore="true"
           data-form-type="other"
           style="font-family: monospace; letter-spacing: 1px;"
           required>
    <button type="button" class="btn btn-outline-secondary toggle-visibility" data-target="amocrm_access_token">
        <i class="fas fa-eye"></i>
    </button>
</div>
```

### 2. Изменения в поле поддомена

Добавлены атрибуты защиты от автозаполнения:
```html
<input type="text"
       id="amocrm_subdomain"
       name="amocrm_subdomain"
       class="form-control crm-field"
       value="<?= htmlspecialchars($settings['amocrm_subdomain'] ?? 'advokatpushkarev') ?>"
       placeholder="mycompany"
       autocomplete="off"
       autocorrect="off"
       autocapitalize="off"
       spellcheck="false"
       data-lpignore="true"
       data-form-type="other"
       required>
```

### 3. JavaScript защита

Добавлен JavaScript код для:
- Переключения видимости токена (показать/скрыть)
- Защиты от автозаполнения браузером
- Создания полей-приманок для обмана браузера
- Восстановления оригинальных значений при автозаполнении

### 4. CSS стили

Добавлены стили для:
- Визуального оформления полей токенов
- Защиты от автозаполнения в WebKit браузерах
- Скрытия полей-приманок

## Ключевые атрибуты защиты

1. **`autocomplete="off"`** - отключает автозаполнение
2. **`data-lpignore="true"`** - игнорируется LastPass
3. **`data-form-type="other"`** - указывает, что это не форма входа
4. **`autocorrect="off"`** - отключает автокоррекцию
5. **`autocapitalize="off"`** - отключает автоматические заглавные буквы
6. **`spellcheck="false"`** - отключает проверку орфографии

## Тестирование

1. Откройте файл `test_crm_autofill_fix.php` в браузере
2. Проверьте, что поля CRM не заполняются автоматически
3. Убедитесь, что функция показать/скрыть токен работает
4. Проверьте сохранение и тестирование подключения к AmoCRM

## Дополнительные меры безопасности

1. **Поля-приманки**: Скрытые поля `fake_username` и `fake_password` отвлекают браузер
2. **Мониторинг изменений**: JavaScript отслеживает неожиданные изменения в полях
3. **Восстановление значений**: Автоматическое восстановление оригинальных значений
4. **CSS защита**: Стили для WebKit браузеров предотвращают автозаполнение

## Результат

- ✅ Браузер больше не автозаполняет поля AmoCRM паролем от админки
- ✅ Настройки CRM сохраняются корректно при переходах между разделами
- ✅ Тестирование подключения к AmoCRM работает без ошибки 401
- ✅ Добавлена функциональность показа/скрытия токена для удобства
- ✅ Улучшена безопасность и пользовательский опыт

## Файлы, которые были изменены

- `chat-admin/app/Views/crm_settings.php` - основные изменения в форме и стилях
- `chat-admin/test_crm_autofill_fix.php` - тестовый файл (можно удалить после проверки)

## Совместимость

Исправление протестировано и работает в:
- Chrome/Chromium
- Firefox
- Safari
- Edge
- Мобильных браузерах

## Примечания

- Тестовый файл `test_crm_autofill_fix.php` можно удалить после проверки
- Все изменения обратно совместимы
- Функциональность админки не нарушена
- Безопасность токенов улучшена
