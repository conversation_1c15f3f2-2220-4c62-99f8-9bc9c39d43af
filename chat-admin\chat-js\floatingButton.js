// --- START OF FILE floatingButton.js ---

// chat-js/floatingButton.js
import { HIDE_BUTTON_DELAY } from './config.js';
// findOrFail не нужен, т.к. кнопка опциональна

let chatContainer; // Контейнер, на который наводим мышь
let aiFloatingButton; // Сама кнопка "Звонок AI"
let hideButtonTimeoutId = null;

export function initFloatingButton(onButtonClick) {
    try {
        // Используем классы для выбора элементов
        chatContainer = document.querySelector(".czn-chat-container"); // Основной контейнер чата
        aiFloatingButton = document.querySelector(".czn-chat-call-ai-btn"); // Кнопка звонка

        // Если кнопка или контейнер не найдены, логика плавающей кнопки отключается
        if (!chatContainer) {
            console.warn("FloatingButton: .czn-chat-container not found. Floating button logic disabled.");
            return;
        }
        if (!aiFloatingButton) {
            console.warn("FloatingButton: .czn-chat-call-ai-btn not found. Floating button logic disabled.");
            return;
        }

        console.log("FloatingButton: Initializing...");

        // --- Функции ---
        const showButton = () => {
            clearTimeout(hideButtonTimeoutId); // Отменяем таймер скрытия
            hideButtonTimeoutId = null;
            // Убираем класс hidden и добавляем active для показа с анимацией
            aiFloatingButton.classList.remove("czn-chat-hidden"); // Prefixed class
            aiFloatingButton.classList.add("czn-chat-active");   // Prefixed class
        };

        const startHideTimer = () => {
            clearTimeout(hideButtonTimeoutId); // Отменяем предыдущий таймер, если есть
            hideButtonTimeoutId = setTimeout(() => {
                // Убираем active и добавляем hidden для скрытия с анимацией
                aiFloatingButton.classList.remove("czn-chat-active"); // Prefixed class
                aiFloatingButton.classList.add("czn-chat-hidden");    // Prefixed class
                hideButtonTimeoutId = null;
            }, HIDE_BUTTON_DELAY); // Используем задержку из конфига
        };

        // --- Обработчики ---
        // Показываем кнопку при наведении на контейнер чата
        chatContainer.addEventListener("mouseenter", showButton);
        // Запускаем таймер скрытия, когда мышь уходит с контейнера
        chatContainer.addEventListener("mouseleave", startHideTimer);

        // Отменяем таймер скрытия, если мышь наведена на саму кнопку
        aiFloatingButton.addEventListener("mouseenter", () => clearTimeout(hideButtonTimeoutId));
        // Запускаем таймер скрытия снова, когда мышь уходит с кнопки
        aiFloatingButton.addEventListener("mouseleave", startHideTimer);

        // Обработчик клика по кнопке
        aiFloatingButton.addEventListener("click", () => {
            if (typeof onButtonClick === 'function') {
                onButtonClick(); // Вызываем переданный колбэк (openVoiceCall)
            }
            // Опционально: скрыть кнопку сразу после клика
            // aiFloatingButton.classList.remove("czn-chat-active");
            // aiFloatingButton.classList.add("czn-chat-hidden");
            // clearTimeout(hideButtonTimeoutId); // Отменяем таймер
        });

        // Изначально кнопка скрыта (через CSS класс czn-chat-hidden)
        aiFloatingButton.classList.add("czn-chat-hidden"); // Убедимся, что она скрыта при старте

        console.log("FloatingButton: Event listeners attached.");

    } catch (error) {
        console.error("FloatingButton: Initialization failed:", error);
    }
}

// --- END OF FILE floatingButton.js ---