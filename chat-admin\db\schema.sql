-- Таблица пользователей
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reset_token TEXT,
    token_expires TIMESTAMP
);

-- Таблица сессий чата
CREATE TABLE IF NOT EXISTS chat_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL, -- Changed from INTEGER, removed FK
    session_uuid TEXT UNIQUE NOT NULL,
    chat_id TEXT UNIQUE, -- Добавляем chat_id
    title TEXT DEFAULT 'Новый чат',
    is_active INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    -- Removed: <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id)
);

-- Таблица сообщений
CREATE TABLE IF NOT EXISTS messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER NOT NULL,
    user_id TEXT, -- Changed from INTEGER, removed FK
    sender TEXT NOT NULL, -- 'user', 'assistant' или 'system'
    content TEXT NOT NULL, -- Очищенный текст для отображения в чате
    raw_content TEXT, -- Оригинальный текст с SSML тегами для TTS
    tokens INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Изменено с timestamp на created_at
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id)
    -- Removed: FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Таблица настроек API
CREATE TABLE IF NOT EXISTS api_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    api_key TEXT NOT NULL,
    api_model TEXT NOT NULL DEFAULT 'mistral-large-latest',
    max_tokens INTEGER DEFAULT 1000, -- Общие max_tokens (возможно, устаревшие)
    temperature REAL DEFAULT 0.7,
    system_message TEXT DEFAULT 'Ты полезный AI ассистент',
    mistral_api_url TEXT DEFAULT NULL, -- URL для Mistral API
    mistral_max_tokens INTEGER DEFAULT 2048, -- Max tokens для Mistral
    tts_voice TEXT DEFAULT 'Svetlana', -- Голос для TTS
    tts_locale TEXT DEFAULT 'ru-RU', -- Локаль для TTS
    bot_avatar TEXT DEFAULT 'chat-admin/images/darya.svg', -- Путь к аватарке бота
    custom_api_supports_stream INTEGER DEFAULT 0, -- 1 если кастомный API поддерживает стриминг, 0 если нет
    is_active INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Таблица загруженных файлов
CREATE TABLE IF NOT EXISTS uploaded_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL, -- Changed from INTEGER, removed FK
    session_id INTEGER,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    token_count INTEGER DEFAULT 0,
    processed_content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Removed: FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id)
);

-- Таблица контекстных файлов
CREATE TABLE IF NOT EXISTS context_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER NOT NULL,
    file_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id),
    FOREIGN KEY (file_id) REFERENCES uploaded_files(id)
);

-- Таблица векторных баз данных
CREATE TABLE IF NOT EXISTS vector_stores (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    path TEXT NOT NULL,
    dimensions INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Индексы для ускорения запросов
CREATE INDEX IF NOT EXISTS idx_messages_session ON messages(session_id);
-- Removed: CREATE INDEX IF NOT EXISTS idx_messages_user ON messages(user_id);
-- Removed: CREATE INDEX IF NOT EXISTS idx_chat_sessions_user ON chat_sessions(user_id);
-- Removed: CREATE INDEX IF NOT EXISTS idx_files_user ON uploaded_files(user_id);
-- Consider adding indexes on new TEXT user_id columns if needed for performance

-- Триггеры для обновления временных меток
CREATE TRIGGER IF NOT EXISTS update_chat_session_timestamp
AFTER UPDATE ON chat_sessions
BEGIN
    UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_api_settings_timestamp
AFTER UPDATE ON api_settings
BEGIN
    UPDATE api_settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Таблица для логирования ошибок API
CREATE TABLE IF NOT EXISTS api_errors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    session_id INTEGER,
    error_code INTEGER NOT NULL,
    error_message TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Removed: FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id)
);

-- Добавление столбцов для настройки цветов чата
ALTER TABLE api_settings ADD COLUMN chat_primary_color TEXT DEFAULT '#4361ee';
ALTER TABLE api_settings ADD COLUMN chat_primary_dark_color TEXT DEFAULT '#3a56d4';
ALTER TABLE api_settings ADD COLUMN chat_secondary_color TEXT DEFAULT '#3f37c9';
ALTER TABLE api_settings ADD COLUMN chat_text_color TEXT DEFAULT '#79818f';
ALTER TABLE api_settings ADD COLUMN chat_text_light_color TEXT DEFAULT '#8d99ae';
ALTER TABLE api_settings ADD COLUMN chat_bg_color TEXT DEFAULT '#f8f9fa';
ALTER TABLE api_settings ADD COLUMN chat_textarea_edited_color TEXT DEFAULT '#f8f9fa';
ALTER TABLE api_settings ADD COLUMN chat_card_color TEXT DEFAULT '#ffffff';
ALTER TABLE api_settings ADD COLUMN chat_error_color TEXT DEFAULT '#ef233c';
ALTER TABLE api_settings ADD COLUMN chat_error_gentle_color TEXT DEFAULT '#ff7f50';
ALTER TABLE api_settings ADD COLUMN chat_error_gentle_dark_color TEXT DEFAULT '#e57373';
ALTER TABLE api_settings ADD COLUMN chat_success_color TEXT DEFAULT '#4cc9f0';
ALTER TABLE api_settings ADD COLUMN chat_border_color TEXT DEFAULT 'rgba(0, 0, 0, 0.1)';
ALTER TABLE api_settings ADD COLUMN chat_shadow_color TEXT DEFAULT '0 4px 20px rgba(0, 0, 0, 0.08)';
ALTER TABLE api_settings ADD COLUMN chat_shadow_soft_color TEXT DEFAULT '0 8px 30px rgba(0, 0, 0, 0.1)';
ALTER TABLE api_settings ADD COLUMN chat_error_gentle_gradient_start TEXT DEFAULT '#f08080';
ALTER TABLE api_settings ADD COLUMN chat_error_gentle_gradient_end TEXT DEFAULT '#e57373';
ALTER TABLE api_settings ADD COLUMN chat_stop_color TEXT DEFAULT '#f72585';

-- Таблица для хранения текстов интерфейса
CREATE TABLE IF NOT EXISTS texts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE NOT NULL,
    value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Триггер для обновления updated_at при изменении текста
CREATE TRIGGER IF NOT EXISTS update_text_timestamp
AFTER UPDATE ON texts
BEGIN
    UPDATE texts SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Таблица настроек интеграции с CRM
CREATE TABLE IF NOT EXISTS crm_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    amocrm_portal_url TEXT DEFAULT 'https://advokatpushkarev.amocrm.ru',
    amocrm_client_id TEXT,
    amocrm_client_secret TEXT,
    amocrm_redirect_uri TEXT,
    amocrm_access_token TEXT,
    amocrm_refresh_token TEXT,
    amocrm_token_expires_at INTEGER,
    amocrm_client_field_id INTEGER DEFAULT 631683,
    yandex_metrika_counter_id INTEGER DEFAULT 100128474,
    yandex_metrika_goal_name TEXT DEFAULT 'chat_contact_got',
    is_active INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Триггер для обновления updated_at при изменении настроек CRM
CREATE TRIGGER IF NOT EXISTS update_crm_settings_timestamp
AFTER UPDATE ON crm_settings
BEGIN
    UPDATE crm_settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
