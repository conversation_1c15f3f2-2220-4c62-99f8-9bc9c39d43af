/**
 * textManager.js
 * Модуль для управления текстовыми строками интерфейса.
 * Загружает тексты с бэкенда или из локального файла и предоставляет функцию для их получения.
 */
import { getApiBaseUrl } from './config.js'; // Импортируем функцию получения базового URL API
import { texts as localTexts } from './texts_ru.js'; // Импортируем локальные тексты

const textManager = (() => {
    // Переменная для хранения URL API
    let TEXTS_API_URL = null;

    let texts = {}; // Хранилище для загруженных текстов { key: value }
    let isInitialized = false;
    let initializationPromise = null;

    /**
     * Загружает тексты с сервера или использует локальные тексты.
     * @returns {Promise<void>}
     */
    async function loadTexts() {
        if (isInitialized) {
            console.log("TextManager: Texts already loaded.");
            return;
        }

        // Сначала используем локальные тексты
        texts = { ...localTexts };

        try {
            // Получаем URL API только при необходимости
            const apiBaseUrl = getApiBaseUrl();
            if (apiBaseUrl) {
                // Формируем URL для получения текстов
                TEXTS_API_URL = `${apiBaseUrl}&action=getTextsApi&lang=ru`;

                // Затем пытаемся загрузить с сервера
                console.log("TextManager: Loading texts from", TEXTS_API_URL);

                const response = await fetch(TEXTS_API_URL);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const serverTexts = await response.json();

                // Объединяем локальные и серверные тексты (серверные имеют приоритет)
                texts = { ...texts, ...serverTexts };
                console.log("TextManager: Texts loaded successfully from server.");
            } else {
                console.warn("TextManager: API Base URL not available, using only local texts.");
            }
        } catch (error) {
            console.warn("TextManager: Failed to load texts from server, using local texts:", error);
            // В случае ошибки используем только локальные тексты, которые уже загружены
        }

        isInitialized = true;
        console.log("TextManager: Final texts:", texts);
    }

    /**
     * Инициализирует менеджер текстов (загружает тексты).
     * Возвращает промис, который разрешается после загрузки текстов.
     * @returns {Promise<void>}
     */
    function initialize() {
        if (!initializationPromise) {
            initializationPromise = loadTexts();
        }
        return initializationPromise;
    }

    /**
     * Получает текстовую строку по ключу.
     * Если текст не найден, возвращает сам ключ.
     * Поддерживает замену плейсхолдеров вида ${param}.
     *
     * @param {string} key Ключ текста (например, 'chat.title').
     * @param {Object.<string, string|number>} [params={}] Объект с параметрами для замены (например, { sessionId: 123 }).
     * @returns {string} Текстовая строка или ключ, если текст не найден.
     */
    function getText(key, params = {}) {
        if (!isInitialized) {
            console.warn(`TextManager: getText called before initialization for key "${key}". Returning key.`);
            // В идеале, нужно дождаться initialize(), но для простоты вернем ключ
            // или можно возвращать строку загрузки 'Loading...'
            return `[${key}]`; // Возвращаем ключ в скобках для наглядности
        }

        let textValue = texts[key];

        if (textValue === undefined || textValue === null) {
            console.warn(`TextManager: Text key not found: "${key}". Returning key.`);
            return `[${key}]`; // Возвращаем ключ в скобках
        }

        // Замена плейсхолдеров
        if (typeof textValue === 'string' && Object.keys(params).length > 0) {
            textValue = textValue.replace(/\${(.*?)}/g, (match, paramName) => {
                return params[paramName] !== undefined ? String(params[paramName]) : match;
            });
        }

        return String(textValue); // Убедимся, что возвращаем строку
    }

    return {
        initialize,
        getText,
        // Дополнительно можно предоставить доступ ко всем текстам, если нужно
        // getAllTexts: () => texts,
        isReady: () => isInitialized
    };
})();

export default textManager;
