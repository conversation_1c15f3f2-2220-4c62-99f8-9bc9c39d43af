<?php

namespace App\Services;

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

/**
 * Улучшенный PHPMailer сервис с поддержкой DKIM и лучшей доставляемости в Gmail
 */
class EnhancedPHPMailerService
{
    private $config;

    public function __construct($config = [])
    {
        $this->config = array_merge([
            'host' => 'smtp.yandex.ru',
            'port' => 587,
            'username' => '',
            'password' => '',
            'encryption' => 'tls',
            'debug' => false,
            'timeout' => 30,
            'charset' => 'UTF-8',
            'enable_dkim' => false,
            'dkim_domain' => '',
            'dkim_private_key' => '',
            'dkim_selector' => 'default',
            'dkim_passphrase' => ''
        ], $config);
    }

    /**
     * Отправка email с улучшенной аутентификацией для Gmail
     */
    public function sendEmail($to, $subject, $body, $from, $fromName = '', $isHtml = false)
    {
        // Сначала пытаемся через улучшенный SMTP
        if (!empty($this->config['host']) && !empty($this->config['username']) && !empty($this->config['password'])) {
            try {
                $result = $this->sendViaEnhancedSMTP($to, $subject, $body, $from, $fromName, $isHtml);
                if ($result) {
                    $this->log("Email sent successfully via Enhanced SMTP to: $to");
                    return true;
                }
            } catch (\Exception $e) {
                $this->log("Enhanced SMTP failed: " . $e->getMessage());
            }
        }

        // Fallback к обычной отправке
        $this->log("Using fallback email method");
        return $this->sendViaFallback($to, $subject, $body, $from, $fromName, $isHtml);
    }

    /**
     * Отправка через улучшенный SMTP с оптимизацией для Gmail
     */
    private function sendViaEnhancedSMTP($to, $subject, $body, $from, $fromName = '', $isHtml = false)
    {
        $mail = new PHPMailer(true);

        try {
            // Настройки SMTP
            $mail->isSMTP();
            $mail->Host = $this->config['host'];
            $mail->SMTPAuth = true;
            $mail->Username = $this->config['username'];
            $mail->Password = $this->config['password'];
            $mail->Port = $this->config['port'];

            // Отключаем проверку SSL сертификатов для обхода проблем с прокси
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            );

            // Настройка шифрования
            if ($this->config['encryption'] === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            } elseif ($this->config['encryption'] === 'tls') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            }

            // Настройки отладки
            if ($this->config['debug']) {
                $mail->SMTPDebug = SMTP::DEBUG_SERVER;
                $mail->Debugoutput = function($str, $level) {
                    $this->log("SMTP Debug: $str");
                };
            }

            // Настройки таймаута и соединения
            $mail->Timeout = $this->config['timeout'];
            $mail->SMTPKeepAlive = false;

            // Настройки безопасности для локальных серверов
            $mail->SMTPOptions = [
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                ]
            ];

            // DKIM настройки (если включены)
            if ($this->config['enable_dkim'] && !empty($this->config['dkim_domain']) && !empty($this->config['dkim_private_key'])) {
                $this->setupDKIM($mail);
            }

            // Отправитель
            $mail->setFrom($from, $fromName);
            $mail->addReplyTo($from, $fromName);

            // Получатель
            $mail->addAddress($to);

            // Контент
            $mail->isHTML($isHtml);
            $mail->CharSet = $this->config['charset'];
            $mail->Subject = $subject;
            
            if ($isHtml) {
                $mail->Body = $body;
                // Создаем текстовую версию для лучшей доставляемости
                $mail->AltBody = $this->htmlToText($body);
            } else {
                $mail->Body = $body;
            }

            // Дополнительные заголовки для лучшей доставляемости в Gmail
            $this->addGmailOptimizedHeaders($mail);

            // Отправляем
            $result = $mail->send();
            
            if ($result) {
                $this->log("Enhanced SMTP: Email sent successfully to $to");
                return true;
            } else {
                $this->log("Enhanced SMTP: Failed to send email to $to");
                return false;
            }

        } catch (Exception $e) {
            $this->log("Enhanced SMTP Exception: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Настройка DKIM подписи
     */
    private function setupDKIM($mail)
    {
        try {
            $mail->DKIM_domain = $this->config['dkim_domain'];
            $mail->DKIM_private = $this->config['dkim_private_key'];
            $mail->DKIM_selector = $this->config['dkim_selector'];
            $mail->DKIM_passphrase = $this->config['dkim_passphrase'];
            $mail->DKIM_identity = $mail->From;
            
            $this->log("DKIM configured for domain: " . $this->config['dkim_domain']);
        } catch (\Exception $e) {
            $this->log("DKIM setup failed: " . $e->getMessage());
        }
    }

    /**
     * Добавление заголовков, оптимизированных для Gmail
     */
    private function addGmailOptimizedHeaders($mail)
    {
        // Основные заголовки для лучшей доставляемости
        $mail->addCustomHeader('X-Mailer', 'EnhancedPHPMailer-' . PHPMailer::VERSION);
        $mail->addCustomHeader('X-Priority', '3');
        $mail->addCustomHeader('X-MSMail-Priority', 'Normal');
        $mail->addCustomHeader('Importance', 'Normal');
        
        // Заголовки для предотвращения спама
        $mail->addCustomHeader('X-Auto-Response-Suppress', 'All');
        $mail->addCustomHeader('Auto-Submitted', 'auto-generated');
        
        // Заголовки для лучшей категоризации в Gmail
        $mail->addCustomHeader('X-Google-Original-From', $mail->From);
        
        // List-Unsubscribe для соответствия требованиям Gmail
        $mail->addCustomHeader('List-Unsubscribe', '<mailto:' . $this->config['username'] . '?subject=unsubscribe>');
        
        // Дополнительные заголовки безопасности
        $mail->addCustomHeader('X-Spam-Status', 'No');
        $mail->addCustomHeader('X-Spam-Score', '0.0');
    }

    /**
     * Конвертация HTML в текст для AltBody
     */
    private function htmlToText($html)
    {
        // Простая конвертация HTML в текст
        $text = strip_tags($html);
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        return $text;
    }

    /**
     * Fallback отправка
     */
    private function sendViaFallback($to, $subject, $body, $from, $fromName = '', $isHtml = false)
    {
        if (!function_exists('mail')) {
            $this->log("mail() function not available");
            return $this->saveToFile($to, $subject, $body, $from, $fromName);
        }

        // Настраиваем mail() для работы с внешним SMTP если возможно
        if (!empty($this->config['host']) && !empty($this->config['username'])) {
            ini_set('SMTP', $this->config['host']);
            ini_set('smtp_port', $this->config['port']);
            ini_set('sendmail_from', $this->config['username']);

            // Для Windows также устанавливаем аутентификацию
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                ini_set('auth_username', $this->config['username']);
                ini_set('auth_password', $this->config['password']);
            }

            $this->log("Configured mail() settings: " . $this->config['host'] . ":" . $this->config['port']);
        }

        // Формируем заголовки для mail()
        $headers = [];
        
        if ($fromName) {
            $headers[] = "From: =?UTF-8?B?" . base64_encode($fromName) . "?= <$from>";
        } else {
            $headers[] = "From: $from";
        }
        
        $headers[] = "Reply-To: $from";
        $headers[] = "Return-Path: $from";
        $headers[] = "MIME-Version: 1.0";
        
        if ($isHtml) {
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        } else {
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
        }
        
        $headers[] = "Content-Transfer-Encoding: 8bit";
        $headers[] = "X-Mailer: EnhancedPHPMailerService/1.0 (fallback)";
        $headers[] = "Date: " . date('r');
        $headers[] = "Message-ID: <" . uniqid() . "@" . ($this->config['host'] ?: 'localhost') . ">";
        
        // Добавляем заголовки для Gmail
        $headers[] = "X-Priority: 3";
        $headers[] = "Importance: Normal";
        $headers[] = "List-Unsubscribe: <mailto:" . $this->config['username'] . "?subject=unsubscribe>";
        
        $headerString = implode("\r\n", $headers);
        
        $result = mail($to, $subject, $body, $headerString);
        
        if ($result) {
            $this->log("Email sent successfully via mail() to: $to");
        } else {
            $this->log("Failed to send email via mail() to: $to");

            // Получаем последнюю ошибку
            $lastError = error_get_last();
            if ($lastError) {
                $this->log("Mail() error: " . $lastError['message']);
            }

            // Сохраняем в файл как backup
            $this->saveToFile($to, $subject, $body, $from, $fromName);
        }
        
        return $result;
    }

    /**
     * Сохранение email в файл как backup
     */
    private function saveToFile($to, $subject, $body, $from, $fromName = '')
    {
        try {
            $notificationsDir = dirname(__DIR__, 2) . '/notifications/';
            
            if (!is_dir($notificationsDir)) {
                mkdir($notificationsDir, 0755, true);
            }
            
            $filename = 'email_backup_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.txt';
            $filepath = $notificationsDir . $filename;
            
            $fileContent = "=== EMAIL BACKUP ===\n";
            $fileContent .= "Время: " . date('Y-m-d H:i:s') . "\n";
            $fileContent .= "От: " . ($fromName ? "$fromName <$from>" : $from) . "\n";
            $fileContent .= "Кому: $to\n";
            $fileContent .= "Тема: $subject\n";
            $fileContent .= "Статус: Email не отправлен, сохранен в файл\n";
            $fileContent .= "Сервис: EnhancedPHPMailerService\n\n";
            $fileContent .= "Содержимое:\n";
            $fileContent .= $body . "\n\n";
            $fileContent .= "=== КОНЕЦ BACKUP ===\n";
            
            if (file_put_contents($filepath, $fileContent)) {
                $this->log("Email saved to backup file: $filename");
                return true;
            } else {
                $this->log("Failed to save email backup file");
                return false;
            }
            
        } catch (\Exception $e) {
            $this->log("Error saving email backup: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Статический метод для быстрой отправки
     */
    public static function quickSend($config, $to, $subject, $body, $from, $fromName = '', $isHtml = false)
    {
        $mailer = new self($config);
        return $mailer->sendEmail($to, $subject, $body, $from, $fromName, $isHtml);
    }

    /**
     * Проверка доступности SMTP сервера
     */
    public static function testConnection($host, $port, $timeout = 5)
    {
        $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
        if ($connection) {
            fclose($connection);
            return true;
        }
        return false;
    }

    /**
     * Логирование
     */
    private function log($message)
    {
        if ($this->config['debug']) {
            error_log("EnhancedPHPMailerService: " . $message);
        }
    }

    /**
     * Валидация email адреса
     */
    public static function validateEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Очистка email адреса
     */
    public static function sanitizeEmail($email)
    {
        return filter_var(trim($email), FILTER_SANITIZE_EMAIL);
    }
}
