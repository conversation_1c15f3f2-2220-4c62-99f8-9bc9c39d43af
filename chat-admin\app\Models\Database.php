<?php

namespace App\Models;

// Логика инициализации и получения соединения теперь внутри этого класса.
class Database {
    private static $db = null;
    // Пути к файлам БД и схемы (относительно папки chat-admin)
    private const DB_FILE_PATH = '/db/chat.db';
    private const DB_SCHEMA_PATH = '/db/schema.sql';
    /**
     * Возвращает статическое соединение с базой данных SQLite3.
     * Инициализирует БД при первом вызове, используя функцию из db.php.
     *
     * @return \SQLite3 Объект подключения к БД.
     * @throws \Exception В случае ошибки инициализации БД.
     */
    public static function getInstance(): \SQLite3 {
        if (self::$db === null) {
            try {
                self::$db = self::initializeAndConnect();
            } catch (\Exception $e) {
                error_log("Critical error getting DB connection in Database model: " . $e->getMessage());
                // Пробрасываем исключение дальше, чтобы приложение могло его обработать
                throw new \Exception('Failed to connect to the database.', 500, $e);
            }
        }
        return self::$db;
    }

    /**
     * Инициализирует и подключается к базе данных SQLite.
     * Создает БД и применяет схему, если файл БД не существует.
     * Логика перенесена из db.php/initDB().
     *
     * @return \SQLite3 Объект подключения к БД.
     * @throws \Exception В случае ошибки.
     */
    private static function initializeAndConnect(): \SQLite3 {
        $baseDir = dirname(__DIR__, 2);
        $dbPath = $baseDir . self::DB_FILE_PATH;
        $schemaPath = $baseDir . self::DB_SCHEMA_PATH;
        $dbDir = dirname($dbPath);
        $logPrefix = "Database::initializeAndConnect - ";

        // Log paths for debugging
        error_log($logPrefix . "BaseDir: $baseDir, DbPath: $dbPath, SchemaPath: $schemaPath");

        // Check/Create DB directory
        if (!is_dir($dbDir)) {
            error_log($logPrefix . "Database directory '$dbDir' does not exist. Attempting to create...");
            if (!mkdir($dbDir, 0775, true) && !is_dir($dbDir)) {
                 $errorMsg = "Failed to create database directory: " . $dbDir;
                 error_log($logPrefix . $errorMsg);
                 throw new \Exception($errorMsg);
            }
             error_log($logPrefix . "Database directory '$dbDir' created successfully.");
        } elseif (!is_writable($dbDir)) {
             // Log non-writable directory, might be an issue later
             error_log($logPrefix . "WARNING: Database directory '$dbDir' is not writable.");
        }

        $isNewDB = !file_exists($dbPath);
        $db = null; // Initialize db variable

        try {
            error_log($logPrefix . "Attempting to connect to database: $dbPath");
            // Check writability of the file if it exists
            if (!$isNewDB && file_exists($dbPath) && !is_writable($dbPath)) {
                 error_log($logPrefix . "WARNING: Database file '$dbPath' exists but is not writable.");
                 // Consider if this should be a fatal error depending on application needs
                 // throw new \Exception("Database file is not writable: $dbPath");
            } elseif ($isNewDB && !is_writable($dbDir)) {
                 // If DB is new, the directory MUST be writable
                 throw new \Exception("Database directory is not writable, cannot create new database file: $dbDir");
            }

            // Attempt connection
            $db = new \SQLite3($dbPath);
            $db->enableExceptions(true);
            error_log($logPrefix . "Database connection successful: $dbPath");

            // Initialize if it's a new database
            if ($isNewDB) {
                error_log($logPrefix . "Database file not found at '$dbPath'. Initializing new database...");
                if (!file_exists($schemaPath)) {
                    throw new \Exception("Database schema file not found: " . $schemaPath);
                }
                 error_log($logPrefix . "Reading schema file: $schemaPath");
                $schemaSql = file_get_contents($schemaPath);
                if ($schemaSql === false) {
                    throw new \Exception("Failed to read database schema file: " . $schemaPath);
                }

                error_log($logPrefix . "Applying schema and seeding data in transaction...");
                $db->exec('BEGIN;');
                try {
                    error_log($logPrefix . "Executing schema...");
                    if (!$db->exec($schemaSql)) {
                         // Exception will be thrown due to enableExceptions(true)
                         // This catch block might catch it, or the outer one will.
                         throw new \Exception("Error executing database schema: " . $db->lastErrorMsg());
                    }
                    error_log($logPrefix . "Database schema applied successfully.");

                    error_log($logPrefix . "Seeding initial data...");
                    self::seedInitialData($db); // seedInitialData should throw on error
                    error_log($logPrefix . "Initial data seeded successfully.");

                    $db->exec('COMMIT;');
                    error_log($logPrefix . "Database initialization transaction committed for: " . $dbPath);

                } catch (\Exception $initError) {
                    $db->exec('ROLLBACK;');
                    error_log($logPrefix . "Database initialization failed during transaction: " . $initError->getMessage());
                    // Attempt to remove the partially created DB file
                    if (file_exists($dbPath)) {
                        @unlink($dbPath);
                        error_log($logPrefix . "Attempted to remove partially created DB file: $dbPath");
                    }
                    throw $initError; // Re-throw the initialization error
                }
            } else {
                 error_log($logPrefix . "Using existing database: " . $dbPath);
                 // --- Добавляем проверку и миграцию схемы для существующих БД ---
                 self::checkAndMigrateSchema($db);
                 // Optional: Add check for table existence or schema version if needed
                 // $tablesExist = $db->querySingle("SELECT name FROM sqlite_master WHERE type='table' AND name='chat_sessions'");
                 // if (!$tablesExist) { throw new \Exception("Database file exists but required tables are missing."); }
            }

            return $db;

        } catch (\Exception $e) {
            // Catch connection errors or other exceptions during the process
            $errorMsg = "Failed to initialize or open database '$dbPath': " . $e->getMessage();
            error_log($logPrefix . $errorMsg);
            // Ensure $db is closed if partially opened before error
            if ($db instanceof \SQLite3) {
                try { $db->close(); } catch (\Exception $closeErr) { /* Ignore close error */ }
            }
            // Re-throw a generic error to avoid exposing too many details potentially
            throw new \Exception('Error initializing or opening the database. Check logs for details.', 500, $e);
        }
    }

     /**
      * Заполняет базу данных начальными данными (пользователь, настройки).
      * Логика перенесена из db.php/initDB().
      *
      * @param \SQLite3 $db Объект подключения к БД.
      * @throws \Exception В случае ошибки.
      */
     private static function seedInitialData(\SQLite3 $db): void {
         // 1. Пользователь admin
         try {
             $adminPasswordHash = password_hash('admin', PASSWORD_DEFAULT); // ЗАМЕНИТЕ ПАРОЛЬ!
             $stmtUser = $db->prepare("INSERT INTO users (username, password, email) VALUES (:user, :pass, :email)");
             $stmtUser->bindValue(':user', 'admin', SQLITE3_TEXT);
             $stmtUser->bindValue(':pass', $adminPasswordHash, SQLITE3_TEXT);
             $stmtUser->bindValue(':email', '<EMAIL>', SQLITE3_TEXT); // Уникальный email!
             $stmtUser->execute();
             error_log("Default admin user created.");
         } catch (\Exception $e) {
             if ($db->lastErrorCode() !== 19) { // 19 = SQLITE_CONSTRAINT_UNIQUE
                 throw new \Exception("Error creating admin user: " . $e->getMessage());
             }
             error_log("Admin user likely already exists.");
         }

         // 2. Настройки API
         $apiKey = getenv('MISTRAL_API_KEY') ?: ''; // Читаем из окружения
         if (empty($apiKey)) {
             error_log("WARNING: MISTRAL_API_KEY environment variable is not set!");
             // Можно установить невалидный ключ по умолчанию или оставить пустым
             // $apiKey = 'INVALID_KEY_PLEASE_SET_IN_ENV';
         }

         try {
             $stmtApi = $db->prepare("INSERT INTO api_settings (api_key, api_model, system_message, is_active, temperature, max_tokens) VALUES (:key, :model, :sys, :active, :temp, :tokens)");
             $stmtApi->bindValue(':key', $apiKey, SQLITE3_TEXT);
             $stmtApi->bindValue(':model', 'mistral-large-latest', SQLITE3_TEXT);
             $stmtApi->bindValue(':sys', 'Ты полезный AI ассистент. Отвечай на вопросы подробно и вежливо.', SQLITE3_TEXT);
             $stmtApi->bindValue(':active', 0, SQLITE3_INTEGER); // По умолчанию неактивно
             $stmtApi->bindValue(':temp', 0.7, SQLITE3_FLOAT);
             $stmtApi->bindValue(':tokens', 1000, SQLITE3_INTEGER);
             $stmtApi->execute();
             error_log("Default API settings created (inactive by default).");
         } catch (\Exception $e) {
              if ($db->lastErrorCode() !== 19) { // Проверяем на UNIQUE constraint, если он есть
                  throw new \Exception("Error creating API settings: " . $e->getMessage());
              }
              error_log("API settings likely already exist.");
         }
     }

    // Приватный конструктор, чтобы предотвратить создание экземпляров
    private function __construct() {}

    // Приватный метод клонирования, чтобы предотвратить клонирование
    private function __clone() {}

    // Приватный метод unserialize, чтобы предотвратить unserialize
    public function __wakeup() {}

    /**
     * Сбрасывает соединение с базой данных.
     * Это заставит следующий вызов getInstance() создать новое соединение
     * и выполнить проверку/миграцию схемы.
     */
    public static function resetConnection(): void {
        if (self::$db instanceof \SQLite3) {
            try {
                self::$db->close();
            } catch (\Exception $e) {
                error_log("Error closing database connection: " . $e->getMessage());
            }
        }
        self::$db = null;
    }

    /**
     * Проверяет схему таблицы api_settings и добавляет недостающие колонки.
     * Также инициализирует таблицу текстов, если она еще не существует.
     *
     * @param \SQLite3 $db Объект подключения к БД.
     */
    private static function checkAndMigrateSchema(\SQLite3 $db): void {
        $logPrefix = "Database::checkAndMigrateSchema - ";

        // Инициализация таблицы текстов
        self::initializeTextsTable($db);

        $tableName = 'api_settings';
        $columnsToAdd = [
            'mistral_api_url' => 'TEXT DEFAULT NULL',
            'mistral_max_tokens' => 'INTEGER DEFAULT 2048',
            'tts_voice' => 'TEXT DEFAULT \'Svetlana\'', // Добавляем tts_voice
            'tts_locale' => 'TEXT DEFAULT \'ru-RU\'',   // Добавляем tts_locale
            'bot_avatar' => 'TEXT DEFAULT \'chat-admin/images/darya.svg\'', // <-- Добавлено
            'custom_api_supports_stream' => 'INTEGER DEFAULT 0', // Добавляем поддержку стриминга для кастомного API
            // Добавление столбцов для настройки цветов чата
            'chat_primary_color' => 'TEXT DEFAULT \'#4361ee\'',
            'chat_primary_dark_color' => 'TEXT DEFAULT \'#3a56d4\'',
            'chat_secondary_color' => 'TEXT DEFAULT \'#3f37c9\'',
            'chat_text_color' => 'TEXT DEFAULT \'#79818f\'',
            'chat_text_light_color' => 'TEXT DEFAULT \'#8d99ae\'',
            'chat_bg_color' => 'TEXT DEFAULT \'#f8f9fa\'',
            'chat_textarea_edited_color' => 'TEXT DEFAULT \'#f8f9fa\'',
            // Настройки превью-сообщений
            'preview_messages_enabled' => 'INTEGER DEFAULT 1', // Включить/выключить превью
            'preview_message_delay' => 'INTEGER DEFAULT 3000', // Задержка между сообщениями в мс
            'preview_hide_greeting' => 'INTEGER DEFAULT 1', // Скрывать стандартное приветствие если превью включено
            'auto_open_chat' => 'INTEGER DEFAULT 1', // Автоматически открывать чат после загрузки страницы
            'auto_open_delay' => 'INTEGER DEFAULT 0', // Задержка перед автоматическим открытием чата в мс (0 = сразу)
            'notification_email' => 'TEXT DEFAULT \'\'', // Email для уведомлений о телефонах
            'email_from' => 'TEXT DEFAULT \'<EMAIL>\'', // Email отправителя
            'email_subject' => 'TEXT DEFAULT \'Новый телефон из чата: {phone}\'', // Тема письма
            'email_body' => 'TEXT DEFAULT \'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}\'', // Текст письма
            // SMTP настройки
            'smtp_enabled' => 'INTEGER DEFAULT 0', // Использовать SMTP вместо mail()
            'smtp_host' => 'TEXT DEFAULT \'\'', // SMTP сервер
            'smtp_port' => 'INTEGER DEFAULT 587', // SMTP порт
            'smtp_username' => 'TEXT DEFAULT \'\'', // SMTP логин
            'smtp_password' => 'TEXT DEFAULT \'\'', // SMTP пароль
            'smtp_encryption' => 'TEXT DEFAULT \'tls\'', // Тип шифрования: tls, ssl, none
            'chat_card_color' => 'TEXT DEFAULT \'#ffffff\'',
            'chat_error_color' => 'TEXT DEFAULT \'#ef233c\'',
            'chat_error_gentle_color' => 'TEXT DEFAULT \'#ff7f50\'',
            'chat_error_gentle_dark_color' => 'TEXT DEFAULT \'#e57373\'',
            'chat_success_color' => 'TEXT DEFAULT \'#4cc9f0\'',
            'chat_border_color' => 'TEXT DEFAULT \'rgba(0, 0, 0, 0.1)\'',
            'chat_shadow_color' => 'TEXT DEFAULT \'0 4px 20px rgba(0, 0, 0, 0.08)\'',
            'chat_shadow_soft_color' => 'TEXT DEFAULT \'0 8px 30px rgba(0, 0, 0, 0.1)\'',
            // Добавление столбцов для градиентных цветов кнопок
            'chat_error_gentle_gradient_start' => 'TEXT DEFAULT \'#f08080\'',
            'chat_error_gentle_gradient_end' => 'TEXT DEFAULT \'#e57373\'',
        ];

        try {
            // Получаем информацию о колонках таблицы
            $result = $db->query("PRAGMA table_info($tableName)");
            $existingColumns = [];
            while ($row = $result->fetchArray(\SQLITE3_ASSOC)) {
                $existingColumns[$row['name']] = true;
            }

            // Проверяем и добавляем каждую недостающую колонку
            foreach ($columnsToAdd as $columnName => $columnDefinition) {
                if (!isset($existingColumns[$columnName])) {
                    error_log($logPrefix . "Column '$columnName' not found in table '$tableName'. Adding column...");
                    $sql = "ALTER TABLE $tableName ADD COLUMN $columnName $columnDefinition";
                    if ($db->exec($sql)) {
                        error_log($logPrefix . "Successfully added column '$columnName' to table '$tableName'.");
                    } else {
                        // enableExceptions(true) должен был выбросить исключение, но на всякий случай
                        throw new \Exception("Failed to add column '$columnName' to table '$tableName': " . $db->lastErrorMsg());
                    }
                } else {
                     error_log($logPrefix . "Column '$columnName' already exists in table '$tableName'.");
                }
            }
        } catch (\Exception $e) {
            // Логируем ошибку, но не прерываем работу приложения,
            // так как основная функциональность может работать и без новых колонок.
            // Однако, сохранение новых настроек будет падать.
            error_log($logPrefix . "Error during schema migration for table '$tableName': " . $e->getMessage());
            // Можно пробросить исключение, если миграция критична:
            // throw new \Exception("Schema migration failed for table '$tableName'.", 500, $e);
        }

        // Создаем таблицу настроек CRM, если она не существует
        self::createCrmSettingsTable($db);
    }

    /**
     * Создает таблицу настроек CRM
     *
     * @param \SQLite3 $db Объект подключения к БД.
     */
    private static function createCrmSettingsTable(\SQLite3 $db): void {
        $logPrefix = "Database::createCrmSettingsTable - ";

        try {
            $sql = "
                CREATE TABLE IF NOT EXISTS crm_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    amocrm_portal_url TEXT DEFAULT 'https://advokatpushkarev.amocrm.ru',
                    amocrm_login TEXT DEFAULT '<EMAIL>',
                    amocrm_password TEXT DEFAULT 'PoV8mdix',
                    amocrm_client_id TEXT DEFAULT NULL,
                    amocrm_client_secret TEXT DEFAULT NULL,
                    amocrm_access_token TEXT DEFAULT NULL,
                    amocrm_refresh_token TEXT DEFAULT NULL,
                    amocrm_token_expires_at INTEGER DEFAULT NULL,
                    amocrm_client_id_field_id INTEGER DEFAULT 631683,
                    yandex_metrika_counter_id INTEGER DEFAULT 100128474,
                    yandex_metrika_goal_name TEXT DEFAULT 'chat_contact_got',
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ";

            $db->exec($sql);

            // Добавляем новые колонки для OAuth, если они не существуют
            self::addOAuthColumnsIfNotExist($db);

            // Создаем триггер для обновления updated_at
            $triggerSql = "
                CREATE TRIGGER IF NOT EXISTS update_crm_settings_timestamp
                AFTER UPDATE ON crm_settings
                BEGIN
                    UPDATE crm_settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
                END
            ";

            $db->exec($triggerSql);

            error_log($logPrefix . "crm_settings table created with trigger.");
        } catch (\Exception $e) {
            error_log($logPrefix . "Error creating crm_settings table: " . $e->getMessage());
        }
    }

    /**
     * Добавляет OAuth колонки в таблицу crm_settings, если они не существуют
     *
     * @param \SQLite3 $db Объект подключения к БД.
     */
    private static function addOAuthColumnsIfNotExist(\SQLite3 $db): void {
        $logPrefix = "Database::addOAuthColumnsIfNotExist - ";

        try {
            // Проверяем существующие колонки
            $result = $db->query("PRAGMA table_info(crm_settings)");
            $existingColumns = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                $existingColumns[] = $row['name'];
            }

            // Список новых OAuth колонок
            $oauthColumns = [
                'amocrm_client_id' => 'TEXT DEFAULT NULL',
                'amocrm_client_secret' => 'TEXT DEFAULT NULL',
                'amocrm_access_token' => 'TEXT DEFAULT NULL',
                'amocrm_refresh_token' => 'TEXT DEFAULT NULL',
                'amocrm_token_expires_at' => 'INTEGER DEFAULT NULL'
            ];

            // Добавляем отсутствующие колонки
            foreach ($oauthColumns as $columnName => $columnDefinition) {
                if (!in_array($columnName, $existingColumns)) {
                    $alterSql = "ALTER TABLE crm_settings ADD COLUMN $columnName $columnDefinition";
                    $db->exec($alterSql);
                    error_log($logPrefix . "Added column: $columnName");
                }
            }

        } catch (\Exception $e) {
            error_log($logPrefix . "Error adding OAuth columns: " . $e->getMessage());
        }
    }

    /**
     * Инициализирует таблицу текстов, если она еще не существует.
     *
     * @param \SQLite3 $db Объект подключения к БД.
     */
    private static function initializeTextsTable(\SQLite3 $db): void {
        $logPrefix = "Database::initializeTextsTable - ";

        try {
            // Проверяем, существует ли таблица texts
            $tableExists = false;
            $result = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='texts'");
            if ($result) {
                $row = $result->fetchArray();
                $tableExists = !empty($row);
            }

            // Если таблица не существует, создаем ее
            if (!$tableExists) {
                error_log($logPrefix . "Table 'texts' not found. Creating table...");

                // SQL для создания таблицы texts
                $createTableSql = "
                CREATE TABLE IF NOT EXISTS texts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT NOT NULL,
                    language TEXT NOT NULL DEFAULT 'ru',
                    text TEXT NOT NULL,
                    UNIQUE(key, language)
                );";

                if ($db->exec($createTableSql)) {
                    error_log($logPrefix . "Successfully created table 'texts'.");

                    // Добавляем начальные данные
                    self::seedTextsData($db);
                } else {
                    throw new \Exception("Failed to create table 'texts': " . $db->lastErrorMsg());
                }
            } else {
                // Проверяем, есть ли данные в таблице
                $result = $db->query("SELECT COUNT(*) as count FROM texts");
                $row = $result->fetchArray();
                $count = $row['count'] ?? 0;

                if ($count == 0) {
                    error_log($logPrefix . "Table 'texts' exists but is empty. Adding initial data...");
                    self::seedTextsData($db);
                } else {
                    error_log($logPrefix . "Table 'texts' already exists and contains data.");
                }
            }

            // Генерируем JS-файл с текстами
            self::generateTextsJsFile($db);

        } catch (\Exception $e) {
            error_log($logPrefix . "Error during initialization of table 'texts': " . $e->getMessage());
        }
    }

    /**
     * Добавляет начальные данные в таблицу текстов.
     *
     * @param \SQLite3 $db Объект подключения к БД.
     */
    private static function seedTextsData(\SQLite3 $db): void {
        $logPrefix = "Database::seedTextsData - ";

        // Начальные данные для текстов
        $initialTexts = [
            ['call_ai_button', 'ru', 'Звонок AI'],
            ['assistant_hint', 'ru', 'AI ассистент'],
            ['chat_title', 'ru', 'AI Ассистент'],
            ['new_chat_title', 'ru', 'Новый чат'],
            ['close_chat_title', 'ru', 'Закрыть чат'],
            ['mic_hint', 'ru', 'Говорите... Нажмите снова для остановки.'],
            ['mic_active_text', 'ru', 'Говорите'],
            ['message_placeholder', 'ru', 'Введите сообщение...'],
            ['voice_input_title', 'ru', 'Голосовой ввод'],
            ['send_button_title', 'ru', 'Отправить'],
            ['modal_title', 'ru', 'AI ассистент Дарья'],
            ['refresh_title', 'ru', 'Обновить подключение'],
            ['open_chat_title', 'ru', 'Открыть текстовый чат'],
            ['close_modal_title', 'ru', 'Закрыть'],
            ['connection_status', 'ru', 'Подключение...'],
            ['end_call_title', 'ru', 'Завершить разговор'],
            ['end_call_text', 'ru', 'Завершить'],
            ['voice_session_title', 'ru', 'Голосовой чат'],
            ['error_speech_not_supported', 'ru', 'Распознавание речи не поддерживается вашим браузером.'],
            ['error_mic_activation', 'ru', 'Не удалось активировать микрофон: '],
            ['error_recognition', 'ru', 'Ошибка распознавания'],
            ['error_chat_title_empty', 'ru', 'Название чата не может быть пустым.'],
            ['error_update_chat_title', 'ru', 'Не удалось обновить название чата: '],
            ['error_delete_chat', 'ru', 'Ошибка удаления чата: '],
            ['error_init', 'ru', 'Произошла ошибка при инициализации интерфейса ассистента. Пожалуйста, обновите страницу или обратитесь в поддержку.'],
            ['interrupt_response', 'ru', 'Прервать ответ']
        ];

        try {
            // Начинаем транзакцию
            $db->exec('BEGIN;');

            // Подготавливаем запрос для вставки
            $stmt = $db->prepare("INSERT OR IGNORE INTO texts (key, language, text) VALUES (:key, :language, :text)");

            // Вставляем каждую запись
            foreach ($initialTexts as $text) {
                $stmt->bindValue(':key', $text[0], SQLITE3_TEXT);
                $stmt->bindValue(':language', $text[1], SQLITE3_TEXT);
                $stmt->bindValue(':text', $text[2], SQLITE3_TEXT);
                $stmt->execute();
                $stmt->reset();
            }

            // Завершаем транзакцию
            $db->exec('COMMIT;');
            error_log($logPrefix . "Successfully added initial texts data.");

        } catch (\Exception $e) {
            // В случае ошибки откатываем транзакцию
            $db->exec('ROLLBACK;');
            error_log($logPrefix . "Error adding initial texts data: " . $e->getMessage());
        }
    }

    /**
     * Генерирует JS-файл с текстами.
     *
     * @param \SQLite3 $db Объект подключения к БД.
     */
    private static function generateTextsJsFile(\SQLite3 $db): void {
        $logPrefix = "Database::generateTextsJsFile - ";

        try {
            // Получаем все тексты для русского языка
            $stmt = $db->prepare("SELECT key, text FROM texts WHERE language = 'ru'");
            $result = $stmt->execute();

            $texts = [];
            if ($result) {
                while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                    $texts[$row['key']] = $row['text'];
                }
            }

            if (!empty($texts)) {
                // Формируем содержимое JS-файла
                $jsContent = "/**\n";
                $jsContent .= " * Автоматически сгенерированный файл с текстами для языка: ru\n";
                $jsContent .= " * Дата генерации: " . date('Y-m-d H:i:s') . "\n";
                $jsContent .= " */\n\n";
                $jsContent .= "export const texts = " . json_encode($texts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . ";\n";

                // Путь к файлу
                $filePath = dirname(__DIR__, 2) . '/chat-js/texts_ru.js';

                // Сохраняем файл
                $result = file_put_contents($filePath, $jsContent);

                if ($result !== false) {
                    error_log($logPrefix . "JS-файл с текстами успешно сгенерирован: $filePath");
                } else {
                    error_log($logPrefix . "Ошибка при генерации JS-файла с текстами: $filePath");
                }
            } else {
                error_log($logPrefix . "Нет текстов для генерации JS-файла.");
            }

        } catch (\Exception $e) {
            error_log($logPrefix . "Error generating texts JS file: " . $e->getMessage());
        }
    }
}
