export class ServerSpeechRecognizer {
    constructor({ onStart, onResult, onError, onEnd, onRecordingStop }) { // Added onRecordingStop
        this.onStartCallback = onStart;
        this.onResultCallback = onResult;
        this.onErrorCallback = onError;
        this.onEndCallback = onEnd;
        this.onRecordingStopCallback = onRecordingStop; // Store the new callback

        this.mediaRecorder = null;
        this.chunks = [];
        this.isListening = false;
        this.error = null;
        this.stream = null;
        this.audioCtx = null;
        this.sourceNode = null;
        this.analyser = null;
        this._vadIntervalId = null;
    }

    async start() {
        console.log("ServerSpeechRecognizer: Attempting to start...");
        if (this.isListening) {
            console.warn("ServerSpeechRecognizer: Already listening.");
            return;
        }
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            console.log("ServerSpeechRecognizer: Got user media.");
            this.mediaRecorder = new MediaRecorder(this.stream, { mimeType: 'audio/webm' });
            this.chunks = [];

            // --- Voice activity detection ---
            this.audioCtx = new (window.AudioContext || window.webkitAudioContext)();
            this.sourceNode = this.audioCtx.createMediaStreamSource(this.stream);
            this.analyser = this.audioCtx.createAnalyser();
            this.analyser.fftSize = 512;
            this.analyser.smoothingTimeConstant = 0.5;
            this.sourceNode.connect(this.analyser);
            this.silenceStart = null;
            this.silenceDelay = 1700; // 1.7 seconds of silence
            this.volumeThreshold = 0.01; // Adjust if needed

            const dataArray = new Uint8Array(this.analyser.frequencyBinCount);

            const detectSilence = () => {
                if (!this.isListening) { // Stop checking if not listening
                    if (this._vadIntervalId) clearInterval(this._vadIntervalId);
                    this._vadIntervalId = null;
                    return;
                }

                this.analyser.getByteFrequencyData(dataArray);
                let sum = 0;
                for (let i = 0; i < dataArray.length; i++) {
                    sum += dataArray[i];
                }
                const averageVolume = sum / dataArray.length;
                const volume = averageVolume / 128.0;

                if (volume > this.volumeThreshold) {
                    this.silenceStart = null; // Reset silence timer if sound detected
                } else {
                    if (!this.silenceStart) {
                        this.silenceStart = Date.now(); // Start silence timer
                    } else {
                        const elapsed = Date.now() - this.silenceStart;
                        if (elapsed > this.silenceDelay) {
                            console.log(`VAD: Silence duration > ${this.silenceDelay}ms. Stopping recording.`);
                            if (this._vadIntervalId) clearInterval(this._vadIntervalId); // Stop VAD check
                            this._vadIntervalId = null;
                            if (this.isListening) {
                                try {
                                    this.mediaRecorder.stop(); // Stop recorder, triggers onstop
                                } catch(e) {
                                    console.warn('ServerSpeechRecognizer: Error stopping recorder on silence:', e);
                                    // Force cleanup if stop fails
                                    this.cleanupAudioResources();
                                    this.isListening = false;
                                }
                            }
                        }
                    }
                }
            };

            if (this._vadIntervalId) clearInterval(this._vadIntervalId); // Clear previous interval if any
            this._vadIntervalId = setInterval(detectSilence, 100); // Check every 100ms
            // --- end VAD ---

            this.mediaRecorder.onstart = () => {
                console.log("ServerSpeechRecognizer: Recording started.");
                this.isListening = true;
                this.error = null;
                this.silenceStart = null; // Reset silence timer on start
                if (typeof this.onStartCallback === 'function') this.onStartCallback();
            };

            this.mediaRecorder.ondataavailable = (e) => {
                if (e.data.size > 0) {
                    this.chunks.push(e.data);
                }
            };

            this.mediaRecorder.onstop = async () => {
                console.log("ServerSpeechRecognizer: Recording stopped (onstop event).");
                this.cleanupAudioResources(); // Clean up VAD interval and AudioContext

                // Call the new callback to signal recording stopped (for UI/state update)
                if (typeof this.onRecordingStopCallback === 'function') {
                    this.onRecordingStopCallback();
                }

                this.isListening = false; // Ensure listening state is false

                if (this.chunks.length === 0) {
                    console.warn("ServerSpeechRecognizer: No audio chunks recorded.");
                    // Call onError directly to signal failure to record
                    if (typeof this.onErrorCallback === 'function') this.onErrorCallback({ error: 'no_audio_data', message: 'No audio data recorded' });
                    if (typeof this.onEndCallback === 'function') this.onEndCallback({ error: 'no_audio_data' });
                    return; // Don't proceed further
                }

                // Proceed with conversion and sending
                try {
                    const blob = new Blob(this.chunks, { type: 'audio/webm' });
                    console.log(`ServerSpeechRecognizer: Converting blob size: ${blob.size}`);
                    const wavBlob = await this.convertWebmToWav(blob);
                    this.sendToServer(wavBlob);
                } catch (conversionError) {
                     console.error("ServerSpeechRecognizer: Error converting audio:", conversionError);
                     if (typeof this.onErrorCallback === 'function') this.onErrorCallback({ error: 'conversion_error', message: conversionError.message });
                     if (typeof this.onEndCallback === 'function') this.onEndCallback({ error: 'conversion_error' });
                }
            };

            this.mediaRecorder.onerror = (e) => {
                console.error("ServerSpeechRecognizer: MediaRecorder error", e);
                this.cleanupAudioResources();
                this.isListening = false;
                this.error = e.error?.name || 'recorder_error';
                if (typeof this.onErrorCallback === 'function') this.onErrorCallback({ error: this.error, message: e.error?.message || 'MediaRecorder error' });
                if (typeof this.onEndCallback === 'function') this.onEndCallback({ error: this.error });
            };

            this.mediaRecorder.start(1000); // Collect chunks every second (doesn't affect VAD)
            console.log("ServerSpeechRecognizer: MediaRecorder started.");

        } catch (e) {
            console.error('ServerSpeechRecognizer: start() error (getUserMedia or setup failed)', e);
            this.isListening = false;
            this.error = e.name || 'start_error';
            this.cleanupAudioResources(); // Clean up if setup failed
            if (typeof this.onErrorCallback === 'function') this.onErrorCallback({ error: this.error, message: e.message });
            if (typeof this.onEndCallback === 'function') this.onEndCallback({ error: this.error });
        }
    }

    stop() {
        console.log("ServerSpeechRecognizer: stop() called.");
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
             console.log("ServerSpeechRecognizer: Stopping MediaRecorder...");
            try {
                this.mediaRecorder.stop(); // This will trigger 'onstop'
            } catch (e) {
                 console.warn("ServerSpeechRecognizer: Error calling mediaRecorder.stop():", e);
                 this.cleanupAudioResources(); // Force cleanup
                 this.isListening = false;
            }
        } else {
            console.log("ServerSpeechRecognizer: stop() called but recorder not recording.");
            this.cleanupAudioResources(); // Still cleanup resources
            this.isListening = false;
        }
        // Stop media stream tracks
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            console.log("ServerSpeechRecognizer: MediaStream tracks stopped.");
            this.stream = null;
        }
    }

    abort() {
        console.log("ServerSpeechRecognizer: abort() called.");
        this.cleanupAudioResources(); // Stop VAD, close context
        if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
             console.log("ServerSpeechRecognizer: Aborting MediaRecorder...");
             // Don't call stop() as we don't want onstop to trigger sendToServer
             try {
                 // A direct way to stop without triggering onstop isn't standard.
                 // We rely on cleanup and state flags.
                 this.mediaRecorder.ondataavailable = null; // Prevent further data chunks
                 this.mediaRecorder.onstop = null; // Prevent onstop logic
                 this.mediaRecorder.onerror = null; // Prevent error logic during abort
                 if (this.mediaRecorder.state === 'recording') {
                     this.mediaRecorder.requestData(); // Try to get last chunk if needed? Maybe not.
                     // No standard 'abort', just stop tracks and cleanup
                 }
             } catch(e) {
                 console.warn("ServerSpeechRecognizer: Error during abort cleanup:", e);
             }
        }
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            console.log("ServerSpeechRecognizer: MediaStream tracks stopped during abort.");
            this.stream = null;
        }
        this.isListening = false;
        this.chunks = []; // Clear chunks
        // Trigger onEnd with an 'aborted' error type if needed by the handler
        if (typeof this.onEndCallback === 'function') this.onEndCallback({ error: 'aborted' });
    }

    cleanupAudioResources() {
        console.log("ServerSpeechRecognizer: Cleaning up audio resources (VAD interval, AudioContext).");
        if (this._vadIntervalId) {
            clearInterval(this._vadIntervalId);
            this._vadIntervalId = null;
        }
        if (this.audioCtx && this.audioCtx.state !== 'closed') {
            this.audioCtx.close().catch(e => console.warn("Error closing AudioContext:", e));
            this.audioCtx = null;
        }
        this.sourceNode = null;
        this.analyser = null;
    }

    destroy() {
        console.log("ServerSpeechRecognizer: destroy() called.");
        this.abort(); // Perform abort logic
        this.mediaRecorder = null; // Release recorder reference
        // Nullify callbacks
        this.onStartCallback = null;
        this.onResultCallback = null;
        this.onErrorCallback = null;
        this.onEndCallback = null;
        this.onRecordingStopCallback = null;
        console.log("ServerSpeechRecognizer: Instance destroyed.");
    }

    async convertWebmToWav(blob) {
        // Используем webm-to-wav конвертацию через AudioContext
        console.log("ServerSpeechRecognizer: Converting to WAV...");
        const tempAudioCtx = new (window.AudioContext || window.webkitAudioContext)(); // Use temporary context
        try {
            const arrayBuffer = await blob.arrayBuffer();
            const audioBuffer = await tempAudioCtx.decodeAudioData(arrayBuffer);
            const wavBuffer = this.encodeWAV(audioBuffer);
            console.log("ServerSpeechRecognizer: WAV conversion successful.");
            return new Blob([wavBuffer], { type: 'audio/wav' });
        } catch (error) {
             console.error("Error during WAV conversion:", error);
             throw error; // Re-throw to be caught in onstop
        } finally {
            tempAudioCtx.close().catch(e => console.warn("Error closing temp AudioContext:", e));
        }
    }

    encodeWAV(audioBuffer) {
        const numChannels = 1; // Force mono
        const sampleRate = 8000; // Target sample rate for Yandex
        const format = 1; // PCM
        const bitsPerSample = 16;

        // Downsample and convert to mono if necessary
        const samples = this.processAudioBuffer(audioBuffer, sampleRate);

        const blockAlign = numChannels * bitsPerSample / 8;
        const byteRate = sampleRate * blockAlign;
        const dataSize = samples.length * bitsPerSample / 8; // Recalculate based on actual samples length

        const buffer = new ArrayBuffer(44 + dataSize);
        const view = new DataView(buffer);

        let offset = 0;

        function writeString(s) {
            for (let i = 0; i < s.length; i++) {
                view.setUint8(offset++, s.charCodeAt(i));
            }
        }

        // RIFF header
        writeString('RIFF');
        view.setUint32(offset, 36 + dataSize, true); offset += 4;
        writeString('WAVE');
        // fmt chunk
        writeString('fmt ');
        view.setUint32(offset, 16, true); offset += 4; // Subchunk1Size (16 for PCM)
        view.setUint16(offset, format, true); offset += 2; // AudioFormat
        view.setUint16(offset, numChannels, true); offset += 2; // NumChannels
        view.setUint32(offset, sampleRate, true); offset += 4; // SampleRate
        view.setUint32(offset, byteRate, true); offset += 4; // ByteRate
        view.setUint16(offset, blockAlign, true); offset += 2; // BlockAlign
        view.setUint16(offset, bitsPerSample, true); offset += 2; // BitsPerSample
        // data chunk
        writeString('data');
        view.setUint32(offset, dataSize, true); offset += 4; // Subchunk2Size

        // Write samples
        this.floatTo16BitPCM(view, offset, samples);

        return buffer;
    }

     // Helper to downsample and ensure mono
    processAudioBuffer(buffer, targetSampleRate) {
        const sourceSampleRate = buffer.sampleRate;
        const numberOfChannels = buffer.numberOfChannels;
        let sourceData = buffer.getChannelData(0); // Start with the first channel

        // Mix down to mono if necessary
        if (numberOfChannels > 1) {
            console.log(`ServerSpeechRecognizer: Mixing ${numberOfChannels} channels to mono.`);
            const monoData = new Float32Array(sourceData.length);
            for (let i = 0; i < sourceData.length; i++) {
                let sampleSum = 0;
                for (let j = 0; j < numberOfChannels; j++) {
                    sampleSum += buffer.getChannelData(j)[i];
                }
                monoData[i] = sampleSum / numberOfChannels;
            }
            sourceData = monoData;
        }

        // Downsample if necessary
        if (targetSampleRate === sourceSampleRate) {
            return sourceData; // No downsampling needed
        }
        if (targetSampleRate > sourceSampleRate) {
             console.warn("ServerSpeechRecognizer: Target sample rate higher than source. Upsampling not implemented, returning original.");
             return sourceData; // Or implement upsampling if needed
        }

        console.log(`ServerSpeechRecognizer: Downsampling from ${sourceSampleRate}Hz to ${targetSampleRate}Hz.`);
        const sampleRateRatio = sourceSampleRate / targetSampleRate;
        const newLength = Math.round(sourceData.length / sampleRateRatio);
        const result = new Float32Array(newLength);
        let offsetResult = 0;
        let offsetBuffer = 0;
        while (offsetResult < result.length) {
            const nextOffsetBuffer = Math.round((offsetResult + 1) * sampleRateRatio);
            let accum = 0, count = 0;
            // Simple averaging for downsampling
            for (let i = Math.floor(offsetBuffer); i < Math.floor(nextOffsetBuffer) && i < sourceData.length; i++) {
                accum += sourceData[i];
                count++;
            }
            result[offsetResult] = count > 0 ? accum / count : 0; // Avoid division by zero
            offsetResult++;
            offsetBuffer = nextOffsetBuffer;
        }
        return result;
    }


    floatTo16BitPCM(view, offset, input) {
        for (let i = 0; i < input.length; i++, offset += 2) {
            let s = Math.max(-1, Math.min(1, input[i]));
            s = s < 0 ? s * 0x8000 : s * 0x7FFF; // Convert to 16-bit signed integer
            view.setInt16(offset, s, true); // Write as little-endian
        }
    }

    async sendToServer(wavBlob) {
        const formData = new FormData();
        formData.append('audio', wavBlob, 'audio.wav');
        console.log("ServerSpeechRecognizer: Sending WAV blob to server...");

        try {
            const response = await fetch('chat-admin/voice_recognize.php', {
                method: 'POST',
                body: formData
            });
            const responseText = await response.text(); // Get raw text first
            console.log('Raw response from voice_recognize.php:', responseText);

            let result = null;
            try {
                result = JSON.parse(responseText); // Try parsing JSON
            } catch (jsonErr) {
                console.error('Error parsing JSON from voice_recognize.php:', jsonErr, "Response text:", responseText);
                // Call onError callback with parse error
                if (typeof this.onErrorCallback === 'function') this.onErrorCallback({ error: 'json_parse_error', message: `Failed to parse server response: ${jsonErr.message}` });
                if (typeof this.onEndCallback === 'function') this.onEndCallback({ error: 'json_parse_error' });
                return; // Stop processing
            }

            // Check if recognition was successful (result.text exists)
            if (result && result.text) {
                console.log("ServerSpeechRecognizer: Recognition successful:", result.text);
                // Call onResult callback with the recognized text
                if (typeof this.onResultCallback === 'function') {
                    this.onResultCallback({
                        results: [[{ transcript: result.text, isFinal: true }]], // Mimic Web Speech API structure
                        resultIndex: 0
                    });
                }
            } else { // Recognition failed or error reported by PHP script
                const errorMessage = (result && result.error) ? result.error : 'No text recognized or unknown server error';
                console.warn("ServerSpeechRecognizer: Recognition failed:", errorMessage, "Raw data:", result?.raw);
                // Call onError callback with recognition_failed error
                if (typeof this.onErrorCallback === 'function') this.onErrorCallback({ error: 'recognition_failed', message: errorMessage });
            }
        } catch (e) { // Catch network errors during fetch
            console.error('ServerSpeechRecognizer: Network error sending audio to server:', e);
            // Call onError callback with network_error
            if (typeof this.onErrorCallback === 'function') this.onErrorCallback({ error: 'network_error', message: e.message });
        } finally {
            // Always call onEnd after attempting to send, regardless of success/failure
            // The error state (this.error) might have been set in onError handlers
            console.log("ServerSpeechRecognizer: Calling onEnd callback.");
            if (typeof this.onEndCallback === 'function') this.onEndCallback({ error: this.error });
            this.error = null; // Reset error state for next recognition cycle
        }
    }
}
