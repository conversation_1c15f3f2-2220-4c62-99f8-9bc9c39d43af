<?php

namespace App\Controllers;

// Используем ТОЛЬКО необходимые сервисы/классы
use App\Services\EdgeTtsService; // Главный сервис
use Exception; // Базовый Exception

// НЕ НАСЛЕДУЕМСЯ от BaseController, чтобы не тянуть лишнего
class TtsController {

    private EdgeTtsService $edgeTtsService;

    // Конструктор инициализирует ТОЛЬКО TTS сервис
    public function __construct() {
        // --- VVV ИЗМЕНЕНИЕ ЗДЕСЬ VVV ---
        // Создаем сервис БЕЗ передачи логгера как аргумента.
        // Если конструктор EdgeTtsService принимает таймаут, можно передать его:
        // $this->edgeTtsService = new EdgeTtsService(30); // Пример: таймаут 30 секунд
        // Если конструктор EdgeTtsService не принимает аргументов или принимает только таймаут:
        $this->edgeTtsService = new EdgeTtsService();
        // --- ^^^ КОНЕЦ ИЗМЕНЕНИЯ ^^^ ---

        // Логируем инициализацию самого TtsController
        $this->log("TtsController initialized.");
    }

    /**
     * Простой метод для логирования из этого контроллера
     */
    private function log(string $message): void {
         $logFile = dirname(__DIR__, 2) . '/debug_log.txt';
         $date = date('[Y-m-d H:i:s] ');
         // Добавим идентификатор запроса или пользователя, если возможно/нужно
         $requestId = $_GET['request_id'] ?? substr(md5(uniqid()), 0, 6); // Пример ID
         file_put_contents($logFile, $date . "TtsController [$requestId] - " . $message . "\n", FILE_APPEND | LOCK_EX);
    }


    /**
     * Обрабатывает запрос на синтез речи.
     */
    public function synthesize(): void {
        header('Content-Type: application/json; charset=utf-8');
        $response = ['status' => 'error', 'message' => 'TTS Error'];
        $httpCode = 500;

        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $httpCode = 405;
                throw new Exception('Method Not Allowed. POST required.');
            }

            $input = file_get_contents('php://input');
            if (!$input) { // Проверка на пустой input
                 $httpCode = 400;
                 throw new Exception('Empty request body');
            }
            $data = json_decode($input, true);
            // Проверка на ошибку JSON decode
             if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
                 $httpCode = 400;
                 throw new Exception('Invalid JSON received: ' . json_last_error_msg());
             }

            $text = trim($data['text'] ?? '');

            if (!$text) {
                $httpCode = 400;
                throw new Exception('No text provided');
            }

            $this->log("Synthesize request received for text: " . substr($text, 0, 100) . "...");

            // Вызываем сервис (предполагаем, что он возвращает null при ошибке)
            $audioBase64 = $this->edgeTtsService->synthesizeSpeech($text);

            if ($audioBase64 !== null) {
                $this->log("Synthesis successful. Returning audio (length: ".strlen($audioBase64).").");
                $response = ['status' => 'success', 'audio' => $audioBase64];
                $httpCode = 200;
            } else {
                // Ошибка уже залогирована внутри сервиса
                $this->log("Synthesis failed (service returned null).");
                $response = ['status' => 'error', 'message' => 'TTS synthesis failed'];
                // Оставляем 500, т.к. это внутренняя проблема синтеза
                $httpCode = 500;
            }

        } catch (Exception $e) {
            $this->log("Exception caught - " . $e->getMessage());
            $response['message'] = $e->getMessage();
            if ($httpCode === 500 && $e->getCode() >= 400 && $e->getCode() < 600) {
                $httpCode = $e->getCode();
            }
        }

        if (!headers_sent()) {
            http_response_code($httpCode);
        }
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
}