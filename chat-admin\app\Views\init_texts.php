<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Инициализация таблицы текстов</h2>
            </div>
            <div class="card-body">
                <p>Эта страница позволяет инициализировать таблицу текстов в базе данных и создать JS-файл с текстами.</p>
                <p>Нажмите кнопку ниже, чтобы запустить процесс инициализации:</p>
                
                <button id="init-texts-btn" class="btn btn-primary">Инициализировать таблицу текстов</button>
                
                <div id="init-result" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const initButton = document.getElementById('init-texts-btn');
    const resultDiv = document.getElementById('init-result');
    
    initButton.addEventListener('click', async function() {
        initButton.disabled = true;
        resultDiv.innerHTML = '<div class="alert alert-info">Выполняется инициализация таблицы текстов...</div>';
        
        try {
            const response = await fetch('index.php?page=init_texts_process');
            const html = await response.text();
            
            resultDiv.innerHTML = `<div class="alert alert-success">
                <h4>Результат инициализации:</h4>
                <div>${html}</div>
            </div>`;
        } catch (error) {
            resultDiv.innerHTML = `<div class="alert alert-danger">
                <h4>Ошибка при инициализации:</h4>
                <p>${error.message}</p>
            </div>`;
            console.error('Ошибка при инициализации таблицы текстов:', error);
        } finally {
            initButton.disabled = false;
        }
    });
});
</script>
