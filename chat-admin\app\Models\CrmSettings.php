<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Database;

/**
 * Модель для работы с настройками интеграции CRM
 */
class CrmSettings
{
    private \SQLite3 $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Получает настройки CRM
     *
     * @return array|null Массив с настройками или null, если настройки не найдены
     */
    public function getSettings(): ?array
    {
        try {
            $stmt = $this->db->prepare("SELECT * FROM crm_settings ORDER BY id DESC LIMIT 1");
            $result = $stmt->execute();
            $settings = $result->fetchArray(\SQLITE3_ASSOC);
            
            return $settings ?: null;
        } catch (\Exception $e) {
            error_log("CrmSettings::getSettings - Error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Сохраняет настройки CRM
     *
     * @param array $data Данные настроек
     * @return bool Успешность операции
     */
    public function saveSettings(array $data): bool
    {
        try {
            $currentSettings = $this->getSettings();

            if ($currentSettings) {
                // Обновляем существующие настройки
                $stmt = $this->db->prepare("
                    UPDATE crm_settings SET
                        amocrm_subdomain = :amocrm_subdomain,
                        amocrm_access_token = :amocrm_access_token,
                        amocrm_pipeline_id = :amocrm_pipeline_id,
                        amocrm_responsible_user_id = :amocrm_responsible_user_id,
                        yandex_metrika_counter_id = :yandex_metrika_counter_id,
                        yandex_metrika_goal_name = :yandex_metrika_goal_name,
                        integration_enabled = :integration_enabled,
                        is_active = :is_active,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id
                ");

                $stmt->bindValue(':id', $currentSettings['id'], \SQLITE3_INTEGER);
            } else {
                // Создаем новые настройки
                $stmt = $this->db->prepare("
                    INSERT INTO crm_settings (
                        amocrm_subdomain,
                        amocrm_access_token,
                        amocrm_pipeline_id,
                        amocrm_responsible_user_id,
                        yandex_metrika_counter_id,
                        yandex_metrika_goal_name,
                        integration_enabled,
                        is_active
                    ) VALUES (
                        :amocrm_subdomain,
                        :amocrm_access_token,
                        :amocrm_pipeline_id,
                        :amocrm_responsible_user_id,
                        :yandex_metrika_counter_id,
                        :yandex_metrika_goal_name,
                        :integration_enabled,
                        :is_active
                    )
                ");
            }

            // Привязываем значения
            $stmt->bindValue(':amocrm_subdomain', $data['amocrm_subdomain'] ?? 'advokatpushkarev', \SQLITE3_TEXT);
            $stmt->bindValue(':amocrm_access_token', $data['amocrm_access_token'] ?? null, \SQLITE3_TEXT);
            $stmt->bindValue(':amocrm_pipeline_id', $data['amocrm_pipeline_id'] ?? null, \SQLITE3_INTEGER);
            $stmt->bindValue(':amocrm_responsible_user_id', $data['amocrm_responsible_user_id'] ?? null, \SQLITE3_INTEGER);
            $stmt->bindValue(':yandex_metrika_counter_id', (int)($data['yandex_metrika_counter_id'] ?? 100128474), \SQLITE3_INTEGER);
            $stmt->bindValue(':yandex_metrika_goal_name', $data['yandex_metrika_goal_name'] ?? 'chat_contact_got', \SQLITE3_TEXT);
            $stmt->bindValue(':integration_enabled', (int)($data['integration_enabled'] ?? 1), \SQLITE3_INTEGER);
            $stmt->bindValue(':is_active', (int)($data['is_active'] ?? 0), \SQLITE3_INTEGER);

            return (bool)$stmt->execute();
        } catch (\Exception $e) {
            error_log("CrmSettings::saveSettings - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверяет, активна ли интеграция с CRM
     *
     * @return bool
     */
    public function isActive(): bool
    {
        $settings = $this->getSettings();
        return $settings && (bool)$settings['is_active'];
    }

    /**
     * Получает настройки AmoCRM
     *
     * @return array|null
     */
    public function getAmoCrmSettings(): ?array
    {
        $settings = $this->getSettings();
        if (!$settings) {
            return null;
        }

        return [
            'portal_url' => $settings['amocrm_portal_url'],
            'client_id' => $settings['amocrm_client_id'],
            'client_secret' => $settings['amocrm_client_secret'],
            'redirect_uri' => $settings['amocrm_redirect_uri'],
            'client_field_id' => $settings['amocrm_client_id_field_id']
        ];
    }

    /**
     * Получает настройки Яндекс.Метрики
     *
     * @return array|null
     */
    public function getYandexMetrikaSettings(): ?array
    {
        $settings = $this->getSettings();
        if (!$settings) {
            return null;
        }

        return [
            'counter_id' => $settings['yandex_metrika_counter_id'],
            'goal_name' => $settings['yandex_metrika_goal_name']
        ];
    }

    /**
     * Обновляет OAuth токены
     *
     * @param string $accessToken Access token
     * @param string $refreshToken Refresh token
     * @param int $expiresAt Время истечения токена (timestamp)
     * @return bool Успешность операции
     */
    public function updateTokens(string $accessToken, string $refreshToken, int $expiresAt): bool
    {
        try {
            $currentSettings = $this->getSettings();

            if (!$currentSettings) {
                error_log("CrmSettings::updateTokens - No settings found");
                return false;
            }

            $stmt = $this->db->prepare("
                UPDATE crm_settings SET
                    amocrm_access_token = :access_token,
                    amocrm_refresh_token = :refresh_token,
                    amocrm_token_expires_at = :expires_at
                WHERE id = :id
            ");

            $stmt->bindValue(':access_token', $accessToken, \SQLITE3_TEXT);
            $stmt->bindValue(':refresh_token', $refreshToken, \SQLITE3_TEXT);
            $stmt->bindValue(':expires_at', $expiresAt, \SQLITE3_INTEGER);
            $stmt->bindValue(':id', $currentSettings['id'], \SQLITE3_INTEGER);

            return (bool)$stmt->execute();
        } catch (\Exception $e) {
            error_log("CrmSettings::updateTokens - Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Получает OAuth данные для AmoCRM
     *
     * @return array|null Массив с OAuth данными или null
     */
    public function getOAuthData(): ?array
    {
        $settings = $this->getSettings();

        if (!$settings) {
            return null;
        }

        return [
            'client_id' => $settings['amocrm_client_id'] ?? null,
            'client_secret' => $settings['amocrm_client_secret'] ?? null,
            'access_token' => $settings['amocrm_access_token'] ?? null,
            'refresh_token' => $settings['amocrm_refresh_token'] ?? null,
            'expires_at' => $settings['amocrm_token_expires_at'] ?? null,
            'portal_url' => $settings['amocrm_portal_url'] ?? null
        ];
    }

    /**
     * Проверяет, настроен ли OAuth для AmoCRM
     *
     * @return bool
     */
    public function isOAuthConfigured(): bool
    {
        $oauthData = $this->getOAuthData();

        return $oauthData &&
               !empty($oauthData['client_id']) &&
               !empty($oauthData['client_secret']) &&
               !empty($oauthData['portal_url']);
    }

    /**
     * Проверяет, есть ли действительные токены
     *
     * @return bool
     */
    public function hasValidTokens(): bool
    {
        $oauthData = $this->getOAuthData();

        return $oauthData &&
               !empty($oauthData['access_token']) &&
               !empty($oauthData['refresh_token']) &&
               $oauthData['expires_at'] > time();
    }


}
