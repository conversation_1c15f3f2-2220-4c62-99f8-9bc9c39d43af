name: CI

on:
  push:
  pull_request:

jobs:
  psalm:
    name: Psalm
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.1"

      - name: Install composer dependencies
        uses: "ramsey/composer-install@v1"

      - name: Run Psalm
        run: vendor/bin/psalm --show-info=false --find-unused-psalm-suppress --no-progress

  phpunit:
    name: PHPUnit
    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-version:
          - "7.4"
          - "8.0"
          - "8.1"
        calculator:
          - GMP
          - BCMath
          - Native

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          coverage: pcov

      - name: Install composer dependencies
        uses: "ramsey/composer-install@v1"

      - name: Run PHPUnit
        run: vendor/bin/phpunit
        env:
          CALCULATOR: ${{ matrix.calculator }}
        if: ${{ matrix.php-version != '8.0' }}

      - name: Run PHPUnit with coverage
        run: |
          mkdir -p build/logs
          vendor/bin/phpunit --coverage-clover build/logs/clover.xml
        env:
          CALCULATOR: ${{ matrix.calculator }}
        if: ${{ matrix.php-version == '8.0' }}

      - name: Run PHPUnit with bcscale()
        run: vendor/bin/phpunit
        env:
          CALCULATOR: BCMath
          BCMATH_DEFAULT_SCALE: 8
        if: ${{ matrix.calculator == 'BCMath' }}

      - name: Upload coverage report to Coveralls
        run: vendor/bin/php-coveralls --coverage_clover=build/logs/clover.xml -v
        env:
          COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        if: ${{ matrix.php-version == '8.0' }}
