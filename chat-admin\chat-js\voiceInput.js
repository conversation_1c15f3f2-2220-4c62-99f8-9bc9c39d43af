// chat-js/voiceInput.js
import { SpeechRecognizer } from './speechRecognizer.js';

let recognizer = null;
let sendTimeout = null;
let isRecognizing = false;

export function toggleVoiceInput(inputSelector, onSendCallback) {
    const inputEl = document.querySelector(inputSelector);
    if (!inputEl) {
        console.error('voiceInput: input element not found:', inputSelector);
        return;
    }

    const micBtn = document.getElementById('micButton');
    const micHint = document.getElementById('micHint');

    if (micBtn && micBtn.classList.contains('recording')) {
        console.log('voiceInput: stopping recognition by user click');
        if (recognizer) recognizer.stop();
        isRecognizing = false;
        clearTimeout(sendTimeout);
        micBtn.classList.remove('recording');
        if (micHint) {
            micHint.style.opacity = '0';
            micHint.style.visibility = 'hidden';
        }
        inputEl.placeholder = 'Введите сообщение...';
        return;
    }

    if (!recognizer) {
        recognizer = new SpeechRecognizer({
            onStart: () => {
                console.log('voiceInput: recognition started');
                isRecognizing = true;
                inputEl.placeholder = 'Говорите...';
                const micBtn = document.getElementById('micButton');
                if (micBtn) micBtn.classList.add('recording');
                const micHint = document.getElementById('micHint');
                if (micHint) {
                    micHint.style.opacity = '1';
                    micHint.style.visibility = 'visible';
                }
            },
            onResult: (event) => {
                let interim = '';
                let finalText = '';
                for (let i = event.resultIndex; i < event.results.length; ++i) {
                    const transcript = event.results[i][0].transcript.trim();
                    if (event.results[i].isFinal) {
                        finalText += (finalText ? ' ' : '') + transcript;
                    } else {
                        interim = transcript;
                    }
                }
                inputEl.value = (finalText || interim).trim();

                // --- Таймер автоотправки через 2 секунды тишины ---
                clearTimeout(sendTimeout);
                sendTimeout = setTimeout(() => {
                    const text = inputEl.value.trim();
                    if (text && typeof onSendCallback === 'function') {
                        console.log('voiceInput: 2s silence, auto-sending:', text);
                        onSendCallback(text);
                    }
                    // Скрываем хинт и убираем класс
                    const micBtn = document.getElementById('micButton');
                    if (micBtn) micBtn.classList.remove('recording');
                    const micHint = document.getElementById('micHint');
                    if (micHint) {
                        micHint.style.opacity = '0';
                        micHint.style.visibility = 'hidden';
                    }
                }, 2000);
                // --- Конец таймера ---
                inputEl.placeholder = 'Введите сообщение...';
            },
            onError: (e) => {
                console.error('voiceInput: recognition error', e);
                inputEl.placeholder = 'Ошибка распознавания';
                isRecognizing = false;
                const micBtn = document.getElementById('micButton');
                if (micBtn) micBtn.classList.remove('recording');
                const micHint = document.getElementById('micHint');
                if (micHint) {
                    micHint.style.opacity = '0';
                    micHint.style.visibility = 'hidden';
                }
                inputEl.placeholder = 'Введите сообщение...';
            },
            onEnd: () => {
                console.log('voiceInput: recognition ended');
                isRecognizing = false;
                // Не скрываем хинт и не отправляем текст здесь
                // Ждем срабатывания таймера автоотправки
            }
        });
    }

    console.log('voiceInput: starting recognition');
    recognizer.start();
}

export function stopVoiceInput() {
    if (recognizer) {
        recognizer.stop();
    }
    clearTimeout(sendTimeout);
    isRecognizing = false;
}

export function isVoiceInputActive() {
    return isRecognizing;
}
