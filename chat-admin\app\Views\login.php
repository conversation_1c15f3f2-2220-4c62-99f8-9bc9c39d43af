<?php
// Ожидаем необязательную переменную $error из контроллера
$error = $error ?? null; // Устанавливаем в null, если не передана
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Вход в админ-панель</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../chat-admin/public/admin-css/admin.css">
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="fas fa-robot" style="font-size: 2.5rem; color: var(--primary); margin-bottom: 1rem;"></i>
            <h1 class="login-title">Вход в админ-панель</h1>
            <p>Используйте учетные данные администратора</p>
        </div>

        <!-- Форма отправляет POST-запрос на URL, который будет обрабатываться роутером -->
        <!-- TODO: Обновить action для соответствия роутеру (например, index.php?page=login) -->
        <form class="login-form" method="POST" action="index.php?page=login">
            <div class="form-group">
                <label for="username">Имя пользователя</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">Пароль</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit">
                <i class="fas fa-sign-in-alt"></i> Войти
            </button>

            <?php if ($error): ?>
                <div class="error-message"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>
        </form>
        <div class="forgot-password-link" style="text-align:center; margin-top:1rem;">
            <a href="index.php?page=forgot">Забыли пароль?</a>
        </div>
    </div>
</body>
</html>
