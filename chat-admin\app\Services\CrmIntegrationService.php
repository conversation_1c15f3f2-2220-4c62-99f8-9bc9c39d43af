<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\CrmSettings;
use App\Services\SimpleAmoCrmService;

/**
 * Основной сервис интеграции с CRM
 * Координирует создание лидов в AmoCRM при получении контактных данных из чата
 */
class CrmIntegrationService
{
    private CrmSettings $crmSettings;

    public function __construct()
    {
        $this->crmSettings = new CrmSettings();
    }

    /**
     * Обрабатывает сообщение и создает лид в AmoCRM, если найдены контактные данные
     *
     * @param string $message Текст сообщения
     * @param array $chatHistory История чата
     * @param array|null $analyticsData Аналитические данные (UTM, ClientID, URL)
     * @return array Результат обработки
     */
    public function processMessage(string $message, array $chatHistory = [], ?array $analyticsData = null): array
    {
        $result = [
            'success' => false,
            'lead_created' => false,
            'contact_created' => false,
            'lead_id' => null,
            'contact_id' => null,
            'message' => '',
            'phone_detected' => null
        ];

        try {
            // Проверяем, активна ли интеграция
            $settings = $this->crmSettings->getSettings();
            if (!$settings || empty($settings['integration_enabled'])) {
                $result['success'] = true;
                $result['message'] = 'CRM integration is disabled';
                return $result;
            }

            // Проверяем настройки AmoCRM
            if (empty($settings['amocrm_subdomain']) || empty($settings['amocrm_access_token'])) {
                $result['success'] = true;
                $result['message'] = 'AmoCRM not configured';
                return $result;
            }

            // Извлекаем телефон из сообщения
            $phone = SimpleAmoCrmService::extractPhone($message);
            if (!$phone) {
                $result['success'] = true;
                $result['message'] = 'No phone detected in message';
                return $result;
            }

            $result['phone_detected'] = $phone;

            // Создаем сервис AmoCRM
            $simpleAmoCrmService = new SimpleAmoCrmService(
                $settings['amocrm_subdomain'],
                $settings['amocrm_access_token']
            );

            // Формируем историю чата для комментария
            $formattedChatHistory = $this->formatChatHistory($chatHistory, $analyticsData);

            // Создаем лид с полной историей и аналитическими данными
            $crmResult = $simpleAmoCrmService->createFullLead(
                $phone,
                $formattedChatHistory,
                $settings['amocrm_pipeline_id'] ?? null,
                $settings['amocrm_responsible_user_id'] ?? null,
                $analyticsData
            );

            if ($crmResult['success']) {
                $result['success'] = true;
                $result['lead_created'] = true;
                $result['lead_id'] = $crmResult['lead_id'];
                $result['contact_id'] = $crmResult['contact_id'] ?? null;
                $result['message'] = 'Lead created successfully in AmoCRM';
            } else {
                $result['message'] = 'Failed to create lead: ' . $crmResult['message'];
            }

        } catch (\Exception $e) {
            error_log("CrmIntegrationService::processMessage - Error: " . $e->getMessage());
            $result['message'] = 'Error processing message: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Форматирует историю чата для передачи в AmoCRM
     */
    private function formatChatHistory(array $chatHistory, ?array $analyticsData = null): array
    {
        $formattedHistory = [];

        foreach ($chatHistory as $message) {
            // Определяем отправителя: проверяем разные возможные поля
            $isUser = false;
            if (isset($message['role'])) {
                $isUser = $message['role'] === 'user';
            } elseif (isset($message['sender'])) {
                $isUser = $message['sender'] === 'user';
            } elseif (isset($message['sender_type'])) {
                $isUser = $message['sender_type'] === 'user';
            }

            $formattedHistory[] = [
                'timestamp' => $message['created_at'] ?? $message['timestamp'] ?? time(),
                'is_user' => $isUser,
                'message' => $message['content'] ?? $message['message'] ?? ''
            ];
        }

        // Добавляем аналитические данные если есть
        if ($analyticsData) {
            $analyticsText = "Аналитические данные:\n";
            if (!empty($analyticsData['utm_source'])) {
                $analyticsText .= "UTM Source: " . $analyticsData['utm_source'] . "\n";
            }
            if (!empty($analyticsData['utm_medium'])) {
                $analyticsText .= "UTM Medium: " . $analyticsData['utm_medium'] . "\n";
            }
            if (!empty($analyticsData['utm_campaign'])) {
                $analyticsText .= "UTM Campaign: " . $analyticsData['utm_campaign'] . "\n";
            }
            if (!empty($analyticsData['page_url'])) {
                $analyticsText .= "Страница: " . $analyticsData['page_url'] . "\n";
            }
            if (!empty($analyticsData['clientId'])) {
                $analyticsText .= "Client ID: " . $analyticsData['clientId'] . "\n";
            }

            $formattedHistory[] = [
                'timestamp' => time(),
                'is_user' => false,
                'message' => $analyticsText
            ];
        }

        return $formattedHistory;
    }

    /**
     * Подготавливает данные контакта для AmoCRM
     *
     * @param array $contacts Детектированные контактные данные
     * @param array|null $analyticsData Аналитические данные
     * @return array
     */
    private function prepareContactData(array $contacts, ?array $analyticsData = null): array
    {
        $contactData = [
            'name' => 'Контакт из чата',
            'custom_fields' => []
        ];

        // Добавляем телефон
        if (!empty($contacts['phones'])) {
            $contactData['phone'] = $contacts['phones'][0]; // Берем первый телефон
        }

        // Добавляем email
        if (!empty($contacts['emails'])) {
            $contactData['email'] = $contacts['emails'][0]; // Берем первый email
        }

        // Добавляем ClientID Яндекс.Метрики в кастомное поле
        if ($analyticsData && !empty($analyticsData['clientId'])) {
            $amoCrmSettings = $this->crmSettings->getAmoCrmSettings();
            if ($amoCrmSettings && !empty($amoCrmSettings['client_id_field_id'])) {
                $contactData['custom_fields'][$amoCrmSettings['client_id_field_id']] = $analyticsData['clientId'];
            }
        }

        return $contactData;
    }

    /**
     * Подготавливает данные лида для AmoCRM
     *
     * @param array $contacts Детектированные контактные данные
     * @param array|null $analyticsData Аналитические данные
     * @param array $amoCrmSettings Настройки AmoCRM
     * @return array
     */
    private function prepareLeadData(array $contacts, ?array $analyticsData = null, array $amoCrmSettings = []): array
    {
        $leadData = [
            'name' => 'Лид из чата',
            'price' => 0,
            'custom_fields' => []
        ];

        // Формируем название лида на основе контактных данных
        if (!empty($contacts['phones'])) {
            $leadData['name'] = 'Лид из чата: ' . $contacts['phones'][0];
        } elseif (!empty($contacts['emails'])) {
            $leadData['name'] = 'Лид из чата: ' . $contacts['emails'][0];
        }

        // Добавляем ClientID Яндекс.Метрики в кастомное поле
        if ($analyticsData && !empty($analyticsData['clientId']) && !empty($amoCrmSettings['client_id_field_id'])) {
            $leadData['custom_fields'][$amoCrmSettings['client_id_field_id']] = $analyticsData['clientId'];
        }

        return $leadData;
    }

    /**
     * Подготавливает текст примечания с историей чата и аналитическими данными
     *
     * @param array $chatHistory История чата
     * @param array|null $analyticsData Аналитические данные
     * @return string
     */
    private function prepareChatHistoryNote(array $chatHistory, ?array $analyticsData = null): string
    {
        $note = "=== ИСТОРИЯ ПЕРЕПИСКИ ===\n\n";

        // Добавляем историю чата
        foreach ($chatHistory as $message) {
            $sender = $message['sender'] === 'user' ? 'Пользователь' : 'Ассистент';
            $content = $message['content'] ?? $message['text'] ?? '';
            $timestamp = $message['created_at'] ?? date('Y-m-d H:i:s');
            
            $note .= "[$timestamp] $sender: $content\n\n";
        }

        // Добавляем аналитические данные
        if ($analyticsData) {
            $note .= "=== АНАЛИТИЧЕСКИЕ ДАННЫЕ ===\n\n";
            
            if (!empty($analyticsData['pageUrl'])) {
                $note .= "URL страницы: " . $analyticsData['pageUrl'] . "\n";
            }
            
            if (!empty($analyticsData['referrer'])) {
                $note .= "Источник перехода: " . $analyticsData['referrer'] . "\n";
            }
            
            if (!empty($analyticsData['clientId'])) {
                $note .= "ClientID Яндекс.Метрики: " . $analyticsData['clientId'] . "\n";
            }
            
            if (!empty($analyticsData['utmParams'])) {
                $note .= "\nUTM-метки:\n";
                foreach ($analyticsData['utmParams'] as $key => $value) {
                    $note .= "- $key: $value\n";
                }
            }
            
            if (!empty($analyticsData['sessionStart'])) {
                $note .= "\nНачало сессии: " . $analyticsData['sessionStart'] . "\n";
            }
        }

        return $note;
    }

    /**
     * Получает историю чата для сессии
     *
     * @param int $sessionId ID сессии
     * @return array
     */
    public function getChatHistory(int $sessionId): array
    {
        try {
            // Здесь должна быть логика получения истории чата из базы данных
            // Пока возвращаем пустой массив
            return [];
        } catch (\Exception $e) {
            error_log("CrmIntegrationService::getChatHistory - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Проверяет статус интеграции
     *
     * @return array
     */
    public function getIntegrationStatus(): array
    {
        return [
            'active' => $this->crmSettings->isActive(),
            'amocrm_configured' => $this->crmSettings->getAmoCrmSettings() !== null,
            'yandex_metrika_configured' => $this->crmSettings->getYandexMetrikaSettings() !== null
        ];
    }
}
