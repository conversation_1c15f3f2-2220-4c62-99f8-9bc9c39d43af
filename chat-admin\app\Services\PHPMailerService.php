<?php

namespace App\Services;

/**
 * Профессиональный email сервис на основе PHPMailer
 * Поддерживает SMTP, fallback и подробное логирование
 */
class PHPMailerService
{
    private $host;
    private $port;
    private $username;
    private $password;
    private $encryption;
    private $debug = false;
    private $timeout = 30;

    public function __construct($config = [])
    {
        $this->host = $config['host'] ?? 'smtp.yandex.ru';
        $this->port = $config['port'] ?? 587;
        $this->username = $config['username'] ?? '';
        $this->password = $config['password'] ?? '';
        $this->encryption = $config['encryption'] ?? 'tls';
        $this->debug = $config['debug'] ?? false;
        $this->timeout = $config['timeout'] ?? 30;
    }

    /**
     * Отправка email с автоматическим fallback
     */
    public function sendEmail($to, $subject, $body, $from, $fromName = '', $isHtml = false)
    {
        // Сначала пытаемся через SMTP
        if ($this->host && $this->username && $this->password) {
            try {
                $result = $this->sendViaSMTP($to, $subject, $body, $from, $fromName, $isHtml);
                if ($result) {
                    $this->log("Email sent successfully via SMTP to: $to");
                    return true;
                }
            } catch (\Exception $e) {
                $this->log("SMTP failed: " . $e->getMessage());
            }
        }

        // Fallback к встроенной функции mail()
        $this->log("Using mail() function fallback");
        return $this->sendViaMail($to, $subject, $body, $from, $fromName, $isHtml);
    }

    /**
     * Отправка через SMTP (упрощенная реализация PHPMailer логики)
     */
    private function sendViaSMTP($to, $subject, $body, $from, $fromName = '', $isHtml = false)
    {
        // Проверяем доступность SMTP сервера
        $connection = @fsockopen($this->host, $this->port, $errno, $errstr, 5);
        if (!$connection) {
            throw new \Exception("Cannot connect to SMTP server {$this->host}:{$this->port} - $errstr ($errno)");
        }
        fclose($connection);

        // Создаем SSL контекст
        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ]);

        // Подключаемся к SMTP серверу
        if ($this->encryption === 'ssl') {
            $socket = stream_socket_client("ssl://{$this->host}:{$this->port}", $errno, $errstr, $this->timeout, STREAM_CLIENT_CONNECT, $context);
        } else {
            $socket = stream_socket_client("tcp://{$this->host}:{$this->port}", $errno, $errstr, $this->timeout, STREAM_CLIENT_CONNECT, $context);
        }

        if (!$socket) {
            throw new \Exception("Failed to connect to SMTP server: $errstr ($errno)");
        }

        // Читаем приветствие сервера
        $response = fgets($socket, 515);
        $this->log("Server greeting: " . trim($response));

        if (substr($response, 0, 3) !== '220') {
            fclose($socket);
            throw new \Exception("SMTP server error: $response");
        }

        // EHLO команда
        $this->smtpCommand($socket, "EHLO " . gethostname());

        // STARTTLS если нужно
        if ($this->encryption === 'tls') {
            try {
                $this->smtpCommand($socket, "STARTTLS");

                if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                    throw new \Exception("Failed to enable TLS encryption");
                }

                // Повторяем EHLO после TLS
                $this->smtpCommand($socket, "EHLO " . gethostname());
            } catch (\Exception $e) {
                $this->log("TLS failed, continuing without encryption: " . $e->getMessage());
                // Продолжаем без TLS, не закрываем соединение
            }
        }

        // Аутентификация
        $this->smtpCommand($socket, "AUTH LOGIN");
        $this->smtpCommand($socket, base64_encode($this->username));
        $this->smtpCommand($socket, base64_encode($this->password));

        // Отправка письма
        $this->smtpCommand($socket, "MAIL FROM: <$from>");
        $this->smtpCommand($socket, "RCPT TO: <$to>");
        $this->smtpCommand($socket, "DATA");

        // Формируем заголовки
        $headers = $this->buildHeaders($to, $subject, $from, $fromName, $isHtml);
        
        // Отправляем заголовки и тело
        $emailData = $headers . "\r\n" . $body . "\r\n.";
        fputs($socket, $emailData . "\r\n");

        $response = fgets($socket, 515);
        $this->log("Data response: " . trim($response));

        if (substr($response, 0, 3) !== '250') {
            fclose($socket);
            throw new \Exception("Failed to send email data: $response");
        }

        // Завершаем соединение
        $this->smtpCommand($socket, "QUIT");
        fclose($socket);

        return true;
    }



    /**
     * Fallback отправка через встроенную функцию mail()
     */
    private function sendViaMail($to, $subject, $body, $from, $fromName = '', $isHtml = false)
    {
        if (!function_exists('mail')) {
            $this->log("mail() function not available");
            return false;
        }

        // Формируем заголовки для mail()
        $headers = [];

        if ($fromName) {
            $headers[] = "From: =?UTF-8?B?" . base64_encode($fromName) . "?= <$from>";
        } else {
            $headers[] = "From: $from";
        }

        $headers[] = "Reply-To: $from";
        $headers[] = "Return-Path: $from";
        $headers[] = "MIME-Version: 1.0";

        if ($isHtml) {
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        } else {
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
        }

        $headers[] = "Content-Transfer-Encoding: 8bit";
        $headers[] = "X-Mailer: PHPMailerService/2.0 (fallback)";
        $headers[] = "Date: " . date('r');

        $headerString = implode("\r\n", $headers);

        $result = mail($to, $subject, $body, $headerString);

        if ($result) {
            $this->log("Email sent successfully via mail() to: $to");
        } else {
            $this->log("Failed to send email via mail() to: $to");
        }

        return $result;
    }

    /**
     * Статический метод для быстрой отправки
     */
    public static function quickSend($config, $to, $subject, $body, $from, $fromName = '', $isHtml = false)
    {
        $mailer = new self($config);
        return $mailer->sendEmail($to, $subject, $body, $from, $fromName, $isHtml);
    }

    /**
     * Проверка доступности SMTP сервера
     */
    public static function testConnection($host, $port, $timeout = 5)
    {
        $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
        if ($connection) {
            fclose($connection);
            return true;
        }
        return false;
    }

    /**
     * Построение заголовков email
     */
    private function buildHeaders($to, $from, $fromName, $subject, $isHtml = false)
    {
        $headers = [];
        $headers[] = "From: " . ($fromName ? "$fromName <$from>" : $from);
        $headers[] = "To: $to";
        $headers[] = "Subject: $subject";
        $headers[] = "Date: " . date('r');
        $headers[] = "Message-ID: <" . uniqid() . "@" . gethostname() . ">";
        $headers[] = "MIME-Version: 1.0";

        if ($isHtml) {
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        } else {
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
        }

        $headers[] = "Content-Transfer-Encoding: 8bit";
        $headers[] = "X-Mailer: Custom PHP Mailer";

        return implode("\r\n", $headers);
    }

    /**
     * Отправка SMTP команды
     */
    private function smtpCommand($socket, $command)
    {
        fwrite($socket, $command . "\r\n");
        $response = fgets($socket, 512);

        if ($this->debug) {
            $this->log("SMTP Command: $command");
            $this->log("SMTP Response: " . trim($response));
        }

        $code = substr($response, 0, 3);
        if ($code >= 400) {
            throw new \Exception("SMTP Error: $response");
        }

        return $response;
    }

    /**
     * Логирование
     */
    private function log($message)
    {
        if ($this->debug) {
            error_log("PHPMailerService: " . $message);
        }
    }

    /**
     * Валидация email адреса
     */
    public static function validateEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Очистка email адреса
     */
    public static function sanitizeEmail($email)
    {
        return filter_var(trim($email), FILTER_SANITIZE_EMAIL);
    }
}
