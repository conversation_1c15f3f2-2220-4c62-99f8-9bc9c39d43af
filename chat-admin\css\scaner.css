:root {
    --primary: #4361ee;
    --primary-dark: #3a56d4;
    --secondary: #3f37c9;
    --text: #2b2d42;
    --text-light: #8d99ae;
    --bg: #f8f9fa;
    --card: #ffffff;
    --error: #ef233c;
    --success: #4cc9f0;
    --border: rgba(0,0,0,0.1);
    --shadow: 0 4px 20px rgba(0,0,0,0.08);
}

[data-theme="dark"] {
    --primary: #4895ef;
    --primary-dark: #3a7bc8;
    --secondary: #560bad;
    --text: #f8f9fa;
    --text-light: #adb5bd;
    --bg: #121212;
    --card: #1e1e1e;
    --error: #f72585;
    --success: #4cc9f0;
    --border: rgba(255,255,255,0.1);
    --shadow: 0 4px 20px rgba(0,0,0,0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    transition: background-color 0.3s, color 0.2s;
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    background-color: var(--bg);
    color: var(--text);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    line-height: 1.6;
}

header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.25rem;
}

.logo-icon {
    color: var(--primary);
    font-size: 1.5rem;
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background-color: var(--border);
}

main {
    flex: 1;
    padding: 2rem 1.5rem;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

.hero {
    text-align: center;
    margin-bottom: 2.5rem;
    animation: fadeIn 0.5s ease-out;
}

.hero h1 {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    font-weight: 700;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero p {
    color: var(--text-light);
    max-width: 500px;
    margin: 0 auto;
}

.scanner-container {
    position: relative;
    margin: 0 auto 2rem;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow);
    max-width: 500px;
    aspect-ratio: 1;
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

#video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: none;
}

.scanner-overlay {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
    background-color: rgba(0,0,0,0.7);
    color: white;
    opacity: 1;
    transition: opacity 0.3s;
}

.scanner-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.scanner-icon {
    font-size: 3rem;
    color: var(--primary);
}

.scanner-frame {
    position: absolute;
    width: 70%;
    height: 70%;
    border: 3px solid var(--primary);
    border-radius: 1rem;
    box-shadow: 0 0 0 100vmax rgba(0,0,0,0.7);
    animation: pulse 2s infinite;
}

#canvas {
    display: none;
}

.controls {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 0.875rem 2rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
    transition: all 0.2s;
}

.btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(67, 97, 238, 0.4);
}

.btn:active {
    transform: translateY(0);
}

.btn i {
    font-size: 1.1rem;
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary);
    border: 1px solid var(--primary);
    box-shadow: none;
}

.btn-secondary:hover {
    background-color: rgba(67, 97, 238, 0.1);
}

.result-container {
    background-color: var(--card);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    margin-top: 2rem;
    display: none;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border);
}

.result-title {
    font-weight: 600;
    font-size: 1.25rem;
}

.result-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    background-color: var(--success);
    color: white;
}

.result-status.error {
    background-color: var(--error);
}

.result-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.result-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.result-label {
    font-size: 0.875rem;
    color: var(--text-light);
}

.result-value {
    font-weight: 500;
    word-break: break-word;
}

.loading {
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    text-align: center;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(67, 97, 238, 0.2);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s linear infinite;
}

.flash {
    position: fixed;
    inset: 0;
    background-color: white;
    opacity: 0;
    pointer-events: none;
    z-index: 1000;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { opacity: 0.8; }
    50% { opacity: 0.4; }
    100% { opacity: 0.8; }
}

footer {
    text-align: center;
    padding: 1.5rem;
    color: var(--text-light);
    font-size: 0.875rem;
    border-top: 1px solid var(--border);
}

@media (max-width: 600px) {
    .hero h1 {
        font-size: 1.75rem;
    }
    
    .result-content {
        grid-template-columns: 1fr;
    }
}