<?php

namespace App\Controllers;

use App\Models\User;
use App\Models\DashboardStats;
use App\Models\Database;

class DashboardController extends BaseController {
    protected $db;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->checkAuth(); // Проверяем авторизацию для доступа к дашборду
    }

    public function index() {
        $userId = $this->getUserId(); // Получаем ID текущего пользователя
        $userModel = new User($this->db); // Создаем экземпляр модели User
        $user = $userModel->getUserById($userId); // Получаем данные пользователя по ID

        $stats = new DashboardStats($this->db);
        $data = [
            'user' => $user, // Передаем данные пользователя в представление
            'totalMessages' => $stats->getTotalMessagesCount(),
            'totalChats' => $stats->getTotalChatsCount(),
            'totalUsers' => $stats->getTotalUsersCount(),
            'totalContextFiles' => $stats->getTotalContextFilesCount(),
            'avgMessagesPerChat' => round($stats->getAverageMessagesPerChat(), 1),
            'monthlyTrend' => $stats->getMessagesMonthlyTrend(),
            'topUsers' => $stats->getTopActiveUsers(5),
            'currentPage' => 'dashboard'
        ];
        $this->loadView('dashboard', $data);
    }
}
