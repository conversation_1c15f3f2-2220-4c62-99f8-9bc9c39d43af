<?php 
$pageTitle = 'Просмотр переписки';
$currentUser = $currentUser ?? ['name' => 'Администратор', 'email' => '<EMAIL>'];
require __DIR__ . '/../partials/header.php'; 
?>

<style>
:root {
    --sidebar-width: 260px;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main-layout {
    display: flex;
    min-height: calc(100vh - 60px);
}

.sidebar {
    width: var(--sidebar-width);
    background: #f8f9fa;
    border-right: 1px solid #e0e6ed;
    padding: 20px;
    position: fixed;
    height: calc(100vh - 60px);
    overflow-y: auto;
}

.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 20px;
    max-width: calc(100% - var(--sidebar-width));
}

.chat-header {
    background: var(--primary-gradient);
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.chat-container {
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f9fafb;
    height: auto;
    border-radius: 0 0 8px 8px;
}

.message-row {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.message-row.user-message {
    border-left: 4px solid #5c6bc0;
}

.message-row.assistant-message {
    border-left: 4px solid #4caf50;
}

.message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    align-items: center;
}

.message-date {
    color: #78909c;
    font-size: 0.9em;
}

.message-content {
    line-height: 1.5;
    word-wrap: break-word;
}


.badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.badge-user {
    background: rgba(92, 107, 192, 0.15);
    color: #5c6bc0;
    border: 1px solid rgba(92, 107, 192, 0.3);
}

.badge-assistant {
    background: rgba(76, 175, 80, 0.15);
    color: #4caf50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.message-content {
    line-height: 1.5;
    word-wrap: break-word;
}

.phone-badge {
    background: rgba(102, 126, 234, 0.15);
    color: #667eea;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    margin: 0 4px 8px 0;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    border: 1px solid rgba(102, 126, 234, 0.3);
}

.phone-badge i {
    color: #667eea;
}

.session-info-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.info-label {
    font-weight: 600;
    color: #546e7a;
}

.info-value {
    text-align: right;
}

.action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.btn-action {
    flex: 1;
    text-align: center;
    padding: 8px 10px;
    border-radius: 8px;
    font-size: 0.85rem;
}

.btn-delete {
    background: #ffebee;
    color: #f44336;
    border: 1px solid #ffcdd2;
}

.btn-delete:hover {
    background: #ffcdd2;
}

.btn-back {
    background: #e3f2fd;
    color: #2196f3;
    border: 1px solid #bbdefb;
}

.btn-back:hover {
    background: #bbdefb;
}

.no-messages {
    text-align: center;
    padding: 40px 20px;
    color: #78909c;
}

.no-messages i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #cfd8dc;
}

/* Стили для детального представления цифровых данных */
.digital-data-detailed {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.data-section {
    background: white;
    border-radius: 6px;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

.data-section h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.data-items {
    max-height: 200px;
    overflow-y: auto;
}

.data-item {
    padding: 0.25rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.data-item:last-child {
    border-bottom: none;
}

.data-item .badge {
    font-size: 0.8em;
    font-weight: 500;
}

.data-item small {
    display: block;
    margin-top: 0.25rem;
    font-style: italic;
}
</style>

<div class="main-layout">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="session-info-card">
            <h4 class="mb-4"><i class="fas fa-info-circle me-2"></i>Информация о сессии</h4>
            
            <div class="info-row">
                <span class="info-label">ID сессии:</span>
                <span class="info-value"><?= $session['id'] ?? 'N/A' ?></span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Название:</span>
                <span class="info-value"><?= htmlspecialchars($session['title'] ?? 'Без названия') ?></span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Сообщений:</span>
                <span class="info-value"><?= count($messages) ?></span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Создана:</span>
                <span class="info-value">
                    <?= isset($session['created_at']) ? date('d.m.Y H:i', strtotime($session['created_at'])) : 'N/A' ?>
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Обновлена:</span>
                <span class="info-value">
                    <?= isset($session['updated_at']) ? date('d.m.Y H:i', strtotime($session['updated_at'])) : 'N/A' ?>
                </span>
            </div>
            
            <h5 class="mt-4 mb-3"><i class="fas fa-database me-2"></i>Цифровые данные:</h5>
            <div class="digital-data-detailed">
                <?php if (!empty($phones)): ?>
                    <div class="data-section mb-3">
                        <h6 class="text-success mb-2">
                            <i class="fas fa-phone me-1"></i>Телефоны (<?= count($phones) ?>):
                        </h6>
                        <div class="phones-container">
                            <?php foreach ($phones as $phone): ?>
                                <span class="phone-badge">
                                    <i class="fas fa-phone"></i><?= htmlspecialchars($phone) ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($digitalData) && is_array($digitalData)): ?>
                    <?php
                    $dataByType = [];
                    foreach ($digitalData as $data) {
                        if (is_array($data) && isset($data['type']) && $data['type'] !== 'phones') { // Телефоны уже показаны выше
                            $dataByType[$data['type']][] = $data;
                        }
                    }
                    ?>

                    <?php foreach ($dataByType as $type => $items): ?>
                        <div class="data-section mb-3">
                            <h6 class="text-primary mb-2">
                                <i class="fas fa-<?= $controller->getIconForType($type) ?> me-1"></i>
                                <?= $controller->getTypeLabel($type) ?> (<?= count($items) ?>):
                            </h6>
                            <div class="data-items">
                                <?php foreach ($items as $item): ?>
                                    <div class="data-item mb-2">
                                        <span class="badge bg-light text-dark me-2">
                                            <?= htmlspecialchars($item['value']) ?>
                                        </span>
                                        <?php if (!empty($item['context'])): ?>
                                            <small class="text-muted">
                                                Контекст: "<?= htmlspecialchars(mb_substr($item['context'], 0, 50)) ?><?= mb_strlen($item['context']) > 50 ? '...' : '' ?>"
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>

                <?php if (empty($phones) && empty($digitalData)): ?>
                    <div class="text-muted">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Цифровые данные не найдены
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="action-buttons">
                <a href="index.php?page=phone_chats" class="btn-action btn-back">
                    <i class="fas fa-arrow-left me-1"></i> Назад
                </a>
                <button class="btn-action btn-delete delete-chat-btn" data-session-id="<?= $session['id'] ?? 0 ?>">
                    <i class="fas fa-trash me-1"></i> Удалить
                </button>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="chat-header">
            <h3><i class="fas fa-comments me-2"></i><?= htmlspecialchars($session['title'] ?? 'Переписка') ?></h3>
            <p class="mb-0">ID: <?= $session['id'] ?? 'N/A' ?> | Сообщений: <?= count($messages) ?></p>
        </div>
        
        <div class="chat-container">
            <div class="messages-container">
                <?php if (is_array($messages) && count($messages) > 0): ?>
                    <?php foreach ($messages as $message): ?>
                        <div class="message-row <?= $message['sender'] === 'user' ? 'user-message' : 'assistant-message' ?>">
                            <div class="message-header">
                                <div class="message-date">
                                    <?= isset($message['timestamp']) ? date('d.m.Y H:i', strtotime($message['timestamp'])) : '' ?>
                                </div>
                                <div class="message-sender">
                                    <span class="badge <?= $message['sender'] === 'user' ? 'badge-user' : 'badge-assistant' ?>">
                                        <?= $message['sender'] === 'user' ? 'Пользователь' : 'Ассистент' ?>
                                    </span>
                                </div>
                            </div>
                            <div class="message-content">
                                <?= nl2br(htmlspecialchars($message['content'])) ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="no-messages">
                        <i class="fas fa-comment-slash"></i>
                        <h4>Сообщения не найдены</h4>
                        <p>Возможные причины:</p>
                        <ul>
                            <li>Сессия не содержит сообщений</li>
                            <li>Ошибка при загрузке из базы данных</li>
                            <li>Проблемы с SQL запросом</li>
                        </ul>
                        <p class="mt-3">ID сессии: <?= $session['id'] ?? 'N/A' ?></p>
                        <!-- Debug info -->
                        <p>Тип messages: <?= gettype($messages) ?></p>
                        <?php if (is_array($messages)): ?>
                            <p>Количество: <?= count($messages) ?></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Удаление переписки
document.querySelector('.delete-chat-btn').addEventListener('click', function() {
    const sessionId = this.dataset.sessionId;
    if (confirm('Удалить эту переписку? Это действие нельзя отменить.')) {
        // Показываем индикатор загрузки
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        this.disabled = true;
        
        fetch('index.php?page=phone_chats&action=deleteChat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `session_id=${sessionId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Переписка успешно удалена', 'success');
                setTimeout(() => {
                    window.location.href = 'index.php?page=phone_chats';
                }, 1500);
            } else {
                // Восстанавливаем кнопку
                this.innerHTML = '<i class="fas fa-trash me-1"></i> Удалить';
                this.disabled = false;
                showNotification('Ошибка: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Восстанавливаем кнопку
            this.innerHTML = '<i class="fas fa-trash me-1"></i> Удалить';
            this.disabled = false;
            showNotification('Произошла ошибка при удалении', 'error');
        });
    }
});

// Функция для показа уведомлений
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Показываем уведомление
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Скрываем уведомление через 3 секунды
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>

<?php require __DIR__ . '/../partials/footer.php'; ?>
