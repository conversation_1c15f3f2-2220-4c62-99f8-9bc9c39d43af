{"timestamp": "2025-07-27 19:06:56", "phone": "+7 (900) 123-45-67", "session_id": "test_utm_session_1753643216", "chat_history": [{"sender": "assistant", "text": "Здравствуйте! Я помощник адвоката. Чем могу помочь?", "timestamp": "2025-07-27 19:01:56"}, {"sender": "user", "text": "Мне нужна консультация по семейному праву", "timestamp": "2025-07-27 19:03:36"}, {"sender": "assistant", "text": "Конечно! Расскажите подробнее о вашей ситуации. Также оставьте ваши контактные данные для связи.", "timestamp": "2025-07-27 19:05:16"}, {"sender": "user", "text": "Привет! Меня зовут Иван Петров. Мой телефон +7 (900) 123-45-67, свяжитесь со мной пожалуйста.", "timestamp": "2025-07-27 19:06:56"}], "analytics_data": {"clientId": "test_client_1753643216", "pageUrl": "https://example.com/landing-page?utm_source=google&utm_medium=cpc&utm_campaign=test_campaign&utm_term=адвокат&utm_content=banner1", "referrer": "https://google.com/search?q=адвокат+москва", "utmParams": {"utm_source": "google", "utm_medium": "cpc", "utm_campaign": "test_campaign", "utm_term": "адвокат", "utm_content": "banner1"}, "sessionStart": "2025-07-27 19:06:56"}, "utm_summary": {"source": "google", "medium": "cpc", "campaign": "test_campaign", "term": "адвокат", "content": "banner1"}}