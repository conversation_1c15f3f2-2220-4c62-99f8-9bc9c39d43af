<?php

namespace App\Controllers;

class InitTextsController extends BaseController
{
    // Флаг для использования шаблона
    protected $useTemplate = true;

    public function index()
    {
        // Проверяем, авторизован ли пользователь как админ
        $this->requireAdmin();

        // Отображаем страницу инициализации текстов
        $this->render('init_texts', [
            'title' => 'Инициализация таблицы текстов',
            'active_menu' => 'init_texts'
        ]);
    }

    /**
     * Отображает представление с данными.
     *
     * @param string $view Имя файла представления (без .php)
     * @param array $data Данные для передачи в представление
     */
    protected function render(string $view, array $data = []): void
    {
        // Добавляем данные пользователя, если он авторизован
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (isset($_SESSION['user_id'])) {
            // Здесь можно добавить получение данных пользователя из базы данных
            $data['user'] = [
                'username' => $_SESSION['username'] ?? 'Admin',
                'email' => $_SESSION['email'] ?? ''
            ];
        }

        // Добавляем имя текущей страницы для активного пункта меню
        $data['pageName'] = $data['active_menu'] ?? '';

        if ($this->useTemplate) {
            // Загружаем шапку
            $this->loadView('partials/header', $data);
        }

        // Загружаем основное представление
        $this->loadView($view, $data);

        if ($this->useTemplate) {
            // Загружаем подвал
            $this->loadView('partials/footer', $data);
        }
    }

    public function process()
    {
        // Проверяем, авторизован ли пользователь как админ
        $this->requireAdmin();

        // Отключаем шаблон, так как мы будем выводить только результат инициализации
        $this->useTemplate = false;

        // Запускаем скрипт инициализации таблицы текстов
        require_once __DIR__ . '/../Database/init_texts_table.php';
    }
}
