<?php

namespace App\Services;

use App\Models\Settings;
use App\Models\Message;
use App\Services\SmtpService;
use App\Services\CrmIntegrationService;

// Подключаем автозагрузчик Composer для PHPMailer
require_once __DIR__ . '/../../vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class PhoneNotificationService
{
    private $settingsModel;
    private $messageModel;
    private $processedNotifications = []; // Кэш для дедупликации

    public function __construct()
    {
        $this->settingsModel = new Settings();
        $this->messageModel = new Message();
    }

    /**
     * Проверяет сообщение на наличие телефона и отправляет уведомление
     */
    public function checkAndNotify($sessionId, $messageContent, $userId = null, $analyticsData = null)
    {
        error_log("PhoneNotificationService: Checking message for phone: " . substr($messageContent, 0, 100));

        // Проверяем, есть ли телефон в сообщении
        $phone = $this->extractPhone($messageContent);

        if (!$phone) {
            error_log("PhoneNotificationService: No phone found in message");
            return false; // Телефон не найден
        }

        error_log("PhoneNotificationService: Phone found: $phone");

        // Проверяем дедупликацию - создаем уникальный ключ
        $deduplicationKey = md5($sessionId . '_' . $phone);

        // if (isset($this->processedNotifications[$deduplicationKey])) {
        //     error_log("PhoneNotificationService: Notification already sent for session $sessionId and phone $phone - skipping");
        //     return false; // Уведомление уже отправлено
        // }

        // Отмечаем как обработанное
        $this->processedNotifications[$deduplicationKey] = time();

        // Получаем настройки email
        $settings = $this->settingsModel->getSettings();
        $notificationEmail = $settings['notification_email'] ?? '';
        $emailFrom = $settings['email_from'] ?? '<EMAIL>';
        $emailSubject = $settings['email_subject'] ?? 'Новый телефон из чата: {phone}';
        $emailBody = $settings['email_body'] ?? 'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}';

        error_log("PhoneNotificationService: Settings loaded, email: " . ($notificationEmail ?: 'NOT SET'));

        if (empty($notificationEmail)) {
            error_log("PhoneNotificationService: Notification email not configured");
            return false;
        }

        // Получаем полную историю чата
        $chatHistory = $this->getChatHistory($sessionId);

        // Отправляем уведомление
        $emailResult = $this->sendNotification($notificationEmail, $phone, $chatHistory, $sessionId, $emailFrom, $emailSubject, $emailBody, $analyticsData);

        // Отправляем в CRM, если интеграция включена
        $crmSuccess = false;
        try {
            $crmService = new CrmIntegrationService();

            // Получаем массив сообщений для CRM (не форматированную строку)
            // Преобразуем sessionId в int, если это возможно
            $sessionIdInt = is_numeric($sessionId) ? (int)$sessionId : 0;
            $chatHistoryArray = $sessionIdInt > 0 ? $this->messageModel->getContextMessages($sessionIdInt, 9999) : [];

            // Декодируем аналитические данные если они переданы как JSON строка
            $analyticsArray = null;
            if ($analyticsData) {
                if (is_string($analyticsData)) {
                    $analyticsArray = json_decode($analyticsData, true);
                } else {
                    $analyticsArray = $analyticsData;
                }
            }

            // Правильный порядок параметров: message, chatHistory, analyticsData
            $crmResult = $crmService->processMessage($messageContent, $chatHistoryArray, $analyticsArray);
            $crmSuccess = $crmResult['success'] && $crmResult['lead_created'];
            error_log("PhoneNotificationService: CRM integration result: " . json_encode($crmResult));
        } catch (\Exception $e) {
            error_log("PhoneNotificationService: CRM integration failed: " . $e->getMessage());
        }

        // Считаем успешным, если хотя бы одно из действий выполнено (email отправлен ИЛИ лид создан)
        $overallSuccess = $emailResult || $crmSuccess;
        error_log("PhoneNotificationService: Overall result - Email: " . ($emailResult ? 'success' : 'failed') . ", CRM: " . ($crmSuccess ? 'success' : 'failed') . ", Overall: " . ($overallSuccess ? 'success' : 'failed'));

        return $overallSuccess;
    }

    /**
     * Извлекает телефон из текста сообщения
     */
    private function extractPhone($text)
    {
        // Паттерны для поиска телефонов
        $patterns = [
            '/\+7[\s\-\(\)]?[\d\s\-\(\)]{10,}/',           // +7 xxx xxx xxxx
            '/8[\s\-\(\)]?[\d\s\-\(\)]{10,}/',             // 8 xxx xxx xxxx
            '/7[\s\-\(\)]?[\d\s\-\(\)]{10,}/',             // 7 xxx xxx xxxx
            '/\+7[\d]{10}/',                                // +7xxxxxxxxxx
            '/8[\d]{10}/',                                  // 8xxxxxxxxxx
            '/[\+]?[7-8]?[\s\-\(\)]?\d{3}[\s\-\(\)]?\d{3}[\s\-\(\)]?\d{2}[\s\-\(\)]?\d{2}/', // Общий паттерн
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                // Очищаем найденный телефон от лишних символов
                $phone = preg_replace('/[^\d\+]/', '', $matches[0]);
                
                // Проверяем длину (должно быть 10-11 цифр + возможный +)
                if (strlen($phone) >= 10 && strlen($phone) <= 12) {
                    return $matches[0]; // Возвращаем оригинальный формат
                }
            }
        }

        return null;
    }

    /**
     * Получает полную историю чата с красивым форматированием
     */
    private function getChatHistory($sessionId)
    {
        try {
            // Преобразуем sessionId в int, если это возможно
            $sessionIdInt = is_numeric($sessionId) ? (int)$sessionId : 0;
            $messages = $sessionIdInt > 0 ? $this->messageModel->getContextMessages($sessionIdInt, 9999) : [];

            $history = "=== ПОЛНАЯ ПЕРЕПИСКА ЧАТА ===\n";
            $history .= "ID чата: $sessionId\n";
            $history .= "Дата экспорта: " . date('d.m.Y H:i:s') . "\n";
            $history .= "Количество сообщений: " . count($messages) . "\n";
            $history .= str_repeat('=', 60) . "\n\n";

            if (empty($messages)) {
                $history .= "Сообщений в чате не найдено.\n\n";
                return $history;
            }

            $messageCount = 1;
            foreach ($messages as $message) {
                $role = $message['role'] === 'user' ? '👤 ПОЛЬЗОВАТЕЛЬ' : '🤖 АССИСТЕНТ';
                $timestamp = $message['created_at'] ?? 'Неизвестно';
                $content = $message['content'] ?? '';

                // Форматируем время
                $formattedTime = date('d.m.Y H:i:s', strtotime($timestamp));

                $history .= "#{$messageCount} {$role}\n";
                $history .= "Время: {$formattedTime}\n";
                $history .= str_repeat('-', 40) . "\n";
                $history .= "{$content}\n";
                $history .= str_repeat('-', 40) . "\n\n";

                $messageCount++;
            }

            $history .= str_repeat('=', 60) . "\n";
            $history .= "Конец переписки\n";

            return $history;

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error getting chat history: " . $e->getMessage());
            return "❌ ОШИБКА ПОЛУЧЕНИЯ ИСТОРИИ ЧАТА: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * Отправляет email уведомление с fallback для OpenServer
     */
    private function sendNotification($email, $phone, $chatHistory, $sessionId, $emailFrom = '<EMAIL>', $emailSubject = 'Новый телефон из чата: {phone}', $emailBody = 'Пользователь указал телефон в чате.\n\nТелефон: {phone}\nID чата: {session_id}\nВремя: {datetime}', $analyticsData = null)
    {
        // Логируем аналитические данные для отладки
        error_log("PhoneNotificationService::sendNotification - Analytics data received: " . ($analyticsData ? json_encode($analyticsData) : 'NULL'));

        try {
            // Подставляем переменные в тему письма
            $subject = str_replace(['{phone}', '{session_id}', '{datetime}', '{email}'],
                                 [$phone, $sessionId, date('Y-m-d H:i:s'), $email],
                                 $emailSubject);

            // Подставляем переменные в текст письма
            $body = str_replace(['{phone}', '{session_id}', '{datetime}', '{email}'],
                              [$phone, $sessionId, date('Y-m-d H:i:s'), $email],
                              $emailBody);

            // Добавляем UTM метки, если они есть
            if ($analyticsData && is_string($analyticsData)) {
                try {
                    $analytics = json_decode($analyticsData, true);
                    if ($analytics && isset($analytics['utmParams']) && !empty($analytics['utmParams'])) {
                        $body .= "\n\n=== UTM МЕТКИ ===\n";
                        foreach ($analytics['utmParams'] as $key => $value) {
                            $body .= "$key: $value\n";
                        }

                        // Добавляем дополнительные аналитические данные
                        if (isset($analytics['clientId'])) {
                            $body .= "Client ID: " . $analytics['clientId'] . "\n";
                        }
                        if (isset($analytics['pageUrl'])) {
                            $body .= "Page URL: " . $analytics['pageUrl'] . "\n";
                        }
                        if (isset($analytics['referrer'])) {
                            $body .= "Referrer: " . $analytics['referrer'] . "\n";
                        }
                    }
                } catch (\Exception $e) {
                    error_log("PhoneNotificationService: Failed to parse analytics data: " . $e->getMessage());
                }
            } elseif ($analyticsData && is_array($analyticsData)) {
                // Если данные уже в виде массива
                if (isset($analyticsData['utmParams']) && !empty($analyticsData['utmParams'])) {
                    $body .= "\n\n=== UTM МЕТКИ ===\n";
                    foreach ($analyticsData['utmParams'] as $key => $value) {
                        $body .= "$key: $value\n";
                    }

                    // Добавляем дополнительные аналитические данные
                    if (isset($analyticsData['clientId'])) {
                        $body .= "Client ID: " . $analyticsData['clientId'] . "\n";
                    }
                    if (isset($analyticsData['pageUrl'])) {
                        $body .= "Page URL: " . $analyticsData['pageUrl'] . "\n";
                    }
                    if (isset($analyticsData['referrer'])) {
                        $body .= "Referrer: " . $analyticsData['referrer'] . "\n";
                    }
                }
            }

            $body .= "\n\n=== ИСТОРИЯ ЧАТА ===\n\n";
            $body .= $chatHistory;
            $body .= "\n\n---\n";
            $body .= "Автоматическое уведомление от чат-бота";

            // Для OpenServer - сохраняем в файл
            $notificationsDir = __DIR__ . '/../../notifications';
            error_log("PhoneNotificationService: Notifications dir: $notificationsDir");

            if (!is_dir($notificationsDir)) {
                error_log("PhoneNotificationService: Creating notifications directory");
                $created = mkdir($notificationsDir, 0755, true);
                error_log("PhoneNotificationService: Directory created: " . ($created ? 'YES' : 'NO'));
            } else {
                error_log("PhoneNotificationService: Notifications directory exists");
            }

            $filename = 'phone_notification_' . date('Y-m-d_H-i-s') . '_' . $sessionId . '.txt';
            $filepath = $notificationsDir . '/' . $filename;

            error_log("PhoneNotificationService: Saving notification to: $filepath");

            $fullContent = "SUBJECT: $subject\n";
            $fullContent .= "TO: $email\n";
            $fullContent .= "DATE: " . date('Y-m-d H:i:s') . "\n";
            $fullContent .= str_repeat('=', 50) . "\n\n";
            $fullContent .= $body;

            $result = file_put_contents($filepath, $fullContent);
            error_log("PhoneNotificationService: File write result: " . ($result !== false ? "SUCCESS ($result bytes)" : "FAILED"));

            if ($result !== false) {
                error_log("PhoneNotificationService: Notification saved to file: $filepath");

                // Отправляем email
                $emailSent = $this->sendEmail($email, $subject, $body, $emailFrom);

                if ($emailSent) {
                    error_log("PhoneNotificationService: Email sent successfully to $email");

                    // Автоматически удаляем файл уведомления после успешной отправки email
                    if (file_exists($filepath)) {
                        $deleted = unlink($filepath);
                        error_log("PhoneNotificationService: Notification file " . ($deleted ? "deleted" : "deletion failed") . ": $filepath");
                    }
                } else {
                    error_log("PhoneNotificationService: Email sending failed to $email");
                }

                return $emailSent; // Возвращаем результат отправки email
            } else {
                error_log("PhoneNotificationService: Failed to save notification to file");
                return false;
            }

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error sending notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Отправляет email используя PHPMailer с Yandex SMTP и fallback в EML файлы
     */
    private function sendEmail($to, $subject, $body, $from)
    {
        try {
            $settings = $this->settingsModel->getSettings();

            // Пытаемся отправить через PHPMailer с Yandex SMTP
            $smtpEnabled = (bool)($settings['smtp_enabled'] ?? false);

            if ($smtpEnabled && !empty($settings['smtp_host'])) {
                error_log("PhoneNotificationService: Attempting PHPMailer SMTP delivery");

                try {
                    $mail = new PHPMailer(true);

                    // Настройки SMTP для Yandex
                    $mail->isSMTP();
                    $mail->Host = $settings['smtp_host'];
                    $mail->SMTPAuth = true;
                    $mail->Username = $settings['smtp_username'];
                    $mail->Password = $settings['smtp_password'];
                    $mail->Port = (int)($settings['smtp_port'] ?? 465);

                    // Настройки шифрования
                    if ($settings['smtp_encryption'] === 'ssl') {
                        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
                    } else {
                        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                    }

                    // Отключаем проверку SSL сертификатов для обхода сетевых проблем
                    $mail->SMTPOptions = array(
                        'ssl' => array(
                            'verify_peer' => false,
                            'verify_peer_name' => false,
                            'allow_self_signed' => true
                        )
                    );

                    $mail->CharSet = 'UTF-8';

                    // Настройки письма
                    $mail->setFrom($settings['smtp_username'], 'AI Chat System');
                    $mail->addAddress($to);
                    $mail->Subject = $subject;
                    $mail->Body = $body;

                    // Пытаемся отправить
                    $result = $mail->send();

                    if ($result) {
                        error_log("PhoneNotificationService: Email sent successfully via PHPMailer SMTP");
                        return true;
                    }

                } catch (Exception $e) {
                    error_log("PhoneNotificationService: PHPMailer SMTP failed: " . $e->getMessage());
                    // Продолжаем к fallback
                } catch (\Exception $e) {
                    error_log("PhoneNotificationService: PHPMailer general error: " . $e->getMessage());
                    // Продолжаем к fallback
                }
            }

            // Fallback: создаем EML файл для ручной отправки
            error_log("PhoneNotificationService: SMTP failed, creating EML file as fallback");

            $emlFilename = 'email_notification_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.eml';
            $emlContent = "From: AI Chat System <{$settings['smtp_username']}>\n";
            $emlContent .= "To: $to\n";
            $emlContent .= "Subject: $subject\n";
            $emlContent .= "Date: " . date('r') . "\n";
            $emlContent .= "Content-Type: text/plain; charset=UTF-8\n\n";
            $emlContent .= $body;

            $emlResult = file_put_contents($emlFilename, $emlContent);

            if ($emlResult !== false) {
                error_log("PhoneNotificationService: Email saved as EML file: $emlFilename");
                return true;
            } else {
                error_log("PhoneNotificationService: Failed to create EML file");
                return false;
            }

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error in sendEmail: " . $e->getMessage());
            return $this->saveEmailToFile($to, $subject, $body, $from);
        }
    }

    /**
     * Обнаружение телефона в тексте (публичный метод для контроллера)
     */
    public function detectPhone($text)
    {
        return $this->extractPhone($text) !== null;
    }

    /**
     * Отправка уведомления о телефоне (для ручной отправки из админки)
     */
    public function sendPhoneNotification($phone, $chatHistory, $sessionId)
    {
        try {
            // Получаем настройки email из базы данных
            $settings = $this->settingsModel->getSettings();
            $notificationEmail = $settings['notification_email'] ?? '';
            $emailFrom = $settings['email_from'] ?? '<EMAIL>';

            // Если email не настроен, используем значения по умолчанию
            if (empty($notificationEmail)) {
                error_log("PhoneNotificationService: notification_email not configured, using defaults");
                $recipients = [
                    '<EMAIL>',
                    '<EMAIL>'
                ];
            } else {
                // Разбиваем email адреса по запятой, если их несколько
                $recipients = array_map('trim', explode(',', $notificationEmail));
            }

            $subject = '🚨 ОБНАРУЖЕН ТЕЛЕФОН В ЧАТЕ - ' . date('Y-m-d H:i:s');

            // Формируем тело письма
            $body = "=== УВЕДОМЛЕНИЕ О ТЕЛЕФОНЕ ===\n";
            $body .= "Время: " . date('Y-m-d H:i:s') . "\n";
            $body .= "ID сессии: $sessionId\n";
            $body .= "Телефон: $phone\n";
            $body .= "Email отправлен: " . (count($recipients) > 0 ? 'ДА' : 'НЕТ') . "\n\n";

            // Добавляем историю чата
            if (is_array($chatHistory)) {
                $body .= "=== ИСТОРИЯ ПЕРЕПИСКИ ===\n\n";
                foreach ($chatHistory as $index => $message) {
                    $sender = $message['sender'] === 'user' ? '👤 ПОЛЬЗОВАТЕЛЬ' : '🤖 АССИСТЕНТ';
                    $time = date('d.m.Y H:i:s', strtotime($message['created_at']));
                    $content = $message['content'];

                    $messageNumber = $index + 1;
                    $body .= "#{$messageNumber} $sender ($time)\n";
                    $body .= str_repeat('-', 40) . "\n";
                    $body .= "$content\n";
                    $body .= str_repeat('-', 40) . "\n\n";
                }
            } else {
                $body .= "История чата:\n$chatHistory\n\n";
            }

            $body .= "=== КОНЕЦ УВЕДОМЛЕНИЯ ===\n";

            $successCount = 0;

            // Отправляем на все настроенные адреса
            foreach ($recipients as $recipient) {
                // Используем стандартный метод отправки email
                $result = $this->sendEmail($recipient, $subject, $body, $emailFrom);

                if ($result) {
                    $successCount++;
                }
            }

            return $successCount > 0;

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error in sendPhoneNotification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Fallback: сохраняет email в файл (для OpenServer без SMTP)
     */
    private function saveEmailToFile($to, $subject, $body, $from)
    {
        try {
            $emailDir = dirname(__DIR__, 2) . '/email_notifications';

            // Создаем папку, если её нет
            if (!is_dir($emailDir)) {
                mkdir($emailDir, 0777, true);
            }

            $filename = 'email_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.txt';
            $filepath = $emailDir . '/' . $filename;

            $emailContent = "=== EMAIL УВЕДОМЛЕНИЕ ===\n";
            $emailContent .= "Дата: " . date('r') . "\n";
            $emailContent .= "От: $from\n";
            $emailContent .= "Кому: $to\n";
            $emailContent .= "Тема: $subject\n";
            $emailContent .= "Content-Type: text/plain; charset=UTF-8\n";
            $emailContent .= str_repeat('=', 50) . "\n\n";
            $emailContent .= $body;
            $emailContent .= "\n\n" . str_repeat('=', 50) . "\n";
            $emailContent .= "ПРИМЕЧАНИЕ: Это письмо сохранено в файл, так как SMTP недоступен.\n";
            $emailContent .= "Файл: $filepath\n";

            $result = file_put_contents($filepath, $emailContent);

            if ($result !== false) {
                error_log("PhoneNotificationService: Email saved to file: $filepath");
                return true;
            } else {
                error_log("PhoneNotificationService: Failed to save email to file: $filepath");
                return false;
            }

        } catch (\Exception $e) {
            error_log("PhoneNotificationService: Error saving email to file: " . $e->getMessage());
            return false;
        }
    }
}
