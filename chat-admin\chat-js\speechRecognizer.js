// speechRecognizer.js

export class SpeechRecognizer {
    constructor({ onStart, onResult, onError, onEnd }) {
        const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;
        if (!SpeechRecognitionAPI) {
            // Не используем isSupported здесь, так как конструктор может быть вызван до проверки
            console.error("SpeechRecognizer: Browser does not support Speech Recognition API.");
            throw new Error("Browser does not support Speech Recognition API.");
        }

        this.recognition = new SpeechRecognitionAPI();
        this.isListening = false;
        this._handleResult = onResult || (() => {});
        this._handleError = onError || (() => {});
        this._handleStart = onStart || (() => {});
        this._handleEnd = onEnd || (() => {});

        // Настройки распознавания
        this.recognition.lang = 'ru-RU'; // Убедись, что язык установлен правильно
        this.recognition.continuous = false; // Распознавание останавливается после паузы
        this.recognition.interimResults = true; // Получаем промежуточные результаты
        this.recognition.maxAlternatives = 1; // Достаточно одного варианта

        // Привязка обработчиков событий
        this.recognition.onstart = () => {
            console.log("SpeechRecognizer: Event 'start'.");
            this.isListening = true;
            this._handleStart();
        };

        this.recognition.onresult = (event) => {
            this._handleResult(event);
        };

        this.recognition.onerror = (event) => {
            // console.error(`SpeechRecognizer: Event 'error'. Code: ${event.error}`, event.message || '');
             // Запоминаем ошибку для передачи в onend (хотя браузеры могут делать это сами)
            this.lastError = event.error;
            const wasListening = this.isListening;
            this.isListening = false; // Сбрасываем флаг при ошибке
            this._handleError({ error: event.error, message: event.message });
            // onend будет вызван автоматически после onerror
        };

        this.recognition.onend = () => {
            const wasListening = this.isListening; // Запоминаем состояние *до* сброса
            console.log(`SpeechRecognizer: Event 'end'. Was listening: ${wasListening}. Last error code: ${this.lastError || 'none'}`);
            this.isListening = false;
            // Передаем последнюю зафиксированную ошибку в колбэк onEnd
            this._handleEnd({ error: this.lastError });
            this.lastError = null; // Сбрасываем последнюю ошибку
        };
    }

    // --- Статический метод для проверки поддержки API ---
    static isSupported() {
       const supported = !!(window.SpeechRecognition || window.webkitSpeechRecognition);
       console.log("SpeechRecognizer: Checking support... Supported:", supported);
       return supported;
    }
    // --- Конец статического метода ---

    start() {
        if (this.isListening) {
            console.warn("SpeechRecognizer: start() called while already listening.");
            return;
        }
        if (!this.recognition) {
             console.error("SpeechRecognizer: start() called on destroyed instance.");
             return;
        }
        try {
             this.lastError = null; // Сбрасываем ошибку перед стартом
             console.log("SpeechRecognizer: Calling native start()...");
             this.recognition.start();
             // isListening установится в onstart
        } catch (e) {
            // Обработка немедленной ошибки старта (редко, но возможно)
            console.error("SpeechRecognizer: Error calling native start()", e);
            this.isListening = false; // Убедимся, что флаг сброшен
            // Имитируем событие ошибки для консистентности
            this._handleError({ error: 'start-error', message: e.message });
            // onend может не вызваться в этом случае, вызываем его вручную? Нет, лучше не надо.
        }
    }

    stop() {
         if (!this.recognition) { console.error("SpeechRecognizer: stop() called on destroyed instance."); return; }
        if (this.isListening) {
            console.log("SpeechRecognizer: Calling native stop()...");
            this.recognition.stop();
             // onend сработает автоматически. isListening сбросится в onend.
        } else {
            // console.log("SpeechRecognizer: stop() called when not listening."); // Можно раскомментировать
        }
    }

    abort() {
        if (!this.recognition) { console.error("SpeechRecognizer: abort() called on destroyed instance."); return; }
        if (this.isListening) {
            console.log("SpeechRecognizer: Calling native abort()...");
             this.lastError = 'aborted'; // Устанавливаем код ошибки для onend
            this.recognition.abort();
            // onend сработает автоматически с ошибкой 'aborted'. isListening сбросится в onend.
        } else {
             // console.log("SpeechRecognizer: abort() called when not listening."); // Можно раскомментировать
        }
    }

    destroy() {
         console.log("SpeechRecognizer: Destroying instance...");
         if (!this.recognition) {
              console.warn("SpeechRecognizer: destroy() called on already destroyed instance.");
              return;
         }
         // Отписываемся от событий
         this.recognition.onstart = null;
         this.recognition.onresult = null;
         this.recognition.onerror = null;
         this.recognition.onend = null;
         // Пытаемся остановить/прервать, если еще слушает
         if (this.isListening) {
             try {
                 console.log("SpeechRecognizer: Aborting on destroy...");
                 this.recognition.abort();
             } catch(e) {
                 console.warn("SpeechRecognizer: Error aborting during destroy:", e);
             }
         }
         // Удаляем ссылку
         this.recognition = null;
         this.isListening = false;
         console.log("SpeechRecognizer: Instance destroyed.");
    }
}