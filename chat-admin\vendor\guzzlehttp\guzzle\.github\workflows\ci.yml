name: CI

on:
  push:
    branches:
  pull_request:

permissions:
  contents: read

jobs:
  build-lowest-version:
    name: Build lowest version
    runs-on: ubuntu-22.04

    steps:
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '7.2'
          ini-values: error_reporting=E_ALL
          coverage: none
          extensions: mbstring, intl

      - name: Set up Node
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'

      - name: Setup Problem Matchers for PHPUnit
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download dependencies
        run: composer update --no-interaction --no-progress --prefer-stable --prefer-lowest

      - name: Start test servers
        shell: bash
        run: |
          node tests/server.js &
          ./vendor/bin/http_test_server &

      - name: Run tests
        run: ./vendor/bin/phpunit

  build:
    name: Build
    runs-on: ubuntu-22.04
    strategy:
      max-parallel: 10
      matrix:
        php: ['7.2', '7.3', '7.4', '8.0', '8.1', '8.2', '8.3', '8.4']
        psr7: ['^2.7.0']
        include:
          - php: '8.4'
            psr7: '^2.7.0@dev'

    steps:
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          ini-values: error_reporting=E_ALL
          coverage: none
          extensions: mbstring, intl

      - name: Set up Node
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'

      - name: Setup Problem Matchers for PHPUnit
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download dependencies
        run: |
          composer require --no-update "guzzlehttp/psr7:${{ matrix.psr7 }}"
          composer update --no-interaction --no-progress

      - name: Start test servers
        shell: bash
        run: |
          node tests/server.js &
          ./vendor/bin/http_test_server &

      - name: Run tests
        run: ./vendor/bin/phpunit

  build-windows:
    name: Build on Windows
    runs-on: windows-2022
    strategy:
      max-parallel: 10
      matrix:
        php: ['7.2', '7.3', '7.4', '8.0', '8.1', '8.2', '8.3', '8.4']

    steps:
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          ini-values: error_reporting=E_ALL
          coverage: none
          extensions: mbstring, intl

      - name: Set up Node
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'

      - name: Setup Problem Matchers for PHPUnit
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download dependencies
        run: composer update --no-interaction --no-progress

      - name: Start test servers
        shell: bash
        run: |
          node tests/server.js &
          ./vendor/bin/http_test_server &

      - name: Run tests
        run: vendor/bin/phpunit.bat
