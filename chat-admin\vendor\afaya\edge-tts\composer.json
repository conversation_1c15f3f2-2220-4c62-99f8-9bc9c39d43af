{"name": "afaya/edge-tts", "description": "Edge TTS is a PHP package that allows access to the online text-to-speech service used by Microsoft Edge without the need for Microsoft Edge, Windows, or an API key.", "version": "1.2.9", "type": "library", "license": "GPL-3.0+", "repository": {"type": "git", "url": "https://github.com/andresayac/edge-tts"}, "require": {"php": ">=8.1", "symfony/console": "^6.4 || ^7.1", "ratchet/pawl": "^0.4.1", "ramsey/uuid": "^4.7", "react/event-loop": "^1.5"}, "autoload": {"psr-4": {"Afaya\\EdgeTTS\\": "src/"}}, "autoload-dev": {"psr-4": {"Afaya\\EdgeTTS\\Tests\\": "tests/"}}, "bin": ["src/Cli/edge-tts"], "require-dev": {"phpunit/phpunit": "^9.6"}}