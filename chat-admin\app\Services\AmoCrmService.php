<?php

declare(strict_types=1);

namespace App\Services;

/**
 * Сервис для работы с API AmoCRM через OAuth 2.0
 */
class AmoCrmService
{
    private string $portalUrl;
    private string $clientId;
    private string $clientSecret;
    private ?string $accessToken = null;
    private ?string $refreshToken = null;
    private ?int $tokenExpires = null;

    /**
     * @param string $portalUrl URL портала AmoCRM
     * @param string $clientId Client ID интеграции
     * @param string $clientSecret Client Secret интеграции
     * @param string|null $accessToken Существующий access token
     * @param string|null $refreshToken Существующий refresh token
     * @param int|null $tokenExpires Время истечения токена (timestamp)
     */
    public function __construct(
        string $portalUrl,
        ?string $clientId = null,
        ?string $clientSecret = null,
        ?string $accessToken = null,
        ?string $refreshToken = null,
        ?int $tokenExpires = null
    ) {
        $this->portalUrl = rtrim($portalUrl, '/');
        $this->clientId = $clientId ?? '';
        $this->clientSecret = $clientSecret ?? '';
        $this->accessToken = $accessToken;
        $this->refreshToken = $refreshToken;
        $this->tokenExpires = $tokenExpires;
    }

    /**
     * Проверяет, действителен ли текущий access token
     *
     * @return bool
     */
    public function isTokenValid(): bool
    {
        return $this->accessToken !== null &&
               $this->tokenExpires !== null &&
               $this->tokenExpires > time() + 60; // 60 секунд запас
    }

    /**
     * Обновляет access token используя refresh token
     *
     * @return bool
     */
    public function refreshAccessToken(): bool
    {
        if (!$this->refreshToken) {
            error_log("AmoCRM: No refresh token available");
            return false;
        }

        try {
            $url = $this->portalUrl . '/oauth2/access_token';

            $data = [
                'client_id' => $this->clientId,
                'client_secret' => $this->clientSecret,
                'grant_type' => 'refresh_token',
                'refresh_token' => $this->refreshToken,
                'redirect_uri' => '' // Может потребоваться для некоторых интеграций
            ];

            $response = $this->makeRequest($url, 'POST', $data);

            if (isset($response['access_token'])) {
                $this->accessToken = $response['access_token'];
                $this->refreshToken = $response['refresh_token'] ?? $this->refreshToken;
                $this->tokenExpires = time() + ($response['expires_in'] ?? 86400);

                return true;
            }

            error_log("AmoCRM refresh token failed: " . json_encode($response));
            return false;
        } catch (\Exception $e) {
            error_log("AmoCRM refresh token error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Получает access token по authorization code
     *
     * @param string $authCode Код авторизации
     * @param string $redirectUri Redirect URI
     * @return array|null Данные токенов или null при ошибке
     */
    public function getTokenByAuthCode(string $authCode, string $redirectUri): ?array
    {
        try {
            $url = $this->portalUrl . '/oauth2/access_token';

            $data = [
                'client_id' => $this->clientId,
                'client_secret' => $this->clientSecret,
                'grant_type' => 'authorization_code',
                'code' => $authCode,
                'redirect_uri' => $redirectUri
            ];

            $response = $this->makeRequest($url, 'POST', $data);

            if (isset($response['access_token'])) {
                $this->accessToken = $response['access_token'];
                $this->refreshToken = $response['refresh_token'] ?? null;
                $this->tokenExpires = time() + ($response['expires_in'] ?? 86400);

                return [
                    'access_token' => $this->accessToken,
                    'refresh_token' => $this->refreshToken,
                    'expires_in' => $response['expires_in'] ?? 86400,
                    'expires_at' => $this->tokenExpires
                ];
            }

            error_log("AmoCRM get token by auth code failed: " . json_encode($response));
            return null;
        } catch (\Exception $e) {
            error_log("AmoCRM get token by auth code error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Проверяет подключение к AmoCRM
     *
     * @return bool
     */
    public function testConnection(): bool
    {
        try {
            if (!$this->ensureValidToken()) {
                return false;
            }

            $url = $this->portalUrl . '/api/v4/account';
            $response = $this->makeAuthenticatedRequest($url, 'GET');

            return isset($response['id']);
        } catch (\Exception $e) {
            error_log("AmoCRM test connection error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Обеспечивает наличие действительного токена
     *
     * @return bool
     */
    private function ensureValidToken(): bool
    {
        if ($this->isTokenValid()) {
            return true;
        }

        return $this->refreshAccessToken();
    }

    /**
     * Выполняет HTTP запрос
     *
     * @param string $url URL для запроса
     * @param string $method HTTP метод
     * @param array|null $data Данные для отправки
     * @return array Ответ сервера
     * @throws \Exception
     */
    private function makeRequest(string $url, string $method = 'GET', ?array $data = null): array
    {
        $curl = curl_init();

        // Определяем тип контента в зависимости от URL
        $isOAuthRequest = strpos($url, '/oauth2/') !== false;
        $contentType = $isOAuthRequest ? 'application/x-www-form-urlencoded' : 'application/json';

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => [
                'Content-Type: ' . $contentType,
                'User-Agent: Custom-Integration/1.0'
            ],
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            if ($isOAuthRequest) {
                // Для OAuth запросов используем form-urlencoded
                curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
            } else {
                // Для API запросов используем JSON
                curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        curl_close($curl);

        if ($error) {
            throw new \Exception("cURL error: " . $error);
        }

        if ($httpCode >= 400) {
            throw new \Exception("HTTP error: " . $httpCode . " Response: " . $response);
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("JSON decode error: " . json_last_error_msg());
        }

        return $decodedResponse;
    }

    /**
     * Выполняет аутентифицированный HTTP запрос с Bearer токеном
     *
     * @param string $url URL для запроса
     * @param string $method HTTP метод
     * @param array|null $data Данные для отправки
     * @return array Ответ сервера
     * @throws \Exception
     */
    private function makeAuthenticatedRequest(string $url, string $method = 'GET', ?array $data = null): array
    {
        if (!$this->ensureValidToken()) {
            throw new \Exception("Unable to get valid access token");
        }

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->accessToken,
                'User-Agent: Custom-Integration/1.0'
            ],
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2
        ]);

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        curl_close($curl);

        if ($error) {
            throw new \Exception("cURL error: " . $error);
        }

        if ($httpCode >= 400) {
            throw new \Exception("HTTP error: " . $httpCode . " Response: " . $response);
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("JSON decode error: " . json_last_error_msg());
        }

        return $decodedResponse;
    }

    /**
     * Получает текущие токены
     *
     * @return array
     */
    public function getTokens(): array
    {
        return [
            'access_token' => $this->accessToken,
            'refresh_token' => $this->refreshToken,
            'expires_at' => $this->tokenExpires
        ];
    }

    /**
     * Создает лид в AmoCRM
     *
     * @param array $leadData Данные лида
     * @return array|null Результат создания лида
     */
    public function createLead(array $leadData): ?array
    {
        try {
            if (!$this->ensureValidToken()) {
                throw new \Exception('Failed to get valid access token');
            }

            $url = $this->portalUrl . '/api/v4/leads';

            $lead = [
                'name' => $leadData['name'] ?? 'Лид из чата',
                'price' => $leadData['price'] ?? 0,
                'custom_fields_values' => []
            ];

            // Добавляем кастомные поля
            if (!empty($leadData['custom_fields'])) {
                foreach ($leadData['custom_fields'] as $fieldId => $value) {
                    $lead['custom_fields_values'][] = [
                        'field_id' => (int)$fieldId,
                        'values' => [
                            ['value' => $value]
                        ]
                    ];
                }
            }

            $requestData = [$lead];

            $response = $this->makeAuthenticatedRequest($url, 'POST', $requestData);

            if (isset($response['_embedded']['leads'][0])) {
                return $response['_embedded']['leads'][0];
            }

            error_log("Failed to create lead in AmoCRM: " . json_encode($response));
            return null;
        } catch (\Exception $e) {
            error_log("AmoCRM create lead error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Добавляет контакт в AmoCRM
     *
     * @param array $contactData Данные контакта
     * @return array|null Результат создания контакта
     */
    public function createContact(array $contactData): ?array
    {
        try {
            if (!$this->ensureValidToken()) {
                throw new \Exception('Failed to get valid access token');
            }

            $url = $this->portalUrl . '/api/v4/contacts';

            $contact = [
                'name' => $contactData['name'] ?? 'Контакт из чата',
                'custom_fields_values' => []
            ];

            // Добавляем телефон
            if (!empty($contactData['phone'])) {
                $contact['custom_fields_values'][] = [
                    'field_code' => 'PHONE',
                    'values' => [
                        [
                            'value' => $contactData['phone'],
                            'enum_code' => 'WORK'
                        ]
                    ]
                ];
            }

            // Добавляем email
            if (!empty($contactData['email'])) {
                $contact['custom_fields_values'][] = [
                    'field_code' => 'EMAIL',
                    'values' => [
                        [
                            'value' => $contactData['email'],
                            'enum_code' => 'WORK'
                        ]
                    ]
                ];
            }

            // Добавляем кастомные поля
            if (!empty($contactData['custom_fields'])) {
                foreach ($contactData['custom_fields'] as $fieldId => $value) {
                    $contact['custom_fields_values'][] = [
                        'field_id' => (int)$fieldId,
                        'values' => [
                            ['value' => $value]
                        ]
                    ];
                }
            }

            $requestData = [$contact];

            $response = $this->makeAuthenticatedRequest($url, 'POST', $requestData);

            if (isset($response['_embedded']['contacts'][0])) {
                return $response['_embedded']['contacts'][0];
            }

            error_log("Failed to create contact in AmoCRM: " . json_encode($response));
            return null;
        } catch (\Exception $e) {
            error_log("AmoCRM create contact error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Добавляет примечание к лиду
     *
     * @param int $leadId ID лида
     * @param string $noteText Текст примечания
     * @return bool
     */
    public function addNoteToLead(int $leadId, string $noteText): bool
    {
        try {
            if (!$this->ensureValidToken()) {
                throw new \Exception('Failed to get valid access token');
            }

            $url = $this->portalUrl . '/api/v4/leads/' . $leadId . '/notes';

            $note = [
                'note_type' => 'common',
                'params' => [
                    'text' => $noteText
                ]
            ];

            $requestData = [$note];

            $response = $this->makeAuthenticatedRequest($url, 'POST', $requestData);

            return isset($response['_embedded']['notes'][0]);
        } catch (\Exception $e) {
            error_log("AmoCRM add note error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Обменивает код авторизации на токены доступа
     *
     * @param string $code Код авторизации от AmoCRM
     * @return array|null Данные токенов или null в случае ошибки
     */
    public function exchangeCodeForTokens(string $code): ?array
    {
        try {
            $url = $this->portalUrl . '/oauth2/access_token';

            $data = [
                'client_id' => $this->clientId,
                'client_secret' => $this->clientSecret,
                'grant_type' => 'authorization_code',
                'code' => $code,
                'redirect_uri' => $this->getRedirectUri()
            ];

            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => http_build_query($data),
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'User-Agent: AmoCRM-oAuth-client/1.0'
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_TIMEOUT => 30
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                error_log("AmoCRM token exchange failed. HTTP Code: $httpCode, Response: $response");
                return null;
            }

            $tokenData = json_decode($response, true);

            if (!$tokenData || !isset($tokenData['access_token'])) {
                error_log("AmoCRM token exchange: Invalid response format");
                return null;
            }

            return $tokenData;

        } catch (\Exception $e) {
            error_log("AmoCRM token exchange error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Получает Redirect URI для OAuth
     *
     * @return string
     */
    private function getRedirectUri(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        return $protocol . '://' . $host . '/chat-admin/debug_oauth_request.php';
    }


}
