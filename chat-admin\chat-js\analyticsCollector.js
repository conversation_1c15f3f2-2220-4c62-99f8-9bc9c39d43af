// chat-js/analyticsCollector.js

/**
 * Модуль для сбора аналитических данных (UTM-метки, ClientID, URL)
 */

let analyticsData = {
    clientId: null,
    utmParams: {},
    pageUrl: null,
    referrer: null,
    sessionStart: null
};

/**
 * Инициализирует сбор аналитических данных
 */
export async function initAnalyticsCollector() {
    console.log("AnalyticsCollector: Initializing...");

    // Проверяем, переданы ли аналитические данные через URL
    loadAnalyticsFromUrl();

    // Собираем базовые данные
    collectPageData();
    collectUtmParams();
    await collectYandexMetrikaClientId();

    console.log("AnalyticsCollector: Initialized with data:", analyticsData);
}

/**
 * Загружает аналитические данные, переданные через URL параметр
 */
function loadAnalyticsFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    const analyticsParam = urlParams.get('analytics');

    if (analyticsParam) {
        try {
            const passedAnalytics = JSON.parse(decodeURIComponent(analyticsParam));

            // Объединяем переданные данные с текущими
            if (passedAnalytics.clientId) {
                analyticsData.clientId = passedAnalytics.clientId;
            }

            if (passedAnalytics.utmParams && Object.keys(passedAnalytics.utmParams).length > 0) {
                analyticsData.utmParams = { ...analyticsData.utmParams, ...passedAnalytics.utmParams };
            }

            if (passedAnalytics.pageUrl) {
                analyticsData.pageUrl = passedAnalytics.pageUrl;
            }

            if (passedAnalytics.referrer) {
                analyticsData.referrer = passedAnalytics.referrer;
            }

            if (passedAnalytics.sessionStart) {
                analyticsData.sessionStart = passedAnalytics.sessionStart;
            }

            console.log("AnalyticsCollector: Loaded analytics from URL:", passedAnalytics);
        } catch (e) {
            console.warn("AnalyticsCollector: Failed to parse analytics from URL:", e);
        }
    }
}

/**
 * Собирает данные о странице
 */
function collectPageData() {
    // Если данные уже загружены из URL, не перезаписываем их
    if (!analyticsData.pageUrl) {
        analyticsData.pageUrl = window.location.href;
    }

    if (!analyticsData.referrer) {
        analyticsData.referrer = document.referrer || null;
    }

    if (!analyticsData.sessionStart) {
        analyticsData.sessionStart = new Date().toISOString();
    }

    console.log("AnalyticsCollector: Page data collected", {
        pageUrl: analyticsData.pageUrl,
        referrer: analyticsData.referrer
    });
}

/**
 * Собирает UTM-параметры из URL
 */
function collectUtmParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const utmParams = {};
    
    // Список UTM-параметров для сбора
    const utmKeys = [
        'utm_source',
        'utm_medium', 
        'utm_campaign',
        'utm_term',
        'utm_content'
    ];
    
    utmKeys.forEach(key => {
        const value = urlParams.get(key);
        if (value) {
            utmParams[key] = value;
        }
    });
    
    // Также проверяем localStorage на случай, если UTM были сохранены ранее
    const savedUtm = localStorage.getItem('utm_params');
    if (savedUtm) {
        try {
            const parsedUtm = JSON.parse(savedUtm);
            Object.assign(utmParams, parsedUtm);
        } catch (e) {
            console.warn("AnalyticsCollector: Failed to parse saved UTM params");
        }
    }
    
    // Сохраняем UTM в localStorage для последующих страниц
    if (Object.keys(utmParams).length > 0) {
        localStorage.setItem('utm_params', JSON.stringify(utmParams));
        analyticsData.utmParams = utmParams;
        
        console.log("AnalyticsCollector: UTM params collected", utmParams);
    }
}

/**
 * Получает ClientID из Яндекс.Метрики
 */
async function collectYandexMetrikaClientId() {
    // Проверяем, доступна ли Яндекс.Метрика
    if (typeof ym === 'undefined') {
        console.warn("AnalyticsCollector: Yandex.Metrika not found");
        return;
    }

    // Получаем ID счетчика из настроек (асинхронно)
    const counterId = await getYandexMetrikaCounterIdFromSettings();

    if (!counterId) {
        console.warn("AnalyticsCollector: Yandex.Metrika counter ID not found");
        return;
    }

    try {
        ym(counterId, 'getClientID', function(clientID) {
            analyticsData.clientId = clientID;
            console.log("AnalyticsCollector: Yandex.Metrika ClientID collected", clientID);
        });
    } catch (error) {
        console.error("AnalyticsCollector: Error getting Yandex.Metrika ClientID", error);
    }
}

/**
 * Получает ID счетчика Яндекс.Метрики
 * Сначала пытается получить из настроек, затем использует значение по умолчанию
 */
function getYandexMetrikaCounterId() {
    // Пытаемся найти счетчик в глобальных переменных
    if (window.YANDEX_METRIKA_COUNTER_ID) {
        return window.YANDEX_METRIKA_COUNTER_ID;
    }

    // Пытаемся найти в коде Яндекс.Метрики на странице
    const scripts = document.getElementsByTagName('script');
    for (let script of scripts) {
        const content = script.innerHTML;
        const match = content.match(/ym\((\d+),/);
        if (match) {
            return parseInt(match[1]);
        }
    }

    // Значение по умолчанию
    return 100128474;
}

/**
 * Получает ID счетчика Яндекс.Метрики из настроек CRM (асинхронно)
 */
async function getYandexMetrikaCounterIdFromSettings() {
    try {
        // Получаем настройки из API
        const response = await fetch('/chat-admin/api.php?action=getCrmSettings');
        const data = await response.json();

        if (data.success && data.settings && data.settings.yandex_metrika_counter_id) {
            return parseInt(data.settings.yandex_metrika_counter_id);
        }

        // Fallback к синхронному методу
        return getYandexMetrikaCounterId();
    } catch (error) {
        console.warn("AnalyticsCollector: Could not get counter ID from settings, using fallback");
        return getYandexMetrikaCounterId();
    }
}

/**
 * Получает название цели из настроек CRM
 */
async function getYandexMetrikaGoalName() {
    try {
        // Получаем настройки из API
        const response = await fetch('/chat-admin/api.php?action=getCrmSettings');
        const data = await response.json();

        if (data.success && data.settings && data.settings.yandex_metrika_goal_name) {
            return data.settings.yandex_metrika_goal_name;
        }

        // Fallback к значению по умолчанию
        return 'chat_contact_got';
    } catch (error) {
        console.warn("AnalyticsCollector: Could not get goal name from settings, using default");
        return 'chat_contact_got';
    }
}

/**
 * Отправляет событие в Яндекс.Метрику
 */
export async function sendYandexMetrikaGoal(goalName, params = {}) {
    if (typeof ym === 'undefined') {
        console.warn("AnalyticsCollector: Cannot send goal - Yandex.Metrika not found");
        return false;
    }

    const counterId = await getYandexMetrikaCounterIdFromSettings();
    if (!counterId) {
        console.warn("AnalyticsCollector: Cannot send goal - counter ID not found");
        return false;
    }

    try {
        const goalParams = {
            URL: window.location.href,
            ...params
        };

        ym(counterId, 'reachGoal', goalName, goalParams);
        console.log("AnalyticsCollector: Goal sent to Yandex.Metrika", {
            counterId,
            goalName,
            params: goalParams
        });

        return true;
    } catch (error) {
        console.error("AnalyticsCollector: Error sending goal to Yandex.Metrika", error);
        return false;
    }
}

/**
 * Возвращает все собранные аналитические данные
 */
export function getAnalyticsData() {
    return { ...analyticsData };
}

/**
 * Устанавливает аналитические данные (для внешнего использования)
 */
export function setAnalyticsData(data) {
    if (data.clientId) {
        analyticsData.clientId = data.clientId;
    }

    if (data.utmParams && Object.keys(data.utmParams).length > 0) {
        analyticsData.utmParams = { ...analyticsData.utmParams, ...data.utmParams };
    }

    if (data.pageUrl) {
        analyticsData.pageUrl = data.pageUrl;
    }

    if (data.referrer) {
        analyticsData.referrer = data.referrer;
    }

    if (data.sessionStart) {
        analyticsData.sessionStart = data.sessionStart;
    }

    console.log("AnalyticsCollector: Analytics data set externally:", data);
}

/**
 * Обновляет аналитические данные (например, при изменении страницы)
 */
export function updateAnalyticsData() {
    collectPageData();
    // UTM параметры обычно не меняются в рамках сессии
    // ClientID тоже остается тем же
}

/**
 * Очищает сохраненные UTM параметры (например, при завершении сессии)
 */
export function clearSavedUtmParams() {
    localStorage.removeItem('utm_params');
    analyticsData.utmParams = {};
    console.log("AnalyticsCollector: Saved UTM params cleared");
}

/**
 * Проверяет, есть ли контактные данные в сообщении
 * Упрощенная но надежная проверка
 */
export function hasContactData(message) {
    console.log('AnalyticsCollector: Testing message for contacts:', message);

    // Простые и надежные паттерны
    const phonePatterns = [
        /\+7\s*\(\s*\d{3}\s*\)\s*\d{3}[\s\-]?\d{2}[\s\-]?\d{2}/, // +7 (900) 123-45-67
        /\+7\s*\d{10}/, // +79001234567
        /8\s*\d{10}/, // 89001234567
        /\d{11}/, // 79001234567
        /\d{10}/, // 9001234567
        /\d{7,}/ // Любая последовательность из 7+ цифр
    ];

    const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;

    // Проверяем телефоны
    for (let i = 0; i < phonePatterns.length; i++) {
        const pattern = phonePatterns[i];
        if (pattern.test(message)) {
            console.log(`AnalyticsCollector: Phone pattern ${i} matched:`, pattern);
            return true;
        }
    }

    // Проверяем email
    if (emailPattern.test(message)) {
        console.log('AnalyticsCollector: Email pattern matched');
        return true;
    }

    console.log('AnalyticsCollector: No contact data found');
    return false;
}

/**
 * Обрабатывает отправку контактных данных
 */
export async function handleContactSubmission(message) {
    if (!hasContactData(message)) {
        return false;
    }

    console.log("AnalyticsCollector: Contact data detected in message", message);

    // Определяем тип контактных данных
    const hasPhone = /(\+7|8|7)[\s\(\-]?\d{3}[\s\)\-]?\d{3}[\s\-]?\d{2}[\s\-]?\d{2}|\d{10,11}/.test(message);
    const hasEmail = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/.test(message);

    // Получаем название цели из настроек
    const goalName = await getYandexMetrikaGoalName();

    // Отправляем цель в Яндекс.Метрику
    const goalSent = await sendYandexMetrikaGoal(goalName, {
        message_length: message.length,
        has_phone: hasPhone,
        has_email: hasEmail,
        contact_type: hasPhone && hasEmail ? 'both' : (hasPhone ? 'phone' : 'email')
    });

    return goalSent;
}

// Экспортируем для использования в других модулях
export default {
    initAnalyticsCollector,
    getAnalyticsData,
    updateAnalyticsData,
    sendYandexMetrikaGoal,
    handleContactSubmission,
    hasContactData,
    clearSavedUtmParams
};
