<?php

namespace App\Models;

use App\Models\Database;

class ChatSession {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Получает последнюю активную сессию пользователя.
     *
     * @param string $userId ID пользователя (может быть 'anon_...' или число).
     * @return array|false Ассоциативный массив с данными сессии или false.
     */
    public function getActiveSession(string $userId): array|false {
        try {
            $stmt = $this->db->prepare("
                SELECT id, title, updated_at FROM chat_sessions
                WHERE user_id = :user_id
                ORDER BY updated_at DESC
                LIMIT 1
            ");
            $stmt->bindValue(':user_id', $userId, \SQLITE3_TEXT);
            return $stmt->execute()->fetchArray(\SQLITE3_ASSOC);
        } catch (\Exception $e) {
            error_log("Error getting active session for user ($userId): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Получает все сессии пользователя.
     *
     * @param string $userId ID пользователя.
     * @return array Массив сессий.
     */
    public function getUserSessions(string $userId): array {
        $sessions = [];
        try {
            $stmt = $this->db->prepare("
                SELECT id, title, updated_at FROM chat_sessions
                WHERE user_id = :user_id
                ORDER BY updated_at DESC
            ");
            $stmt->bindValue(':user_id', $userId, \SQLITE3_TEXT);
            $result = $stmt->execute();
            while ($row = $result->fetchArray(\SQLITE3_ASSOC)) {
                $sessions[] = $row;
            }
        } catch (\Exception $e) {
            error_log("Error getting sessions for user ($userId): " . $e->getMessage());
        }
        return $sessions;
    }

    /**
     * Создает новую сессию чата.
     *
     * @param string $userId ID пользователя.
     * @param string $title Заголовок чата.
     * @return array|false Массив с ID и заголовком новой сессии или false.
     */
    public function createSession(string $userId, string $title = 'Новый чат'): array|false {
        $cleanTitle = substr(strip_tags($title), 0, 100);
        try {
            $stmt = $this->db->prepare("
                INSERT INTO chat_sessions (user_id, session_uuid, title)
                VALUES (:user_id, :uuid, :title)
            ");
            $stmt->bindValue(':user_id', $userId, \SQLITE3_TEXT);
            $stmt->bindValue(':uuid', uniqid('session_', true), \SQLITE3_TEXT);
            $stmt->bindValue(':title', $cleanTitle, \SQLITE3_TEXT);

            if ($stmt->execute()) {
                $sessionId = $this->db->lastInsertRowID();
                return ['id' => $sessionId, 'title' => $cleanTitle];
            } else {
                 error_log("Failed to create session for user ($userId): " . $this->db->lastErrorMsg());
                 return false;
            }
        } catch (\Exception $e) {
            error_log("Exception creating session for user ($userId): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверяет, принадлежит ли сессия указанному пользователю.
     *
     * @param int $sessionId ID сессии.
     * @param string $userId ID пользователя.
     * @return bool True, если принадлежит, иначе false.
     */
    public function verifySessionOwnership(int $sessionId, string $userId): bool {
        try {
            $stmtCheck = $this->db->prepare("
                SELECT id FROM chat_sessions
                WHERE id = :session_id AND user_id = :user_id
            ");
            $stmtCheck->bindValue(':session_id', $sessionId, \SQLITE3_INTEGER);
            $stmtCheck->bindValue(':user_id', $userId, \SQLITE3_TEXT);
            $session = $stmtCheck->execute()->fetchArray(\SQLITE3_ASSOC);
            return (bool)$session; // True если найдена, false если нет
        } catch (\Exception $e) {
             error_log("Error verifying session ownership (Session: $sessionId, User: $userId): " . $e->getMessage());
             return false;
        }
    }

    /**
     * Обновляет заголовок сессии.
     *
     * @param int $sessionId ID сессии.
     * @param string $title Новый заголовок.
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function updateSessionTitle(int $sessionId, string $title): bool {
        $cleanTitle = mb_substr(strip_tags($title), 0, 100);
        if (empty($cleanTitle)) return false;

        try {
            $stmt = $this->db->prepare("
                UPDATE chat_sessions
                SET title = :title, updated_at = CURRENT_TIMESTAMP
                WHERE id = :id
            ");
            $stmt->bindValue(':id', $sessionId, \SQLITE3_INTEGER);
            $stmt->bindValue(':title', $cleanTitle, \SQLITE3_TEXT);
            return (bool)$stmt->execute();
        } catch (\Exception $e) {
             error_log("Error updating session title (ID: $sessionId): " . $e->getMessage());
             return false;
        }
    }

     /**
     * Обновляет время последнего доступа к сессии.
     *
     * @param int $sessionId ID сессии.
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function touchSession(int $sessionId): bool {
         try {
            $updateStmt = $this->db->prepare("
                UPDATE chat_sessions
                SET updated_at = CURRENT_TIMESTAMP
                WHERE id = :session_id
            ");
            $updateStmt->bindValue(':session_id', $sessionId, \SQLITE3_INTEGER);
            return (bool)$updateStmt->execute();
        } catch (\Exception $e) {
             error_log("Error touching session (ID: $sessionId): " . $e->getMessage());
             return false;
        }
    }

    // Метод deleteChatSession уже есть в Message.php, т.к. он удаляет и сообщения/файлы.
    // Если нужно будет отдельное удаление только записи сессии, можно добавить сюда.
}
