<?php

namespace App\Models;

class TtsModel {
    private string $text;
    private ?string $voiceShortName;
    private ?array $options;

    public function __construct(string $text, ?string $voiceShortName = null, ?array $options = null) {
        $this->text = $text;
        $this->voiceShortName = $voiceShortName;
        $this->options = $options;
    }

    public function getText(): string {
        return $this->text;
    }

    public function getVoiceShortName(): ?string {
        return $this->voiceShortName;
    }

    public function getOptions(): ?array {
        return $this->options;
    }
}
