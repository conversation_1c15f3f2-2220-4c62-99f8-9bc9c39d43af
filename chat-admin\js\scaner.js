document.addEventListener('DOMContentLoaded', () => {
    // --- Весь JS для сканера и темы (без изменений) ---
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const startButton = document.getElementById('startButton');
    const scannerOverlay = document.getElementById('scannerOverlay');
    const loadingDiv = document.getElementById('loading');
    const resultContainer = document.getElementById('resultContainer');
    const resultContent = document.getElementById('resultContent');
    const resultStatus = document.getElementById('resultStatus');
    const themeToggle = document.getElementById('themeToggle');
    const flash = document.getElementById('flash');
    const ctx = canvas.getContext('2d');

    let scanning = false;
    let stream = null;

    themeToggle.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        themeToggle.innerHTML = newTheme === 'dark'
            ? '<i class="fas fa-sun"></i>'
            : '<i class="fas fa-moon"></i>';
    });

    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    themeToggle.innerHTML = savedTheme === 'dark'
        ? '<i class="fas fa-sun"></i>'
        : '<i class="fas fa-moon"></i>';

    startButton.addEventListener('click', async () => {
        if (scanning) {
            stopScanner();
            return;
        }
        try {
            resultContainer.style.display = 'none';
            stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            });
            video.srcObject = stream;
            await video.play();
            video.style.display = 'block';
            scannerOverlay.classList.add('hidden');
            startButton.innerHTML = '<i class="fas fa-stop"></i><span>Остановить</span>';
            scanning = true;
            requestAnimationFrame(scanFrame);
        } catch (err) {
            console.error("Ошибка камеры:", err);
            showError(`Ошибка доступа к камере. Убедитесь, что разрешение предоставлено и используется HTTPS (если не localhost). Ошибка: ${err.message}`);
        }
    });

    function scanFrame() {
        if (!scanning) return;
        if (video.readyState === video.HAVE_ENOUGH_DATA) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const code = jsQR(imageData.data, imageData.width, imageData.height);
            if (code) {
                flash.style.opacity = '0.8';
                setTimeout(() => { flash.style.opacity = '0'; }, 200);
                stopScanner();
                checkCode(code.data);
            }
        }
        requestAnimationFrame(scanFrame);
    }

    function stopScanner() {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }
        video.style.display = 'none';
        scannerOverlay.classList.remove('hidden');
        startButton.innerHTML = '<i class="fas fa-camera"></i><span>Запустить сканер</span>';
        scanning = false;
    }

    async function checkCode(code) {
        loadingDiv.style.display = 'flex';
        try {
            const mockApiResponse = await mockApiCall(code);
            displayResults(code, mockApiResponse);
        } catch (err) {
            console.error("Ошибка API:", err);
            showError(`Ошибка при проверке кода: ${err.message}`);
        } finally {
            loadingDiv.style.display = 'none';
        }
    }

    function displayResults(code, data) {
        resultContent.innerHTML = '';
        const isValid = data.status === 'VALID';
        resultStatus.textContent = isValid ? 'Подлинный' : 'Неизвестный';
        resultStatus.className = `result-status ${isValid ? '' : 'error'}`;
        addResultItem('Код маркировки', code);
        if (data.productName) addResultItem('Наименование', data.productName);
        if (data.manufacturer) addResultItem('Производитель', data.manufacturer);
        if (data.productionDate) addResultItem('Дата производства', formatDate(data.productionDate));
        if (data.statusDescription) addResultItem('Статус', data.statusDescription);
        resultContainer.style.display = 'block';
        resultContainer.style.animation = 'fadeIn 0.5s ease-out';
    }

    function addResultItem(label, value) {
        const item = document.createElement('div');
        item.className = 'result-item';
        item.innerHTML = `
            <div class="result-label">${label}</div>
            <div class="result-value">${value || 'Неизвестно'}</div>
        `;
        resultContent.appendChild(item);
    }

    function showError(message) {
        resultContent.innerHTML = '';
        const item = document.createElement('div');
        item.className = 'result-item';
        item.innerHTML = `
            <div class="result-label" style="color: var(--error);">Ошибка</div>
            <div class="result-value">${message}</div>
        `;
        resultContent.appendChild(item);
        resultStatus.textContent = 'Ошибка';
        resultStatus.className = 'result-status error';
        resultContainer.style.display = 'block';
    }

    function formatDate(dateString) {
        if (!dateString) return 'Неизвестно';
        try {
           const date = new Date(dateString);
           return date.toLocaleDateString('ru-RU');
        } catch (e) {
           return dateString;
        }
    }

    async function mockApiCall(code) {
        await new Promise(resolve => setTimeout(resolve, 1500));
        const mockData = {
            status: 'VALID',
            productName: 'Амоксициллин 500 мг, таблетки',
            manufacturer: 'ООО "Фармстандарт"',
            productionDate: '2023-05-15',
            statusDescription: 'Товар произведен легально и находится в обороте'
        };
        if (Math.random() < 0.1) {
             throw new Error("Сервис временно недоступен. Попробуйте позже.");
        }
        if (code.includes("INVALID")) {
             return { status: 'INVALID', statusDescription: 'Код не найден в системе или выведен из оборота.' };
        }
        return mockData;
    }

    window.addEventListener('beforeunload', () => {
        stopScanner();
    });
     // --- Конец JS для сканера ---


}); // Конец DOMContentLoaded