<?php

namespace App\Models;

use App\Models\Database;

class DashboardStats {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Получает общее количество сообщений.
     *
     * @return int Количество сообщений или 0 в случае ошибки.
     */
    public function getTotalMessagesCount(): int {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM messages");
            $result = $stmt->execute();
            // fetchArray() без аргументов возвращает массив с числовыми индексами
            $count = $result->fetchArray()[0] ?? 0;
            return (int)$count;
        } catch (\Exception $e) {
            error_log("Error fetching total messages count: " . $e->getMessage());
            return 0; // Возвращаем 0 в случае ошибки
        }
    }

    /**
     * Получает количество активных настроек API.
     *
     * @return int Количество активных API или 0 в случае ошибки.
     */
    public function getActiveApiCount(): int {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM api_settings WHERE is_active = 1");
            $result = $stmt->execute();
            $count = $result->fetchArray()[0] ?? 0;
            return (int)$count;
        } catch (\Exception $e) {
            error_log("Error fetching active API count: " . $e->getMessage());
            return 0; // Возвращаем 0 в случае ошибки
        }
    }

    /**
     * Получает количество уникальных чатов.
     *
     * @return int Количество чатов или 0 в случае ошибки.
     */
    public function getTotalChatsCount(): int {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(DISTINCT chat_id) FROM messages");
            $result = $stmt->execute();
            $count = $result->fetchArray()[0] ?? 0;
            return (int)$count;
        } catch (\Exception $e) {
            error_log("Error fetching total chats count: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Получает количество уникальных пользователей.
     *
     * @return int Количество пользователей или 0 в случае ошибки.
     */
    public function getTotalUsersCount(): int {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM users");
            $result = $stmt->execute();
            $count = $result->fetchArray()[0] ?? 0;
            return (int)$count;
        } catch (\Exception $e) {
            error_log("Error fetching total users count: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Получает количество контекстных файлов.
     *
     * @return int Количество файлов или 0 в случае ошибки.
     */
    public function getTotalContextFilesCount(): int {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM context_files");
            $result = $stmt->execute();
            $count = $result->fetchArray()[0] ?? 0;
            return (int)$count;
        } catch (\Exception $e) {
            error_log("Error fetching context files count: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Получает среднее количество сообщений на чат
     * 
     * @return float Среднее количество сообщений или 0 в случае ошибки
     */
    public function getAverageMessagesPerChat(): float {
        try {
            $stmt = $this->db->prepare("
                SELECT CAST(COUNT(m.id) AS FLOAT) / COUNT(DISTINCT m.session_id) as avg_messages
                FROM messages m
                WHERE m.session_id IS NOT NULL
            ");
            $result = $stmt->execute();
            $avg = $result->fetchArray()[0] ?? 0;
            return (float)$avg;
        } catch (\Exception $e) {
            error_log("Error calculating average messages per chat: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Получает статистику сообщений по месяцам за последние 6 месяцев
     * 
     * @return array Массив с количеством сообщений по месяцам
     */
    public function getMessagesMonthlyTrend(): array {
        try {
            $stmt = $this->db->prepare("
                WITH RECURSIVE
                months(date) AS (
                    SELECT date('now', 'start of month', '-5 months')
                    UNION ALL
                    SELECT date(date, '+1 month')
                    FROM months
                    WHERE date < date('now', 'start of month')
                )
                SELECT 
                    strftime('%Y-%m', months.date) as month,
                    COUNT(messages.id) as message_count
                FROM months
                LEFT JOIN messages ON strftime('%Y-%m', messages.created_at) = strftime('%Y-%m', months.date)
                GROUP BY month
                ORDER BY month ASC
            ");
            $result = $stmt->execute();
            $trend = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                $trend[] = $row;
            }
            return $trend;
        } catch (\Exception $e) {
            error_log("Error getting monthly message trend: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Получает топ активных пользователей по количеству сообщений
     * 
     * @param int $limit Количество пользователей в топе
     * @return array Массив с данными пользователей и количеством их сообщений
     */
    public function getTopActiveUsers(int $limit = 5): array {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    u.username,
                    COUNT(m.id) as message_count
                FROM users u
                JOIN messages m ON m.user_id = u.id
                GROUP BY u.id, u.username
                ORDER BY message_count DESC
                LIMIT :limit
            ");
            $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $users = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                $users[] = $row;
            }
            return $users;
        } catch (\Exception $e) {
            error_log("Error getting top active users: " . $e->getMessage());
            return [];
        }
    }
}
