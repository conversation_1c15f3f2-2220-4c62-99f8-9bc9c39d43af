// --- START OF FILE uiModal.js ---

import { findOrFail, createElement } from './domUtils.js';
import { getState } from './state.js';
import textManager from './textManager.js'; // Импортируем менеджер текстов

let modal, modalContent, closeModalBtn, endCallBtn, voiceStatus, avatar, openChatBtn, refreshBtn;
let callButtons = [];
let actionControl;
let actionIcon;
let actionText;
let currentClickHandler = null;

export function initModalUI(onOpenModal, onCloseModal, onSwitchToChat) {
    try {
        modal = findOrFail('.czn-chat-modal');
        modalContent = findOrFail('.czn-chat-modal-content', modal);
        closeModalBtn = findOrFail('.czn-chat-close-modal-btn', modal);
        endCallBtn = findOrFail('.czn-chat-end-call-btn', modal);
        voiceStatus = findOrFail('.czn-chat-voice-status', modal);
        avatar = findOrFail('.czn-chat-avatar', modal);        openChatBtn = findOrFail('.czn-chat-chat-modal-btn', modal);
        refreshBtn = findOrFail('.czn-chat-refresh-btn', modal);
        callButtons = document.querySelectorAll(".czn-chat-call-ai-btn, .czn-chat-new-chat-call-btn");
        actionControl = findOrFail('.czn-chat-action-control', modal);
        actionIcon = findOrFail('.czn-chat-action-icon', actionControl);
        actionText = findOrFail('.czn-chat-action-text', actionControl);

        // Устанавливаем тексты из textManager
        closeModalBtn.title = textManager.getText('close_modal_title');
        refreshBtn.title = textManager.getText('refresh_title');
        openChatBtn.title = textManager.getText('open_chat_title');
        endCallBtn.title = textManager.getText('end_call_title');
        endCallBtn.querySelector('span').textContent = textManager.getText('end_call_text');

        // Устанавливаем заголовок модального окна
        const modalTitle = findOrFail('.czn-chat-modal-title', modal);
        modalTitle.textContent = textManager.getText('modal_title');

        callButtons.forEach(button => {
            button.addEventListener('click', onOpenModal);
            // Устанавливаем текст для кнопок вызова
            const span = button.querySelector('span');
            if (span) {
                span.textContent = textManager.getText('call_ai_button');
            }
        });
        closeModalBtn.addEventListener('click', onCloseModal);
        endCallBtn.addEventListener('click', onCloseModal);

        // Add refresh button animation class while refreshing
        refreshBtn.addEventListener('click', async () => {
            refreshBtn.classList.add('czn-chat-refreshing');
            try {
                await window.voiceCallHandler?.handleRefreshConnection();
            } finally {
                setTimeout(() => refreshBtn.classList.remove('czn-chat-refreshing'), 1000);
            }
        });
        openChatBtn.addEventListener('click', () => {
            onSwitchToChat();
            onCloseModal();
        });

        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                onCloseModal();
            }
        });

        console.log("Modal UI Initialized (with unified action control)");

    } catch (error) {
        console.error("Failed to initialize Modal UI:", error);
    }
}

export function openModalWindow() {
    if (!modal) return;
    modal.style.display = 'block';
    requestAnimationFrame(() => {
        modal.classList.add('czn-chat-show');
    });
    updateModalStatus(textManager.getText('connection_status'));
    setAvatarSpeaking(false);
    if (actionControl) {
        actionControl.style.display = 'none';
    } else {
        console.error("openModalWindow: actionControl not found!");
    }
     if (voiceStatus) { // Убедимся, что статус виден при открытии
        voiceStatus.style.display = 'block';
    }
}

export function closeModalWindow() {
    if (!modal) return;
    modal.classList.remove('czn-chat-show');
    const handleTransitionEnd = (event) => {
        if (event.target === modal && event.propertyName === 'opacity') {
            modal.style.display = 'none';
            if (actionControl) actionControl.style.display = 'none';
            // Не сбрасываем статус здесь, пусть остается последним
            // updateModalStatus('Отключено.');
            modal.removeEventListener('transitionend', handleTransitionEnd);
        }
    };
    modal.addEventListener('transitionend', handleTransitionEnd);
    setTimeout(() => {
        if (!modal.classList.contains('czn-chat-show')) {
            modal.style.display = 'none';
             if (actionControl) actionControl.style.display = 'none';
            modal.removeEventListener('transitionend', handleTransitionEnd);
        }
    }, 400);
}

export function updateModalStatus(text) {
    if (voiceStatus) {
        voiceStatus.textContent = text;
        // УБИРАЕМ СКРЫТИЕ ЭЛЕМЕНТА. Он всегда будет block по CSS.
        // voiceStatus.style.display = text ? 'block' : 'none';
    } else {
        console.warn("updateModalStatus: voiceStatus element (.czn-chat-voice-status) not found.");
    }
}

export function setAvatarSpeaking(isSpeaking) {
    if (avatar) {
        avatar.classList.toggle('czn-chat-is-speaking', isSpeaking);
    } else {
        console.warn("setAvatarSpeaking: avatar element (.czn-chat-avatar) not found.");
    }
}

export function updateActionControlUI(state, clickHandler) {
    if (!actionControl || !actionIcon || !actionText) {
        console.error("updateActionControlUI: Control elements not found!");
        return;
    }

    actionControl.style.display = 'flex';

    if (currentClickHandler) {
        actionControl.removeEventListener('click', currentClickHandler);
        currentClickHandler = null;
    }
    actionControl.classList.remove('czn-chat-interrupt');

    if (state === 'listening') {
        actionIcon.style.backgroundColor = 'var(--czn-chat-text-light)';
        // Путь к иконке задается в CSS через mask-image
        // В модальном окне используем краткий текст "Говорите"
        actionText.textContent = textManager.getText('mic_active_text');
        actionText.style.color = 'var(--czn-chat-text-light)';
        actionControl.style.cursor = 'default';
    } else if (state === 'interrupt') {
        actionControl.classList.add('czn-chat-interrupt');
        // Путь к иконке задается в CSS через mask-image
        actionText.textContent = textManager.getText('interrupt_response');
        actionText.style.color = 'var(--czn-chat-primary)';
        actionControl.style.cursor = 'pointer';
        if (typeof clickHandler === 'function') {
            currentClickHandler = clickHandler;
            actionControl.addEventListener('click', currentClickHandler);
        }
    } else {
        console.warn("updateActionControlUI: Unknown state provided:", state);
        actionControl.style.display = 'none';
    }
}

export function setModalAvatarSrc(src) {
    console.log(`[uiModal] Attempting to set modal avatar src to: ${src}`); // Лог в начале функции
    if (!avatar) {
        console.warn("[uiModal] Avatar container element (.czn-chat-avatar) not found. Cannot set avatar src.");
        return;
    }

    let avatarImg = avatar.querySelector('img');
    console.log(`[uiModal] Found existing img element: ${!!avatarImg}`); // Лог после поиска img

    if (avatarImg) {
        // Если img уже существует, просто обновляем его src
        avatarImg.src = src;
        console.log(`[uiModal] Updated existing modal avatar src to: ${src}`); // Лог после обновления
    } else {
        // Если img не существует, создаем его
        avatarImg = createElement('img', {
            src: src,
            alt: textManager.getText('assistant_hint') // Используем текст из textManager
        });

        // Находим элемент анимации, если он есть
        const voiceAnimationElement = avatar.querySelector('.czn-chat-voice-animation');
        console.log(`[uiModal] Found voice animation element: ${!!voiceAnimationElement}`); // Лог после поиска анимации

        if (voiceAnimationElement) {
            // Вставляем img перед элементом анимации
            avatar.insertBefore(avatarImg, voiceAnimationElement);
            console.log(`[uiModal] Created and inserted new modal avatar img before animation element: ${src}`); // Лог после вставки перед анимацией
        } else {
            // Если элемента анимации нет, просто добавляем img в контейнер
            avatar.appendChild(avatarImg);
            console.log(`[uiModal] Created and appended new modal avatar img: ${src}`); // Лог после добавления в конец
        }
    }
}

// --- END OF FILE uiModal.js ---