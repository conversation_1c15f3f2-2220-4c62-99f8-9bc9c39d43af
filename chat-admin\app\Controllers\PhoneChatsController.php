<?php

namespace App\Controllers;

use App\Models\Database;
use App\Models\User;
use App\Services\PhoneNotificationService;

class PhoneChatsController extends BaseController
{
    private $db;
    private $phoneService;

    public function __construct()
    {
        $this->db = Database::getInstance(); // ИСПРАВЛЕНО: убрано ->getConnection()
        $this->phoneService = new PhoneNotificationService();
    }

    /**
     * Главная страница переписок с телефонами
     */
    public function index()
    {
        $this->checkAuth();

        // Проверяем, является ли запрос AJAX
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        // Отладка
        error_log("PhoneChatsController: isAjax = " . ($isAjax ? 'true' : 'false'));
        error_log("PhoneChatsController: Headers = " . json_encode($_SERVER));

        try {
            // Получаем данные пользователя
            $userId = $this->getUserId();
            $userModel = new \App\Models\User($this->db);
            $user = $userModel->getUserById($userId);

            // Если пользователь не найден, используем значения по умолчанию
            if (!$user || !is_array($user)) {
                $user = ['username' => 'Admin', 'email' => '<EMAIL>'];
            }

            // Получаем параметры фильтрации
            $search = trim($_GET['search'] ?? '');
            $dateFrom = trim($_GET['date_from'] ?? '');
            $dateTo = trim($_GET['date_to'] ?? '');
            $page = max(1, (int)($_GET['page_num'] ?? 1));
            $perPage = (int)($_GET['per_page'] ?? 10); // По умолчанию 10 элементов
            $perPage = in_array($perPage, [5, 10, 20, 50, 100]) ? $perPage : 10; // Разрешенные значения
            $limit = $perPage;
            $offset = ($page - 1) * $limit;

            // Получаем переписки с телефонами
            $phoneChats = $this->getPhoneChats($search, $dateFrom, $dateTo, $limit, $offset);
            $totalCount = $this->getPhoneChatsCount($search, $dateFrom, $dateTo);
            $totalPages = max(1, ceil($totalCount / $limit));

            // Отладочная информация
            error_log("PhoneChatsController: Found " . count($phoneChats) . " phone chats");
            error_log("PhoneChatsController: Total count: " . $totalCount);
            error_log("PhoneChatsController: Total pages: " . $totalPages);
            error_log("PhoneChatsController: Current page: " . $page);
            error_log("PhoneChatsController: Per page: " . $perPage);
            if (!empty($phoneChats)) {
                error_log("PhoneChatsController: First chat data: " . json_encode($phoneChats[0]));

                // Тестируем извлечение данных
                $testText = "9771391669 муж. 9772882033а";
                $testData = $this->extractDigitalDataFromText($testText);
                error_log("PhoneChatsController: Test extraction from '$testText': " . json_encode($testData));
            }

            // Получаем статистику
            $stats = $this->getPhoneStats();

            $data = [
                'phoneChats' => $phoneChats,
                'totalCount' => $totalCount,
                'currentPage' => $page,
                'totalPages' => $totalPages,
                'perPage' => $perPage,
                'search' => htmlspecialchars($search),
                'dateFrom' => htmlspecialchars($dateFrom),
                'dateTo' => htmlspecialchars($dateTo),
                'stats' => $stats,
                'pageName' => 'phone_chats',
                'user' => $user,
                'controller' => $this // Передаем контроллер для доступа к методам
            ];

            // Для AJAX запросов возвращаем только контент
            if ($isAjax) {
                $this->loadView('PhoneChats/ajax_content', $data);
            } else {
                $this->loadView('PhoneChats/index', $data);
            }
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error in index: " . $e->getMessage());

            // Получаем данные пользователя для отображения ошибки
            $userId = $this->getUserId();
            $userModel = new \App\Models\User($this->db);
            $user = $userModel->getUserById($userId);

            if (!$user || !is_array($user)) {
                $user = ['username' => 'Admin', 'email' => '<EMAIL>'];
            }

            $errorData = [
                'phoneChats' => [],
                'totalCount' => 0,
                'currentPage' => 1,
                'totalPages' => 1,
                'search' => '',
                'dateFrom' => '',
                'dateTo' => '',
                'stats' => [
                    'total_chats_with_phones' => 0,
                    'days_with_phones' => 0,
                    'total_messages_with_phones' => 0
                ],
                'pageName' => 'phone_chats',
                'user' => $user,
                'error' => 'Произошла ошибка при загрузке данных'
            ];

            // Для AJAX запросов возвращаем только контент
            if ($isAjax) {
                $this->loadView('PhoneChats/ajax_content', $errorData);
            } else {
                $this->loadView('PhoneChats/index', $errorData);
            }
        }
    }

    /**
     * Просмотр конкретной переписки
     */
    public function view()
    {
        $this->checkAuth();

        $sessionId = (int)($_GET['session_id'] ?? 0);
        if (!$sessionId) {
            header('Location: index.php?page=phone_chats&error=invalid_id');
            exit;
        }

        try {
            // Получаем данные пользователя
            $userId = $this->getUserId();
            $userModel = new \App\Models\User($this->db);
            $user = $userModel->getUserById($userId);

            if (!$user || !is_array($user)) {
                $user = ['username' => 'Admin', 'email' => '<EMAIL>'];
            }

            error_log("PhoneChatsController: Starting view for session ID: $sessionId");
            
            $sessionInfo = $this->getSessionInfo($sessionId);
            if (!$sessionInfo) {
                error_log("PhoneChatsController: Session $sessionId not found");
                header('Location: index.php?page=phone_chats&error=session_not_found');
                exit;
            }
            
            $messages = $this->getSessionMessages($sessionId);
            $phones = $this->getPhonesFromSession($sessionId);
            $digitalData = $this->getDigitalDataFromSession($sessionId);

            // Подробное логирование для диагностики
            error_log("PhoneChatsController: Session $sessionId - Messages count: " . count($messages));
            error_log("PhoneChatsController: Messages data: " . json_encode($messages));

            if (count($messages) > 0) {
                error_log("PhoneChatsController: First message: " . json_encode($messages[0]));
            }

            error_log("PhoneChatsController: Session $sessionId - Phones count: " . count($phones));

            $this->loadView('PhoneChats/view', [
                'session' => $sessionInfo,
                'messages' => $messages,
                'phones' => $phones,
                'digitalData' => $digitalData,
                'pageName' => 'phone_chats',
                'user' => $user,
                'controller' => $this
            ]);
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error in view: " . $e->getMessage());
            header('Location: index.php?page=phone_chats&error=load_failed');
            exit;
        }
    }

    /**
     * Удаление переписки
     */
    public function deleteChat()
    {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendJsonResponse(['success' => false, 'message' => 'Метод не поддерживается'], 405);
            return;
        }

        $sessionId = (int)($_POST['session_id'] ?? 0);
        if (!$sessionId) {
            $this->sendJsonResponse(['success' => false, 'message' => 'Неверный ID сессии']);
            return;
        }

        try {
            // Удаляем сообщения сессии - ИСПРАВЛЕНО для SQLite3
            $stmt = $this->db->prepare("DELETE FROM messages WHERE session_id = ?");
            $stmt->bindValue(1, $sessionId, SQLITE3_INTEGER);
            $stmt->execute();

            // Удаляем саму сессию - ИСПРАВЛЕНО для SQLite3
            $stmt = $this->db->prepare("DELETE FROM chat_sessions WHERE id = ?");
            $stmt->bindValue(1, $sessionId, SQLITE3_INTEGER);
            $stmt->execute();

            $this->sendJsonResponse(['success' => true, 'message' => 'Переписка удалена']);
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error deleting chat: " . $e->getMessage());
            $this->sendJsonResponse(['success' => false, 'message' => 'Ошибка при удалении']);
        }
    }

    /**
     * Массовое удаление переписок
     */
    public function deleteSelected()
    {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendJsonResponse(['success' => false, 'message' => 'Метод не поддерживается'], 405);
            return;
        }

        // Получаем данные из POST
        $sessionIdsJson = $_POST['session_ids'] ?? '';
        $sessionIds = json_decode($sessionIdsJson, true);

        if (empty($sessionIds) || !is_array($sessionIds)) {
            $this->sendJsonResponse(['success' => false, 'message' => 'Не выбраны переписки для удаления']);
            return;
        }

        try {
            $deletedCount = 0;
            foreach ($sessionIds as $sessionId) {
                $sessionId = (int)$sessionId;
                if ($sessionId > 0) {
                    // Удаляем сообщения - ИСПРАВЛЕНО для SQLite3
                    $stmt = $this->db->prepare("DELETE FROM messages WHERE session_id = ?");
                    $stmt->bindValue(1, $sessionId, SQLITE3_INTEGER);
                    $stmt->execute();

                    // Удаляем сессию - ИСПРАВЛЕНО для SQLite3
                    $stmt = $this->db->prepare("DELETE FROM chat_sessions WHERE id = ?");
                    $stmt->bindValue(1, $sessionId, SQLITE3_INTEGER);
                    $stmt->execute();
                    
                    $deletedCount++;
                }
            }

            $this->sendJsonResponse([
                'success' => true, 
                'message' => "Удалено переписок: $deletedCount"
            ]);
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error deleting selected chats: " . $e->getMessage());
            $this->sendJsonResponse(['success' => false, 'message' => 'Ошибка при удалении']);
        }
    }

    /**
     * Отправка уведомления о телефоне
     */
    public function sendNotification()
    {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendJsonResponse(['success' => false, 'message' => 'Метод не поддерживается'], 405);
            return;
        }

        $sessionId = (int)($_POST['session_id'] ?? 0);
        $phone = trim($_POST['phone'] ?? '');

        if (!$sessionId || !$phone) {
            $this->sendJsonResponse(['success' => false, 'message' => 'Неверные параметры']);
            return;
        }

        try {
            // Получаем сообщения сессии
            $messages = $this->getSessionMessages($sessionId);
            
            // Отправляем уведомление
            $result = $this->phoneService->sendPhoneNotification($phone, $messages, "manual_$sessionId");

            if ($result) {
                $this->sendJsonResponse(['success' => true, 'message' => 'Уведомление отправлено']);
            } else {
                $this->sendJsonResponse(['success' => false, 'message' => 'Ошибка отправки уведомления']);
            }
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error sending notification: " . $e->getMessage());
            $this->sendJsonResponse(['success' => false, 'message' => 'Ошибка при отправке']);
        }
    }

    /**
     * Получение переписок с телефонами - ИСПРАВЛЕННАЯ ВЕРСИЯ
     */
    private function getPhoneChats($search = '', $dateFrom = '', $dateTo = '', $limit = 20, $offset = 0)
    {
        try {
            // Сначала получаем все сессии с телефонами
            $sql = "
                SELECT DISTINCT
                    cs.id,
                    cs.session_uuid,
                    cs.title,
                    cs.created_at,
                    cs.updated_at
                FROM chat_sessions cs
                INNER JOIN messages m ON m.session_id = cs.id
                WHERE (
                    m.content LIKE '%+7%' OR
                    m.content LIKE '%8-%' OR
                    m.content LIKE '%(%' OR
                    m.content LIKE '%телефон%' OR
                    m.content LIKE '%номер%'
                )
            ";

            $conditions = [];
            $params = [];

            // Фильтр по дате
            if ($dateFrom) {
                $conditions[] = "DATE(cs.created_at) >= ?";
                $params[] = $dateFrom;
            }
            if ($dateTo) {
                $conditions[] = "DATE(cs.created_at) <= ?";
                $params[] = $dateTo;
            }

            // Поиск по тексту
            if ($search) {
                $conditions[] = "(cs.title LIKE ? OR m.content LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }

            if (!empty($conditions)) {
                $sql .= " AND " . implode(' AND ', $conditions);
            }

            $sql .= " ORDER BY cs.updated_at DESC LIMIT ? OFFSET ?";

            error_log("PhoneChatsController: SQL query: " . $sql);
            error_log("PhoneChatsController: Params: " . json_encode($params));

            $stmt = $this->db->prepare($sql);

            // Привязываем параметры для SQLite3
            $paramIndex = 1;
            foreach ($params as $param) {
                $stmt->bindValue($paramIndex++, $param, SQLITE3_TEXT);
            }
            $stmt->bindValue($paramIndex++, $limit, SQLITE3_INTEGER);
            $stmt->bindValue($paramIndex, $offset, SQLITE3_INTEGER);

            $result = $stmt->execute();
            $results = [];

            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                if ($row && is_array($row) && isset($row['id'])) {
                    // Получаем количество сообщений
                    $row['message_count'] = $this->getMessageCount($row['id']);

                    // Получаем телефоны из сообщений этой сессии
                    $row['phones'] = $this->getPhonesFromSession($row['id']);

                    // Получаем все цифровые данные из сообщений
                    $digitalData = $this->getDigitalDataFromSession($row['id']);
                    $row['digital_data'] = is_array($digitalData) ? $digitalData : [];

                    // Если нет названия, создаем его
                    if (empty($row['title'])) {
                        $row['title'] = 'Переписка #' . $row['id'];
                    }

                    $results[] = $row;
                    error_log("PhoneChatsController: Added chat: " . json_encode($row));
                }
            }

            error_log("PhoneChatsController: Total results: " . count($results));
            return $results;

        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error in getPhoneChats: " . $e->getMessage());
            error_log("PhoneChatsController: Error trace: " . $e->getTraceAsString());
            return [];
        }
    }

    /**
     * Получение количества сообщений в сессии
     */
    private function getMessageCount($sessionId)
    {
        try {
            // ИСПРАВЛЕНО: Используем числовой ID, как показала диагностика
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM messages WHERE session_id = ?");
            $stmt->bindValue(1, (int)$sessionId, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $row = $result->fetchArray(SQLITE3_ASSOC);
            return (int)$row['count'];
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error getting message count for session $sessionId: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Получение телефонов из сессии
     */
    private function getPhonesFromSession($sessionId)
    {
        try {
            // ИСПРАВЛЕНО: Используем числовой ID, как показала диагностика
            $sql = "SELECT content FROM messages WHERE session_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(1, (int)$sessionId, SQLITE3_INTEGER);
            $result = $stmt->execute();

            $phones = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                if ($row && is_array($row) && isset($row['content'])) {
                    $foundPhones = $this->extractPhonesFromText($row['content']);
                    $phones = array_merge($phones, $foundPhones);
                }
            }

            $uniquePhones = array_unique($phones);
            error_log("PhoneChatsController: Found phones for session $sessionId: " . json_encode($uniquePhones));
            return $uniquePhones;
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error getting phones for session $sessionId: " . $e->getMessage());
            return [];
        }
    }



    /**
     * Подсчет переписок с телефонами - УПРОЩЕННАЯ ВЕРСИЯ
     */
    private function getPhoneChatsCount($search = '', $dateFrom = '', $dateTo = '')
    {
        try {
            $sql = "
                SELECT COUNT(DISTINCT cs.id) as total
                FROM chat_sessions cs
                WHERE EXISTS (
                    SELECT 1 FROM messages m
                    WHERE m.session_id = cs.id
                    AND (m.content LIKE '%+7%' OR m.content LIKE '%8-%' OR m.content LIKE '%(%')
                )
            ";

            $conditions = [];
            $params = [];

            if ($dateFrom) {
                $conditions[] = "DATE(cs.created_at) >= ?";
                $params[] = $dateFrom;
            }
            if ($dateTo) {
                $conditions[] = "DATE(cs.created_at) <= ?";
                $params[] = $dateTo;
            }
            if ($search) {
                $conditions[] = "EXISTS (SELECT 1 FROM messages WHERE session_id = cs.id AND content LIKE ?)";
                $params[] = "%$search%";
            }

            if (!empty($conditions)) {
                $sql .= " AND " . implode(' AND ', $conditions);
            }

            $stmt = $this->db->prepare($sql);

            $paramIndex = 1;
            foreach ($params as $param) {
                $stmt->bindValue($paramIndex++, $param, SQLITE3_TEXT);
            }

            $result = $stmt->execute();
            $row = $result->fetchArray(SQLITE3_ASSOC);

            return (int)$row['total'];
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Получение статистики по телефонам - УПРОЩЕННАЯ ВЕРСИЯ
     */
    private function getPhoneStats()
    {
        try {
            // Простая статистика без сложных запросов
            $sql1 = "
                SELECT COUNT(DISTINCT cs.id) as total_chats_with_phones
                FROM chat_sessions cs
                WHERE EXISTS (
                    SELECT 1 FROM messages m
                    WHERE m.session_id = cs.id
                    AND (m.content LIKE '%+7%' OR m.content LIKE '%8-%' OR m.content LIKE '%(%')
                )
            ";

            $stmt1 = $this->db->prepare($sql1);
            $result1 = $stmt1->execute();
            $row1 = $result1->fetchArray(SQLITE3_ASSOC);

            $sql2 = "
                SELECT COUNT(DISTINCT DATE(cs.created_at)) as days_with_phones
                FROM chat_sessions cs
                WHERE EXISTS (
                    SELECT 1 FROM messages m
                    WHERE m.session_id = cs.id
                    AND (m.content LIKE '%+7%' OR m.content LIKE '%8-%' OR m.content LIKE '%(%')
                )
            ";

            $stmt2 = $this->db->prepare($sql2);
            $result2 = $stmt2->execute();
            $row2 = $result2->fetchArray(SQLITE3_ASSOC);

            $sql3 = "
                SELECT COUNT(*) as total_messages_with_phones
                FROM messages
                WHERE content LIKE '%+7%' OR content LIKE '%8-%' OR content LIKE '%(%'
            ";

            $stmt3 = $this->db->prepare($sql3);
            $result3 = $stmt3->execute();
            $row3 = $result3->fetchArray(SQLITE3_ASSOC);

            return [
                'total_chats_with_phones' => (int)$row1['total_chats_with_phones'],
                'days_with_phones' => (int)$row2['days_with_phones'],
                'total_messages_with_phones' => (int)$row3['total_messages_with_phones']
            ];
        } catch (\Exception $e) {
            return [
                'total_chats_with_phones' => 0,
                'days_with_phones' => 0,
                'total_messages_with_phones' => 0
            ];
        }
    }


    /**
     * Получение всех сообщений сессии
     */
    private function getSessionMessages($sessionId)
    {
        try {
            $sql = "SELECT * FROM messages WHERE session_id = ? ORDER BY timestamp ASC";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(1, (int)$sessionId, SQLITE3_INTEGER);
            $result = $stmt->execute();

            $messages = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                if ($row && is_array($row)) {
                    $messages[] = $row;
                }
            }

            return $messages;
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error in getSessionMessages: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Получение информации о сессии с проверкой существования
     */
    private function getSessionInfo($sessionId)
    {
        try {
            $sql = "SELECT * FROM chat_sessions WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(1, (int)$sessionId, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $session = $result->fetchArray(SQLITE3_ASSOC);

            if (!$session) {
                error_log("PhoneChatsController: Session $sessionId not found in chat_sessions table");

                // Дополнительная диагностика
                $debugStmt = $this->db->prepare("SELECT COUNT(*) as total FROM chat_sessions");
                $debugResult = $debugStmt->execute();
                $debugRow = $debugResult->fetchArray(SQLITE3_ASSOC);
                error_log("PhoneChatsController: Total sessions in database: " . $debugRow['total']);

                return null;
            }

            // ИСПРАВЛЕНО: Правильная привязка параметра как INTEGER
            $sql = "SELECT COUNT(*) as message_count FROM messages WHERE session_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(1, (int)$sessionId, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $row = $result->fetchArray(SQLITE3_ASSOC);

            $session['message_count'] = (int)$row['message_count'];

            error_log("PhoneChatsController: Session $sessionId found with {$session['message_count']} messages");
            return $session;
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error in getSessionInfo: " . $e->getMessage());
            error_log("PhoneChatsController: Stack trace: " . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Извлечение телефонов из текста
     */
    private function extractPhonesFromText($text)
    {
        // Проверяем, что текст не null и не пустой
        if (empty($text) || !is_string($text)) {
            return [];
        }

        $phones = [];
        // Improved regex patterns to match more phone number formats
        $patterns = [
            '/\+?7\s*[\(\-]?\s*\d{3}\s*[\)\-]?\s*\d{3}\s*[\-]?\s*\d{2}\s*[\-]?\s*\d{2}/',
            '/8\s*[\(\-]?\s*\d{3}\s*[\)\-]?\s*\d{3}\s*[\-]?\s*\d{2}\s*[\-]?\s*\d{2}/',
            '/\+?7\s*\d{3}\s*\d{3}\s*\d{2}\s*\d{2}/',
            '/\d{11}/',
            '/\+7\(\d{3}\)\d{3}-\d{2}-\d{2}/',
            '/\d{1}\s*\d{3}\s*\d{3}\s*\d{2}\s*\d{2}/',
            '/\(\d{3}\)\s*\d{3}[\s-]?\d{2}[\s-]?\d{2}/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                $phones = array_merge($phones, $matches[0]);
            }
        }

        // Clean and format phone numbers
        $cleanedPhones = [];
        foreach ($phones as $phone) {
            // Remove non-digit characters except leading +
            $cleanPhone = preg_replace('/[^\d\+]/', '', $phone);
            
            // If phone starts with 8, replace with +7
            if (strpos($cleanPhone, '8') === 0 && strlen($cleanPhone) === 11) {
                $cleanPhone = '+7' . substr($cleanPhone, 1);
            }
            // If phone has 10 digits, add +7
            elseif (strlen($cleanPhone) === 10) {
                $cleanPhone = '+7' . $cleanPhone;
            }
            // If phone has 11 digits without country code, format as +7
            elseif (strlen($cleanPhone) === 11 && strpos($cleanPhone, '7') === 0) {
                $cleanPhone = '+' . $cleanPhone;
            }
            
            $cleanedPhones[] = $cleanPhone;
        }

        return array_unique($cleanedPhones);
    }

    /**
     * Получение всех цифровых данных из сессии
     */
    private function getDigitalDataFromSession($sessionId)
    {
        try {
            $sql = "SELECT content FROM messages WHERE session_id = ? LIMIT 50"; // Ограничиваем количество сообщений
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(1, (int)$sessionId, SQLITE3_INTEGER);
            $result = $stmt->execute();

            $digitalData = [];
            $messageCount = 0;
            while (($row = $result->fetchArray(SQLITE3_ASSOC)) && $messageCount < 20) { // Максимум 20 сообщений
                if ($row && is_array($row) && isset($row['content'])) {
                    $foundData = $this->extractDigitalDataFromText($row['content']);
                    $digitalData = array_merge($digitalData, $foundData);
                }
                $messageCount++;
            }

            // Убираем дубликаты по значению и типу
            $uniqueData = [];
            $seen = [];
            foreach ($digitalData as $item) {
                $key = $item['type'] . '|' . $item['value'];
                if (!isset($seen[$key])) {
                    $seen[$key] = true;
                    $uniqueData[] = $item;
                }
            }

            return $uniqueData;
        } catch (\Exception $e) {
            error_log("PhoneChatsController: Error getting digital data for session $sessionId: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Извлечение всех цифровых данных из текста
     */
    private function extractDigitalDataFromText($text)
    {
        // Проверяем, что текст не null и не пустой
        if (empty($text) || !is_string($text)) {
            return [];
        }

        // Упрощенная версия для быстрой работы
        $digitalData = [];

        // Только основные паттерны для телефонов
        $phonePatterns = [
            '/\b\d{10,11}[а-яА-Я]?\b/u', // 10-11 цифр подряд + возможная буква
            '/\+?7\s*\d{3}\s*\d{3}\s*\d{2}\s*\d{2}/',
            '/8\s*\d{3}\s*\d{3}\s*\d{2}\s*\d{2}/',
        ];

        foreach ($phonePatterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                foreach ($matches[0] as $match) {
                    $trimmedMatch = trim($match);
                    if (!empty($trimmedMatch) && strlen($trimmedMatch) >= 10) {
                        $digitalData[] = [
                            'type' => 'phones',
                            'value' => $trimmedMatch,
                            'context' => substr($text, 0, 50) . '...'
                        ];
                    }
                }
            }
        }

        // Простые числа
        if (preg_match_all('/\b\d{4,8}\b/', $text, $matches)) {
            foreach ($matches[0] as $match) {
                if (strlen($match) < 10) { // Не телефоны
                    $digitalData[] = [
                        'type' => 'numbers',
                        'value' => $match,
                        'context' => substr($text, 0, 50) . '...'
                    ];
                }
            }
        }

        // Коды (буквы и цифры)
        if (preg_match_all('/\b[A-Z0-9]{4,8}\b/', $text, $matches)) {
            foreach ($matches[0] as $match) {
                $digitalData[] = [
                    'type' => 'codes',
                    'value' => $match,
                    'context' => substr($text, 0, 50) . '...'
                ];
            }
        }

        // Даты
        if (preg_match_all('/\b\d{1,2}[\.\/\-]\d{1,2}[\.\/\-]\d{2,4}\b/', $text, $matches)) {
            foreach ($matches[0] as $match) {
                $digitalData[] = [
                    'type' => 'dates',
                    'value' => $match,
                    'context' => substr($text, 0, 50) . '...'
                ];
            }
        }

        // Время
        if (preg_match_all('/\b\d{1,2}:\d{2}(?::\d{2})?\b/', $text, $matches)) {
            foreach ($matches[0] as $match) {
                $digitalData[] = [
                    'type' => 'time',
                    'value' => $match,
                    'context' => substr($text, 0, 50) . '...'
                ];
            }
        }

        // Деньги
        if (preg_match_all('/\b\d+(?:\.\d{2})?\s*(?:руб|₽|rub|р)\b/i', $text, $matches)) {
            foreach ($matches[0] as $match) {
                $digitalData[] = [
                    'type' => 'money',
                    'value' => $match,
                    'context' => substr($text, 0, 50) . '...'
                ];
            }
        }

        return array_slice($digitalData, 0, 15); // Увеличиваем лимит
    }

    /**
     * Получение контекста для найденного значения
     */
    private function getContext($text, $value, $contextLength = 30)
    {
        if (empty($text) || !is_string($text)) {
            return '';
        }
        return substr($text, 0, 50) . '...'; // Упрощенная версия для быстрой работы
    }

    /**
     * Получение иконки для типа данных
     */
    public function getIconForType($type)
    {
        $icons = [
            'phones' => 'phone',
            'numbers' => 'hashtag',
            'codes' => 'barcode',
            'dates' => 'calendar',
            'time' => 'clock',
            'money' => 'ruble-sign'
        ];

        return $icons[$type] ?? 'question';
    }

    /**
     * Получение метки для типа данных
     */
    public function getTypeLabel($type)
    {
        $labels = [
            'phones' => 'Телефоны',
            'numbers' => 'Числа',
            'codes' => 'Коды',
            'dates' => 'Даты',
            'time' => 'Время',
            'money' => 'Суммы'
        ];

        return $labels[$type] ?? ucfirst($type);
    }
}
