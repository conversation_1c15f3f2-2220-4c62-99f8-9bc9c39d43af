CREATE TABLE IF NOT EXISTS texts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE NOT NULL,
    value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Триггер для обновления updated_at при изменении записи
CREATE TRIGGER IF NOT EXISTS update_text_timestamp
AFTER UPDATE ON texts
FOR EACH ROW
BEGIN
    UPDATE texts SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;
