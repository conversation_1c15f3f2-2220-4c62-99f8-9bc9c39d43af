<?php

namespace App\Controllers;

use App\Models\Message;
use App\Models\User; // Для получения данных текущего пользователя

class MessagesController extends BaseController {

    private const MESSAGES_PER_PAGE = 20;

    /**
     * Отображает страницу истории сообщений с фильтрами и пагинацией.
     */
    public function index(): void {
        $this->checkAuth();
        $messageModel = new Message();
        $userModel = new User();

        // Получаем параметры фильтрации и пагинации из GET-запроса
        $filters = [
            // Используем FILTER_UNSAFE_RAW и htmlspecialchars позже или в представлении,
            // т.к. user_id может быть строкой 'anon_...'
            'userId' => filter_input(INPUT_GET, 'user_id', FILTER_UNSAFE_RAW),
            'sessionId' => filter_input(INPUT_GET, 'session_id', FILTER_VALIDATE_INT),
            'search' => filter_input(INPUT_GET, 'search', FILTER_UNSAFE_RAW), // Экранировать при выводе
            'dateFrom' => filter_input(INPUT_GET, 'date_from', FILTER_UNSAFE_RAW), // Проверять формат даты, если нужно
            'dateTo' => filter_input(INPUT_GET, 'date_to', FILTER_UNSAFE_RAW),   // Проверять формат даты, если нужно
        ];
        // Удаляем пустые значения из фильтров (null, false, '', 0)
        // filter_var может вернуть false для 0, если нужен 0, используем !== null
        $filters = array_filter($filters, fn($value) => $value !== null && $value !== '');

        $currentPage = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT, ['options' => ['default' => 1, 'min_range' => 1]]);

        // Получаем данные для отображения
        $messageData = $messageModel->getMessages($filters, $currentPage, self::MESSAGES_PER_PAGE);
        // Получаем ID текущего пользователя (предполагается, что это админ)
        $currentUserId = $this->getUserId();
        $filterData = $messageModel->getFilterData($currentUserId);

        // Рассчитываем пагинацию
        $totalMessages = $messageData['totalMessages'];
        $totalPages = $totalMessages > 0 ? (int)ceil($totalMessages / self::MESSAGES_PER_PAGE) : 1;
        // Коррекция, если текущая страница больше максимальной
        if ($currentPage > $totalPages) {
             $currentPage = $totalPages;
             // Можно было бы перезапросить данные для последней страницы, но пока оставим так
        }


        // Обработка сообщений из GET-параметров (успех/ошибка)
        $flashMessage = null;
        $flashType = 'info'; // Тип по умолчанию
        if (isset($_GET['success'])) {
            $flashType = 'success';
            switch ($_GET['success']) {
                case 'message_deleted':
                    $flashMessage = 'Сообщение успешно удалено.';
                    break;
                case 'chat_deleted':
                    $flashMessage = 'Чат успешно удален.';
                    break;
                 case 'messages_deleted': // Добавим сообщение для массового удаления
                     $deletedCount = filter_input(INPUT_GET, 'count', FILTER_VALIDATE_INT);
                     $flashMessage = $deletedCount !== null
                         ? "Успешно удалено сообщений: " . $deletedCount . "."
                         : "Выбранные сообщения успешно удалены.";
                     break;
            }
        } elseif (isset($_GET['error'])) {
            $flashType = 'error';
            switch ($_GET['error']) {
                case 'delete_failed':
                    $flashMessage = 'Ошибка удаления сообщения.';
                    break;
                case 'chat_delete_failed':
                    $flashMessage = 'Ошибка удаления чата.';
                    break;
                case 'invalid_request':
                    $flashMessage = 'Некорректный запрос.';
                    break;
                 case 'delete_selected_failed': // Добавим сообщение для массового удаления
                     $flashMessage = 'Ошибка при удалении выбранных сообщений. Проверьте логи.';
                     break;
            }
        }

        // Экранируем фильтры перед передачей в представление для безопасности
        $safeFilters = array_map('htmlspecialchars', $filters);

        $this->loadView('messages', [
            'user' => $userModel->getUserById($currentUserId), // Данные для header
            'messages' => $messageData['messages'],
            'filterData' => $filterData, // Данные для селектов фильтров
            'currentFilters' => $safeFilters, // Текущие БЕЗОПАСНЫЕ значения фильтров для формы
            'currentPage' => $currentPage,
            'totalPages' => $totalPages,
            'perPage' => self::MESSAGES_PER_PAGE,
            'totalMessages' => $totalMessages,
            'flashMessage' => $flashMessage,
            'flashType' => $flashType,
            'pageName' => 'messages' // Для подсветки меню в header
        ]);
    }

    /**
     * Обрабатывает удаление одного сообщения (POST-запрос из формы).
     */
    public function deleteMessage(): void {
        $this->checkAuth();
        // TODO: Добавить CSRF проверку

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirectBackWithError('invalid_request', $_GET); // Передаем GET параметры
            return; // Добавляем return для ясности
        }

        $messageId = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);

        if (!$messageId || $messageId <= 0) {
             $this->redirectBackWithError('invalid_request', $_GET);
             return;
        }

        $messageModel = new Message();
        if ($messageModel->deleteMessage($messageId)) {
            // Успех - редирект обратно с сообщением об успехе
            $queryString = http_build_query($_GET); // Сохраняем GET параметры
            header('Location: index.php?' . $queryString . '&success=message_deleted');
            exit;
        } else {
            // Ошибка - редирект обратно с сообщением об ошибке
             $this->redirectBackWithError('delete_failed', $_GET);
             return;
        }
    }

     /**
     * Обрабатывает удаление чат-сессии (AJAX POST-запрос).
     * Возвращает JSON ответ.
     */
    public function deleteChatSession(): void {
        $this->checkAuth();
        // TODO: Добавить CSRF проверку

        header('Content-Type: application/json'); // Устанавливаем заголовок в начале

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405); // Method Not Allowed
            echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
            exit;
        }

        // Получаем ID из POST-данных формы (не JSON)
        $sessionId = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);

        if (!$sessionId || $sessionId <= 0) {
            http_response_code(400); // Bad Request
            echo json_encode(['success' => false, 'error' => 'Invalid session ID.']);
            exit;
        }

        $messageModel = new Message();
        $result = $messageModel->deleteChatSession($sessionId);

        if ($result !== false) {
            echo json_encode(['success' => true, 'stats' => $result]);
        } else {
            http_response_code(500); // Internal Server Error
            // Не выводим детальную ошибку пользователю, но логируем ее в модели
            echo json_encode(['success' => false, 'error' => 'Failed to delete chat session.']);
        }
        exit;

        // Старый код для не-AJAX запросов был убран, т.к. предполагается AJAX
    }

    /**
     * Обрабатывает удаление выбранных сообщений (AJAX POST-запрос).
     * Ожидает JSON с массивом 'ids' в теле запроса.
     * Возвращает JSON ответ.
     */
    public function deleteSelectedMessages(): void {
        $this->checkAuth();
        // TODO: Добавить CSRF проверку

        header('Content-Type: application/json'); // Устанавливаем заголовок

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405); // Method Not Allowed
            echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
            exit;
        }

        // Получаем JSON из тела запроса
        $inputJSON = file_get_contents('php://input');
        $input = json_decode($inputJSON, TRUE); // TRUE для ассоциативного массива

        // Проверка на ошибку декодирования JSON
        if (json_last_error() !== JSON_ERROR_NONE) {
             http_response_code(400); // Bad Request
             echo json_encode(['success' => false, 'error' => 'Invalid JSON input: ' . json_last_error_msg()]);
             exit;
        }

        $messageIds = $input['ids'] ?? null;

        // Валидация входных данных
        if (!is_array($messageIds)) { // Проверяем, что это массив (может быть пустым)
            http_response_code(400); // Bad Request
            echo json_encode(['success' => false, 'error' => 'Invalid data format: "ids" key must be an array.']);
            exit;
        }
        if (empty($messageIds)) {
             // Считаем успешным удалением 0 сообщений
             echo json_encode(['success' => true, 'deleted_count' => 0]);
             exit;
        }


        // Валидация каждого ID в массиве
        $validatedIds = [];
        foreach ($messageIds as $id) {
            // Проверяем, что это строка с цифрами или целое число
            if (!is_int($id) && !(is_string($id) && ctype_digit($id))) {
                 http_response_code(400); // Bad Request
                 echo json_encode(['success' => false, 'error' => 'Invalid message ID found in array: ' . htmlspecialchars(print_r($id, true))]);
                 exit;
            }
            // Приводим к int для передачи в модель
            $validatedIds[] = (int)$id;
        }

        // На случай, если после валидации массив стал пустым (хотя проверка выше должна это отсечь)
        if (empty($validatedIds)) {
             http_response_code(400); // Bad Request
             echo json_encode(['success' => false, 'error' => 'No valid message IDs provided after validation.']);
             exit;
        }

        $messageModel = new Message();
        // --- ИСПРАВЛЕНО: Вызываем deleteMessagesByIdList ---
        // Этот метод удаляет сообщения по списку ID без учета сессии
        $deletedCount = $messageModel->deleteMessagesByIdList($validatedIds);
        // --- КОНЕЦ ИСПРАВЛЕНИЯ ---

        if ($deletedCount !== false) {
            // Успех - возвращаем количество удаленных
            echo json_encode(['success' => true, 'deleted_count' => $deletedCount]);
        } else {
            // Ошибка (модель должна была залогировать детали)
            http_response_code(500); // Internal Server Error
            // Не передаем детали ошибки клиенту
            echo json_encode(['success' => false, 'error' => 'Failed to delete selected messages.']);
        }
        exit;
   }


    /**
     * Вспомогательный метод для редиректа назад с сообщением об ошибке.
     * @param string $errorCode Код ошибки для GET-параметра.
     * @param array $existingQueryData Текущие GET параметры для сохранения.
     */
    private function redirectBackWithError(string $errorCode, array $existingQueryData = []): void {
         // Убираем предыдущие параметры success/error, чтобы не дублировались
         unset($existingQueryData['success']);
         unset($existingQueryData['error']);
         unset($existingQueryData['count']); // Убираем count от предыдущего успеха

         // Добавляем новый параметр ошибки
         $existingQueryData['error'] = $errorCode;

         $queryString = http_build_query($existingQueryData);
         header('Location: index.php?' . $queryString);
         exit;
    }

    /**
     * Очищает все чаты, все сообщения и всех пользователей (кроме админа?).
     * Вызывается через AJAX POST-запрос (предположительно).
     * Возвращает JSON ответ.
     */
    public function clearAll(): void {
        $this->checkAuth(); // Проверяем авторизацию админа
        // TODO: Добавить CSRF проверку

        header('Content-Type: application/json'); // Ожидаем JSON ответ

        // Проверка, что запрос POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405); // Method Not Allowed
            echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
            exit;
        }

        try {
            $db = \App\Models\Database::getInstance();
            $adminId = $this->getUserId(); // Получаем ID текущего админа

            // Начинаем транзакцию для атомарности
            $db->exec('BEGIN');

            // 1. Удаляем все файлы (если есть таблица и связь)
            // Предположим, что модель UploadedFile имеет метод deleteAll()
            if (class_exists(\App\Models\UploadedFile::class)) {
                 $fileModel = new \App\Models\UploadedFile();
                 if (method_exists($fileModel, 'deleteAll')) {
                     // Здесь должна быть логика удаления файлов из ФС внутри deleteAll
                     if (!$fileModel->deleteAll()) {
                          throw new \Exception("Failed to delete uploaded files.");
                     }
                 } else {
                      // Если нет массового удаления, удаляем по одному (менее эффективно)
                      // Или удаляем записи напрямую
                      if (!$db->exec('DELETE FROM uploaded_files')) {
                           throw new \Exception("Failed to clear uploaded_files table: " . $db->lastErrorMsg());
                      }
                      // Примечание: файлы в ФС останутся без этого! Нужна доп. логика.
                 }
            }

            // 2. Удаляем все сообщения
            if (!$db->exec('DELETE FROM messages')) {
                 throw new \Exception("Failed to clear messages table: " . $db->lastErrorMsg());
            }

            // 3. Удаляем все сессии
            if (!$db->exec('DELETE FROM chat_sessions')) {
                  throw new \Exception("Failed to clear chat_sessions table: " . $db->lastErrorMsg());
            }

            // 4. Удаляем всех пользователей, КРОМЕ текущего админа
            $userStmt = $db->prepare('DELETE FROM users WHERE id != :admin_id');
            if (!$userStmt) {
                 throw new \Exception("Failed to prepare delete users statement: " . $db->lastErrorMsg());
            }
            // Определяем тип adminId
            $adminIdType = (ctype_digit((string)$adminId)) ? \SQLITE3_INTEGER : \SQLITE3_TEXT;
            $userStmt->bindValue(':admin_id', $adminId, $adminIdType);
            if (!$userStmt->execute()) {
                 $errorMsg = $db->lastErrorMsg();
                 $userStmt->close();
                 throw new \Exception("Failed to delete users: " . $errorMsg);
            }
            $userStmt->close();

            // Если все успешно, коммитим транзакцию
            if (!$db->exec('COMMIT')) {
                 throw new \Exception("Failed to commit transaction: " . $db->lastErrorMsg());
            }

            echo json_encode(['success' => true, 'message' => 'All data cleared successfully (except current admin).']);

        } catch (\Throwable $e) {
             // Если произошла ошибка, откатываем транзакцию
             if (isset($db) && $db->inTransaction()) {
                 $db->exec('ROLLBACK');
             }
            error_log("Clear All Error: " . $e->getMessage()); // Логируем ошибку
            http_response_code(500); // Internal Server Error
            echo json_encode(['success' => false, 'error' => 'An error occurred during cleanup. Check server logs.']);
        }
        exit;
    }
}