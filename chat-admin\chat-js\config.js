// --- START OF FILE config.js ---
// chat-js/config.js
// (ИСПРАВЛЕННАЯ ВЕРСИЯ: Получает базовый URL при инициализации)

let config = {
    chatAdminBaseUrl: null,
    apiBaseUrl: null,
    phpScriptBaseUrl: null,
    userId: null
};

/**
 * Инициализирует конфигурацию, устанавливая базовые URL.
 * Вызывается из main.js после получения URL от chat-loader.js.
 * @param {string} chatAdminBaseUrl - Правильный базовый URL (например, 'https://uniqpaid.com/yurist2/chat-admin/').
 */
export function initializeConfig(chatAdminBaseUrl) {
    if (!chatAdminBaseUrl || typeof chatAdminBaseUrl !== 'string') {
        console.error('[config.js] ОШИБКА: Попытка инициализировать конфиг без валидного chatAdminBaseUrl!');
        throw new Error("Invalid chatAdminBaseUrl provided to initializeConfig");
    }
    // Гарантируем слеш в конце
     if (!chatAdminBaseUrl.endsWith('/')) {
        chatAdminBaseUrl += '/';
    }

    config.chatAdminBaseUrl = chatAdminBaseUrl;
    // Формируем абсолютные URL на основе переданного базового
    config.apiBaseUrl = `${config.chatAdminBaseUrl}simple-cors.php?page=api`;
    config.phpScriptBaseUrl = config.chatAdminBaseUrl;

    // --- Логика User ID (остается без изменений) ---
    let userIdFromStorage = localStorage.getItem('chat_user_id');
    if (!userIdFromStorage) {
        const randomPart = Math.random().toString(36).substring(2, 11);
        const timePart = Date.now().toString(36);
        userIdFromStorage = `user_${timePart}_${randomPart}`;
        try {
            localStorage.setItem('chat_user_id', userIdFromStorage);
        } catch (e) {
            console.warn("[config.js] Не удалось сохранить chat_user_id в localStorage. Чат может работать некорректно между сессиями.", e);
        }
    }
    config.userId = userIdFromStorage;

    console.log(`[config.js] Конфигурация ИНИЦИАЛИЗИРОВАНА.`);
    console.log(`[config.js]   chatAdminBaseUrl: ${config.chatAdminBaseUrl}`);
    console.log(`[config.js]   API_BASE_URL установлен в: ${config.apiBaseUrl}`);
    console.log(`[config.js]   PHP_SCRIPT_BASE_URL установлен в: ${config.phpScriptBaseUrl}`);
    console.log(`[config.js]   User ID: ${config.userId}`);
}

// --- Функции-геттеры для доступа к конфигурации ---
export function getChatAdminBaseUrl() {
    if (!config.chatAdminBaseUrl) console.warn("[config.js] getChatAdminBaseUrl() вызван до инициализации!");
    return config.chatAdminBaseUrl;
}
export function getApiBaseUrl() {
    if (!config.apiBaseUrl) console.warn("[config.js] getApiBaseUrl() вызван до инициализации!");
    return config.apiBaseUrl;
}
export function getPhpScriptBaseUrl() {
    if (!config.phpScriptBaseUrl) console.warn("[config.js] getPhpScriptBaseUrl() вызван до инициализации!");
    return config.phpScriptBaseUrl;
}
export function getUserId() {
    if (!config.userId) console.warn("[config.js] getUserId() вызван до инициализации!");
    return config.userId;
}

// --- Остальные константы (остаются без изменений) ---
export const VOICE_CHAT_SESSION_TITLE = 'Голосовой чат';
export const SESSION_TITLE_MAX_LENGTH = 25;
export const HIDE_BUTTON_DELAY = 3000;
export const SPEECH_RECOGNITION_LANG = 'ru-RU';
export const DEFAULT_TTS_VOICE = 'ru-RU-SvetlanaNeural'; // Пример голоса Azure
export const RESPONSE_TIMEOUT_MS = 60000; // 60 секунд таймаут ответа

// --- END OF FILE config.js ---