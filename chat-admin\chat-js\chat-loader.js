// --- START OF FILE chat-loader.js ---
(function () {
  console.log("[ChatLoader] Скрипт загрузчика запущен.");

  // --- Определяем базовый путь скрипта ---
  let loaderScriptSrc = "";
  if (document.currentScript) {
      loaderScriptSrc = document.currentScript.src;
      console.log("[ChatLoader] URL текущего скрипта:", loaderScriptSrc);
  } else {
      // Fallback
      const scripts = document.getElementsByTagName('script');
      const loaderScriptName = 'chat-loader.js';
      for (let i = scripts.length - 1; i >= 0; i--) {
          if (scripts[i].src && scripts[i].src.includes(loaderScriptName)) {
              loaderScriptSrc = scripts[i].src;
              console.log("[ChatLoader] URL скрипта определен через поиск тегов:", loaderScriptSrc);
              break;
          }
      }
  }

  if (!loaderScriptSrc) {
    console.error(
      "[ChatLoader] КРИТИЧЕСКАЯ ОШИБКА: Не удалось определить URL скрипта загрузчика. Загрузка плагина невозможна."
    );
    try {
      const errorDiv = document.createElement('div');
      errorDiv.textContent = 'Ошибка загрузки компонента чата (не найден URL загрузчика).';
      errorDiv.style.cssText = 'position:fixed; bottom:10px; left:10px; padding:10px; background:red; color:white; z-index:10001;';
      document.body.appendChild(errorDiv);
    } catch(e) {}
    return;
  }

  // --- Извлекаем базовый URL до папки chat-admin/ ---
  // ЭТА ПЕРЕМЕННАЯ ДОЛЖНА СОДЕРЖАТЬ ПРАВИЛЬНЫЙ ПУТЬ С test-chat4
  let chatAdminBaseUrl = "";
  try {
      const url = new URL(loaderScriptSrc);
      const pathParts = url.pathname.split('/');
      let chatAdminIndex = -1;
       for (let i = pathParts.length - 2; i >= 0; i--) {
           if (pathParts[i] === 'chat-admin') {
               chatAdminIndex = i;
               break;
           }
       }
       if (chatAdminIndex === -1) {
          throw new Error("Не удалось найти сегмент 'chat-admin' в пути скрипта: " + url.pathname);
       }
      const basePath = pathParts.slice(0, chatAdminIndex + 1).join('/');
      chatAdminBaseUrl = `${url.origin}${basePath}/`;
      // Убираем двойные слеши на всякий случай (кроме как после http:)
      chatAdminBaseUrl = chatAdminBaseUrl.replace(/([^:]\/)\/+/g, "$1");

  } catch (e) {
      console.error("[ChatLoader] КРИТИЧЕСКАЯ ОШИБКА при определении базового URL:", e, "Используемый URL скрипта:", loaderScriptSrc);
      try {
          const errorDiv = document.createElement('div');
          errorDiv.textContent = 'Ошибка загрузки компонента чата (неверный URL).';
          errorDiv.style.cssText = 'position:fixed; bottom:10px; left:10px; padding:10px; background:red; color:white; z-index:10001;';
          document.body.appendChild(errorDiv);
      } catch(err) {}
      return;
  }

  // Гарантируем слеш в конце
  if (!chatAdminBaseUrl.endsWith('/')) {
      chatAdminBaseUrl += '/';
  }
  // --- ЛОГИРОВАНИЕ ПРАВИЛЬНОГО БАЗОВОГО URL ---
  // Убедитесь, что здесь в консоли отображается: https://uniqpaid.com/test-chat4/chat-admin/
  console.log(`[ChatLoader] ИТОГОВЫЙ Базовый URL для ресурсов определен: ${chatAdminBaseUrl}`);
  // --- ---

  const cssPath = chatAdminBaseUrl + "css-clean.php";
  const dynamicCssPath = chatAdminBaseUrl + "simple-cors.php?page=api&action=getDynamicCss&v=" + Date.now();
  const htmlTemplatePath = chatAdminBaseUrl + "chat-template-prefixed.html";
  const mainScriptImportPath = chatAdminBaseUrl + "chat-js/main.js?v=" + Date.now();
  const baseElementId = "znak-chat-plugin-base";

  // --- Функция для проверки и загрузки Font Awesome ---
  function ensureFontAwesome() {
    return new Promise((resolve) => {
      // Проверяем, загружен ли уже Font Awesome
      const existingFontAwesome = document.querySelector('link[href*="font-awesome"]') ||
                                 document.querySelector('link[href*="fontawesome"]');

      if (existingFontAwesome) {
        console.log("[ChatLoader] Font Awesome найден на странице");
        resolve();
        return;
      }

      console.log("[ChatLoader] Font Awesome не найден, загружаем...");
      loadCss("https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css")
        .then(() => {
          console.log("[ChatLoader] Font Awesome успешно загружен");
          resolve();
        })
        .catch((error) => {
          console.error("[ChatLoader] Не удалось загрузить Font Awesome:", error);
          // Пробуем альтернативный CDN
          console.log("[ChatLoader] Пробуем альтернативный CDN для Font Awesome...");
          loadCss("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css")
            .then(() => {
              console.log("[ChatLoader] Font Awesome успешно загружен с альтернативного CDN");
              resolve();
            })
            .catch((error2) => {
              console.error("[ChatLoader] Не удалось загрузить Font Awesome с альтернативного CDN:", error2);
              resolve(); // Не блокируем загрузку чата
            });
        });
    });
  }

  // --- Вспомогательные функции (loadCss, loadHtml, injectHtml - без изменений) ---
  function loadCss(url) {
    return new Promise((resolve, reject) => {
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = url;
      link.setAttribute('crossorigin', 'anonymous');
      link.onload = () => {
        console.log(`[ChatLoader] CSS успешно загружен: ${url}`);
        resolve();
      };
      link.onerror = (errorEvent) => {
        console.error(`[ChatLoader] Ошибка загрузки CSS: ${url}`, errorEvent);
        const error = new Error(`Ошибка загрузки CSS: ${url}. Возможные причины: файл не найден (404), ошибка сервера или проблема с CORS.`);
        reject(error);
      };
      document.head.appendChild(link);
    });
  }

  function loadHtml(url) {
    console.log(`[ChatLoader] Запрос HTML шаблона: ${url}`);
    return fetch(url, { mode: 'cors' })
      .then((response) => {
        console.log(`[ChatLoader] Получен ответ для HTML (${response.status}): ${url}`);
        if (!response.ok) {
            throw new Error(
                `HTTP ошибка! Статус: ${response.status} ${response.statusText} при загрузке HTML: ${url}. Убедитесь, что файл существует и сервер настроен для CORS для этого пути.`
            );
        }
        return response.text();
      })
      .catch((error) => {
        console.error(`[ChatLoader] Ошибка при fetch HTML шаблона: ${url}`, error);
        throw error;
      });
  }

  function injectHtml(htmlContent) {
    let baseElement = document.getElementById(baseElementId);
    if (!baseElement) {
      baseElement = document.createElement("div");
      baseElement.id = baseElementId;
      document.body.appendChild(baseElement);
      console.log(`[ChatLoader] Создан базовый элемент для плагина с ID: ${baseElementId}`);
    } else {
        console.log(`[ChatLoader] Найден существующий базовый элемент с ID: ${baseElementId}`);
    }
    baseElement.innerHTML = htmlContent;
  }


  /**
   * Извлекает релевантную часть пути (например, 'images/darya.svg') из URL/пути от API.
   * @param {string} urlString potentially incorrect URL or path string from API
   * @returns {string} The extracted relative path or empty string.
   */
   function extractRelevantPathPart(urlString) {
    if (!urlString || typeof urlString !== 'string') {
        console.warn('[ChatLoader] extractRelevantPathPart: Получена невалидная строка URL:', urlString);
        return '';
    }
    try {
        // 1. Работаем с путем, убираем query/hash
        const urlObject = new URL(urlString, 'http://dummybase.com'); // Используем URL для надежного парсинга
        const pathName = urlObject.pathname;

        // 2. Ищем известные папки ('images', 'uploads') с конца пути
        const pathSegments = pathName.split('/').filter(part => part !== '');
        let folderIndex = -1;
        for (let i = pathSegments.length - 1; i >= 0; i--) {
            if (pathSegments[i] === 'images' || pathSegments[i] === 'uploads') {
                folderIndex = i;
                break;
            }
        }

        if (folderIndex !== -1) {
            // 3a. Нашли папку, берем путь от нее
            const relevantPath = pathSegments.slice(folderIndex).join('/');
            console.log(`[ChatLoader] extractRelevantPathPart: Из '${urlString}' извлечен путь (с папкой): '${relevantPath}'`);
            return relevantPath;
        } else {
            // 3b. Папок не нашли, берем только имя файла (последний сегмент)
            const fileName = pathSegments.pop();
            if (fileName) {
              console.log(`[ChatLoader] extractRelevantPathPart: Из '${urlString}' извлечен путь (только файл): '${fileName}'`);
              return fileName;
            } else {
              // Если путь был типа "/" или пустой
              console.warn('[ChatLoader] extractRelevantPathPart: Не удалось извлечь имя файла из:', urlString);
              return '';
            }
        }
    } catch (error) {
        console.error(`[ChatLoader] extractRelevantPathPart: Ошибка при обработке URL '${urlString}':`, error);
        // Fallback: Просто берем все после последнего /
        const lastSlashIndex = urlString.split('?')[0].split('#')[0].lastIndexOf('/');
        if (lastSlashIndex !== -1) {
            return urlString.substring(lastSlashIndex + 1);
        }
        return ''; // Совсем не получилось
    }
  }

  // --- Загрузка И Инициализация основного скрипта (main.js) ---
  // ВАЖНО: `baseResourceUrl` здесь ДОЛЖЕН БЫТЬ равен `chatAdminBaseUrl` (с test-chat4)
  async function loadAndInitMainScript(mainImportUrl, baseResourceUrl) {

    console.log(`[ChatLoader] loadAndInitMainScript: ВХОД. Переданный baseResourceUrl: ${baseResourceUrl}`); // Проверка №1

    // Еще раз убедимся, что baseResourceUrl заканчивается на /
    if (!baseResourceUrl.endsWith('/')) {
        console.warn(`[ChatLoader] loadAndInitMainScript: Входящий baseResourceUrl не заканчивался на /. Добавляем.`);
        baseResourceUrl += '/';
    }

    try {
      const mainModule = await import(mainImportUrl);
      console.log(`[ChatLoader] Модуль ${mainImportUrl} импортирован.`);

      if (mainModule && typeof mainModule.initializeChatModules === "function") {
        console.log("[ChatLoader] Вызов mainModule.initializeChatModules().");
        await mainModule.initializeChatModules(baseResourceUrl); // Передаем правильный base URL

        // --- Загрузка и установка аватара ---
        const avatarApiUrl = `${baseResourceUrl}index.php?page=api&action=getBotAvatar`;
        console.log("[ChatLoader] Запрос URL аватарки с API:", avatarApiUrl); // Проверка №2 - URL API должен быть правильным

        try {
          const avatarResponse = await fetch(avatarApiUrl, { mode: 'cors' });
          if (!avatarResponse.ok) {
              throw new Error(`HTTP ошибка! Статус: ${avatarResponse.status} при запросе аватарки API (${avatarApiUrl}).`);
          }
          const avatarData = await avatarResponse.json();
          console.log("[ChatLoader] Получены данные аватарки от API:", avatarData);

          if (avatarData && avatarData.avatar) {
            const pathFromApi = avatarData.avatar;
            console.log("[ChatLoader] Получен путь/URL аватарки с сервера:", pathFromApi);

            // --- ИЗВЛЕКАЕМ НУЖНУЮ ЧАСТЬ ПУТИ ---
            const relativeAvatarPath = extractRelevantPathPart(pathFromApi);
            console.log(`[ChatLoader] Извлеченный относительный путь аватара: '${relativeAvatarPath}'`); // Проверка №3

            let absoluteAvatarUrl = '';
            if (relativeAvatarPath) {
                // --- СОЗДАЕМ КОРРЕКТНЫЙ АБСОЛЮТНЫЙ URL ---
                // ИСПОЛЬЗУЕМ ПЕРЕМЕННУЮ baseResourceUrl, ПЕРЕДАННУЮ В ЭТУ ФУНКЦИЮ

                // Убираем возможный начальный слеш из относительного пути
                const pathPart = relativeAvatarPath.startsWith('/') ? relativeAvatarPath.slice(1) : relativeAvatarPath;

                // КОМБИНИРУЕМ ИМЕННО baseResourceUrl (который с test-chat4) и pathPart
                absoluteAvatarUrl = `${baseResourceUrl}${pathPart}`;

                // --- КЛЮЧЕВАЯ ПРОВЕРКА №4 ---
                // Этот URL ДОЛЖЕН быть https://uniqpaid.com/test-chat4/chat-admin/images/darya.svg
                console.log(`[ChatLoader] ПРОВЕРКА: Финальный URL аватара перед установкой: ${absoluteAvatarUrl}`);
                // --- ---
            } else {
                console.warn("[ChatLoader] Не удалось извлечь относительный путь из данных API. Аватар не будет установлен.", pathFromApi);
                absoluteAvatarUrl = ''; // Явно делаем пустым
            }

            // --- Установка аватара ---
            if (absoluteAvatarUrl) { // Устанавливаем только если URL не пуст
                if (mainModule.setVoiceCallAvatar && typeof mainModule.setVoiceCallAvatar === "function") {
                    console.log(`[ChatLoader] ВЫЗОВ: mainModule.setVoiceCallAvatar с URL: ${absoluteAvatarUrl}`); // Проверка №5
                    setTimeout(() => {
                        try {
                            mainModule.setVoiceCallAvatar(absoluteAvatarUrl);
                            console.log("[ChatLoader] Успешно вызван mainModule.setVoiceCallAvatar.");
                        } catch(setAvatarError) {
                            console.error("[ChatLoader] Ошибка при вызове setVoiceCallAvatar:", setAvatarError);
                        }
                    }, 150);
                } else {
                    console.warn("[ChatLoader] Функция setVoiceCallAvatar не найдена. Используется Fallback DOM.");
                    setTimeout(() => {
                        try {
                            const modalAvatarImg = document.querySelector(".czn-chat-modal .czn-chat-avatar img");
                            const generalAvatarImg = document.querySelector(".czn-chat-avatar img");

                            if (modalAvatarImg) {
                                console.log(`[ChatLoader] FALLBACK: Установка src=${absoluteAvatarUrl} для .czn-chat-modal .czn-chat-avatar img`); // Проверка №6 (Fallback)
                                modalAvatarImg.src = absoluteAvatarUrl;
                            } else if (generalAvatarImg) {
                                console.log(`[ChatLoader] FALLBACK: Установка src=${absoluteAvatarUrl} для .czn-chat-avatar img`); // Проверка №6 (Fallback)
                                generalAvatarImg.src = absoluteAvatarUrl;
                            } else {
                                console.warn("[ChatLoader] FALLBACK: Не найден элемент img аватара.");
                            }
                        } catch (domError) {
                            console.error("[ChatLoader] Ошибка при fallback установке аватара через DOM:", domError);
                        }
                    }, 400);
                }
            } else {
                 console.log("[ChatLoader] Пропуск установки аватара (URL пуст).");
            }

          } else {
              console.warn("[ChatLoader] API для аватарки вернуло пустой или некорректный ответ в поле 'avatar'.", avatarData);
          }
        } catch (error) {
          console.error("[ChatLoader] Ошибка при загрузке или установке аватарки бота:", error);
        }

      } else {
        console.error(
          `[ChatLoader] КРИТИЧЕСКАЯ ОШИБКА: Функция 'initializeChatModules' не найдена в ${mainImportUrl}.`
        );
        throw new Error(`Функция 'initializeChatModules' не найдена в ${mainImportUrl}`);
      }
    } catch (error) {
      console.error(
        `[ChatLoader] КРИТИЧЕСКАЯ ОШИБКА при импорте/инициализации ${mainImportUrl}`,
        error
      );
       // Error display hints...
      throw error;
    }
  }

  // --- Основная последовательность инициализации плагина ---
  async function initializeChatPlugin() {
    console.log("[ChatLoader] Запуск последовательности инициализации плагина чата...");
    try {
      // Сначала убеждаемся, что Font Awesome загружен
      await ensureFontAwesome();

      // Используем ТОЧНО рассчитанный chatAdminBaseUrl для всего
      await loadCss(cssPath);
      await loadCss(dynamicCssPath);
      console.log("[ChatLoader] Основные CSS стили загружены.");

      const htmlContent = await loadHtml(htmlTemplatePath);
      console.log("[ChatLoader] HTML шаблон успешно загружен.");

      injectHtml(htmlContent);
      console.log("[ChatLoader] HTML успешно внедрен в страницу.");

      await new Promise((resolve) => setTimeout(resolve, 50));

      console.log(`[ChatLoader] Вызов loadAndInitMainScript с mainImportUrl=${mainScriptImportPath} и baseResourceUrl=${chatAdminBaseUrl}`);
      // Передаем ТОЧНО рассчитанный chatAdminBaseUrl
      await loadAndInitMainScript(mainScriptImportPath, chatAdminBaseUrl);
      console.log("[ChatLoader] УСПЕХ: Последовательность инициализации плагина чата завершена.");

    } catch (error) {
      console.error("[ChatLoader] КРИТИЧЕСКАЯ ОШИБКА: Не удалось инициализировать плагин чата.", error);
      // Error display logic...
            try {
          let baseElement = document.getElementById(baseElementId);
          if (!baseElement) {
              baseElement = document.body;
          }
          if (baseElement && !baseElement.querySelector('.znak-chat-loader-error')) {
              const errorDiv = document.createElement('div');
              errorDiv.className = 'znak-chat-loader-error';
              let userMessage = 'Не удалось загрузить компонент ассистента. ';
              if (error.message.includes('CORS') || error.message.includes('fetch') || error.message.includes('module specifier') || error.message.includes('NetworkError')) {
                  userMessage += 'Возможная причина: проблема с доступом к ресурсам (CORS) или сетевая ошибка. ';
              } else if (error.message.includes('404') || error.message.includes('Not Found')) {
                  userMessage += 'Возможная причина: необходимый файл не найден на сервере. ';
              }
              userMessage += 'Пожалуйста, обновите страницу или попробуйте позже. Если проблема повторится, обратитесь в поддержку.';
              errorDiv.textContent = userMessage;
              errorDiv.style.cssText = 'position:fixed; bottom:10px; left:10px; right: 10px; padding:15px; background:rgba(255, 221, 221, 0.95); color:#d8000c; border:1px solid #d8000c; border-radius: 5px; text-align:center; z-index:10001; font-family: sans-serif; font-size: 14px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);';
              baseElement.appendChild(errorDiv);
          }
      } catch (e) {
          console.error("[ChatLoader] Дополнительная ошибка при попытке отобразить сообщение об ошибке пользователю:", e);
      }
    }
  }

  // --- Запуск инициализации ---
  if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", initializeChatPlugin);
      console.log("[ChatLoader] Ожидание DOMContentLoaded для запуска инициализации...");
  } else {
      console.log("[ChatLoader] DOM уже готов, запуск инициализации немедленно...");
      initializeChatPlugin();
  }

})();
// --- END OF FILE chat-loader.js ---