<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Models\CrmSettings;
use App\Models\Database;
use App\Models\User;
use App\Services\SimpleAmoCrmService;

/**
 * Контроллер для управления настройками интеграции с CRM
 */
class CrmController extends BaseController
{
    /**
     * Отображает страницу настроек CRM
     */
    public function index(): void
    {
        $this->checkAuth();
        
        $crmSettingsModel = new CrmSettings();
        $settings = $crmSettingsModel->getSettings();
        
        $message = null;
        $messageType = 'info';
        
        if (isset($_GET['success'])) {
            $messageType = 'success';
            switch ($_GET['success']) {
                case '1':
                case 'settings_saved':
                    $message = 'Настройки CRM успешно сохранены.';
                    break;
                case 'test_success':
                    $message = 'Тестирование интеграции прошло успешно.';
                    break;
                case 'oauth_success':
                    $message = 'OAuth авторизация прошла успешно! Токены сохранены.';
                    break;
            }
        }
        
        if (isset($_GET['error'])) {
            $messageType = 'error';
            switch ($_GET['error']) {
                case 'settings_save':
                    $message = 'Ошибка при сохранении настроек CRM.';
                    break;
                case 'test_failed':
                    $message = 'Ошибка при тестировании интеграции.';
                    break;
                case 'empty_fields':
                    $message = 'Заполните все обязательные поля.';
                    break;
                case 'oauth_failed':
                    $message = 'Ошибка OAuth авторизации: ' . ($_GET['message'] ?? 'Неизвестная ошибка');
                    break;
            }
        }
        
        $this->loadView('crm_settings', [
            'user' => $this->getCurrentUser(),
            'settings' => $settings ?: [],
            'message' => $message,
            'messageType' => $messageType,
            'currentPage' => 'crm'
        ]);
    }

    /**
     * Сохраняет настройки CRM (устаревший метод для обратной совместимости)
     */
    public function updateSettings(): void
    {
        $this->checkAuth();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: index.php?page=crm');
            exit;
        }

        // Валидация обязательных полей
        $requiredFields = ['amocrm_portal_url', 'amocrm_login', 'amocrm_password'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                header('Location: index.php?page=crm&error=empty_fields');
                exit;
            }
        }

        Database::resetConnection();
        $crmSettingsModel = new CrmSettings();
        
        $data = [
            'amocrm_portal_url' => trim($_POST['amocrm_portal_url']),
            'amocrm_login' => trim($_POST['amocrm_login']),
            'amocrm_password' => trim($_POST['amocrm_password']),
            'amocrm_client_id_field_id' => (int)($_POST['amocrm_client_id_field_id'] ?? 631683),
            'yandex_metrika_counter_id' => (int)($_POST['yandex_metrika_counter_id'] ?? 100128474),
            'yandex_metrika_goal_name' => trim($_POST['yandex_metrika_goal_name'] ?? 'chat_contact_got'),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        if ($crmSettingsModel->saveSettings($data)) {
            header('Location: index.php?page=crm&success=settings_saved');
            exit;
        } else {
            header('Location: index.php?page=crm&error=settings_save');
            exit;
        }
    }
    
    /**
     * Сохраняет настройки AmoCRM (простая интеграция)
     */
    public function updateAmoCrmSettings(): void
    {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'Метод не поддерживается'
                ]);
                return;
            }
            header('Location: index.php?page=crm');
            exit;
        }

        Database::resetConnection();
        $crmSettingsModel = new CrmSettings();
        $currentSettings = $crmSettingsModel->getSettings() ?: [];

        // Валидация обязательных полей для простой интеграции
        $requiredFields = ['amocrm_subdomain', 'amocrm_access_token'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                if ($this->isAjaxRequest()) {
                    $this->jsonResponse([
                        'status' => 'error',
                        'message' => 'Заполните все обязательные поля: поддомен и Access Token.'
                    ]);
                    return;
                }
                header('Location: index.php?page=crm&error=empty_fields');
                exit;
            }
        }

        $data = [
            'amocrm_subdomain' => trim($_POST['amocrm_subdomain']),
            'amocrm_access_token' => trim($_POST['amocrm_access_token']),
            'amocrm_pipeline_id' => !empty($_POST['amocrm_pipeline_id']) ? (int)$_POST['amocrm_pipeline_id'] : null,
            'amocrm_responsible_user_id' => !empty($_POST['amocrm_responsible_user_id']) ? (int)$_POST['amocrm_responsible_user_id'] : null,
            // Сохраняем настройки Яндекс.Метрики
            'yandex_metrika_counter_id' => (int)($currentSettings['yandex_metrika_counter_id'] ?? 100128474),
            'yandex_metrika_goal_name' => $currentSettings['yandex_metrika_goal_name'] ?? 'chat_contact_got',
            'is_active' => isset($currentSettings['is_active']) ? (int)$currentSettings['is_active'] : 0
        ];

        if ($crmSettingsModel->saveSettings($data)) {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'status' => 'success',
                    'message' => 'Настройки AmoCRM успешно сохранены. Интеграция готова к работе!'
                ]);
                return;
            }
            header('Location: index.php?page=crm&success=settings_saved');
            exit;
        } else {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'Ошибка при сохранении настроек AmoCRM.'
                ]);
                return;
            }
            header('Location: index.php?page=crm&error=settings_save');
            exit;
        }
    }
    
    /**
     * Сохраняет настройки Яндекс.Метрики
     */
    public function updateYandexMetrikaSettings(): void
    {
        $this->checkAuth();



        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'Метод не поддерживается'
                ]);
                return;
            }
            header('Location: index.php?page=crm');
            exit;
        }

        Database::resetConnection();
        $crmSettingsModel = new CrmSettings();
        $currentSettings = $crmSettingsModel->getSettings() ?: [];

        // Обновляем только настройки Яндекс.Метрики, сохраняем остальные без изменений
        $data = [
            'amocrm_portal_url' => $currentSettings['amocrm_portal_url'] ?? 'https://advokatpushkarev.amocrm.ru',
            'amocrm_login' => $currentSettings['amocrm_login'] ?? '<EMAIL>',
            'amocrm_password' => $currentSettings['amocrm_password'] ?? 'PoV8mdix',
            'amocrm_client_id_field_id' => (int)($currentSettings['amocrm_client_id_field_id'] ?? 631683),
            'yandex_metrika_counter_id' => (int)($_POST['yandex_metrika_counter_id'] ?? 100128474),
            'yandex_metrika_goal_name' => trim($_POST['yandex_metrika_goal_name'] ?? 'chat_contact_got'),
            'is_active' => isset($currentSettings['is_active']) ? (int)$currentSettings['is_active'] : 0
        ];

        if ($crmSettingsModel->saveSettings($data)) {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'status' => 'success',
                    'message' => 'Настройки Яндекс.Метрики успешно сохранены.'
                ]);
                return;
            }
            header('Location: index.php?page=crm&success=settings_saved');
            exit;
        } else {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'Ошибка при сохранении настроек Яндекс.Метрики.'
                ]);
                return;
            }
            header('Location: index.php?page=crm&error=settings_save');
            exit;
        }
    }
    
    /**
     * Сохраняет статус интеграции
     */
    public function updateIntegrationStatus(): void
    {
        $this->checkAuth();



        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'Метод не поддерживается'
                ]);
                return;
            }
            header('Location: index.php?page=crm');
            exit;
        }

        Database::resetConnection();
        $crmSettingsModel = new CrmSettings();
        $currentSettings = $crmSettingsModel->getSettings() ?: [];

        // Обновляем только статус интеграции, сохраняем остальные настройки без изменений
        // Если чекбокс отмечен, $_POST['is_active'] будет равно "1"
        // Если не отмечен, $_POST['is_active'] будет равно "0" (из скрытого поля)
        $isActive = isset($_POST['is_active']) && $_POST['is_active'] === '1' ? 1 : 0;

        $data = array_merge($currentSettings, [
            'is_active' => $isActive
        ]);

        if ($crmSettingsModel->saveSettings($data)) {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'status' => 'success',
                    'message' => 'Статус интеграции успешно обновлен.'
                ]);
                return;
            }
            header('Location: index.php?page=crm&success=status_updated');
            exit;
        } else {
            if ($this->isAjaxRequest()) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'Ошибка при обновлении статуса интеграции.'
                ]);
                return;
            }
            header('Location: index.php?page=crm&error=settings_save');
            exit;
        }
    }

    /**
     * Тестирует подключение к AmoCRM (простая интеграция)
     */
    public function testConnection(): void
    {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse([
                'status' => 'error',
                'message' => 'Метод не поддерживается'
            ]);
            return;
        }

        // Получаем данные из JSON, если они есть
        $jsonData = json_decode(file_get_contents('php://input'), true);
        $crmSettingsModel = new CrmSettings();

        // Проверяем данные из формы или используем сохраненные настройки
        if ($jsonData) {
            // Тестирование с данными из формы
            if (empty($jsonData['amocrm_subdomain'])) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => '🌐 Укажите поддомен AmoCRM (например: mycompany)'
                ]);
                return;
            }

            if (empty($jsonData['amocrm_access_token'])) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => '🔑 Для тестирования необходим Access Token'
                ]);
                return;
            }

            $subdomain = $jsonData['amocrm_subdomain'];
            $accessToken = $jsonData['amocrm_access_token'];
        } else {
            // Используем сохраненные настройки
            $savedSettings = $crmSettingsModel->getSettings();
            if (!$savedSettings) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => '⚙️ Настройки AmoCRM не найдены. Сначала настройте интеграцию.'
                ]);
                return;
            }

            if (empty($savedSettings['amocrm_subdomain']) || empty($savedSettings['amocrm_access_token'])) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => '🔐 Настройки AmoCRM неполные. Укажите поддомен и Access Token.'
                ]);
                return;
            }

            $subdomain = $savedSettings['amocrm_subdomain'];
            $accessToken = $savedSettings['amocrm_access_token'];
        }

        // Тестируем подключение к AmoCRM через простую интеграцию
        try {
            $simpleAmoCrmService = new SimpleAmoCrmService($subdomain, $accessToken);
            $result = $simpleAmoCrmService->testConnection();

            if ($result['success']) {
                $this->jsonResponse([
                    'status' => 'success',
                    'message' => $result['message']
                ]);
            } else {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            $this->jsonResponse([
                'status' => 'error',
                'message' => '🚫 Ошибка подключения к AmoCRM: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Обрабатывает сообщение с телефоном и создает лид в AmoCRM
     */
    public function processPhoneMessage(string $message, array $chatHistory): array
    {
        try {
            // Извлекаем телефон из сообщения
            $phone = SimpleAmoCrmService::extractPhone($message);
            if (!$phone) {
                return [
                    'success' => false,
                    'message' => 'Телефон не найден в сообщении'
                ];
            }

            // Получаем настройки AmoCRM
            $crmSettingsModel = new CrmSettings();
            $settings = $crmSettingsModel->getSettings();

            if (!$settings || empty($settings['amocrm_subdomain']) || empty($settings['amocrm_access_token'])) {
                return [
                    'success' => false,
                    'message' => 'AmoCRM не настроен'
                ];
            }

            if (empty($settings['is_active'])) {
                return [
                    'success' => false,
                    'message' => 'Интеграция с AmoCRM отключена'
                ];
            }

            // Создаем сервис AmoCRM
            $simpleAmoCrmService = new SimpleAmoCrmService(
                $settings['amocrm_subdomain'],
                $settings['amocrm_access_token']
            );

            // Создаем лид с историей чата
            $result = $simpleAmoCrmService->createFullLead(
                $phone,
                $chatHistory,
                $settings['amocrm_pipeline_id'] ?? null,
                $settings['amocrm_responsible_user_id'] ?? null
            );

            return $result;

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Ошибка создания лида: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Получает список доступных воронок AmoCRM
     */
    public function getPipelines(): void
    {
        $this->checkAuth();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse([
                'status' => 'error',
                'message' => 'Метод не поддерживается'
            ]);
            return;
        }

        try {
            // Получаем настройки AmoCRM
            $crmSettingsModel = new CrmSettings();
            $settings = $crmSettingsModel->getSettings();

            if (!$settings || empty($settings['amocrm_subdomain']) || empty($settings['amocrm_access_token'])) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'AmoCRM не настроен. Укажите поддомен и Access Token.'
                ]);
                return;
            }

            // Создаем сервис AmoCRM
            $simpleAmoCrmService = new SimpleAmoCrmService(
                $settings['amocrm_subdomain'],
                $settings['amocrm_access_token']
            );

            // Получаем список воронок
            $result = $simpleAmoCrmService->getPipelines();

            if ($result['success']) {
                $pipelines = array_map(function($pipeline) {
                    return [
                        'id' => $pipeline['id'],
                        'name' => $pipeline['name'],
                        'is_main' => $pipeline['is_main'] ?? false
                    ];
                }, $result['pipelines']);

                $this->jsonResponse([
                    'status' => 'success',
                    'pipelines' => $pipelines,
                    'message' => 'Список воронок получен успешно'
                ]);
            } else {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => $result['message']
                ]);
            }

        } catch (\Exception $e) {
            $this->jsonResponse([
                'status' => 'error',
                'message' => 'Ошибка получения воронок: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Обрабатывает OAuth авторизацию AmoCRM
     */
    public function handleOAuthCallback(): void
    {
        $this->checkAuth();

        if (empty($_GET['code'])) {
            header('Location: index.php?page=crm&error=oauth_no_code');
            exit;
        }

        $authCode = $_GET['code'];

        try {
            $crmSettingsModel = new CrmSettings();
            $oauthData = $crmSettingsModel->getOAuthData();

            if (!$oauthData || empty($oauthData['client_id']) || empty($oauthData['client_secret'])) {
                header('Location: index.php?page=crm&error=oauth_not_configured');
                exit;
            }

            $amoCrmService = new AmoCrmService(
                $oauthData['portal_url'],
                $oauthData['client_id'],
                $oauthData['client_secret']
            );

            $redirectUri = $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] .
                          dirname($_SERVER['SCRIPT_NAME']) . '/index.php?page=crm&action=handleOAuthCallback';

            $tokens = $amoCrmService->getTokenByAuthCode($authCode, $redirectUri);

            if ($tokens) {
                // Сохраняем токены в базу данных
                $expiresAt = time() + $tokens['expires_in'];
                $success = $crmSettingsModel->updateTokens(
                    $tokens['access_token'],
                    $tokens['refresh_token'],
                    $expiresAt
                );

                if ($success) {
                    header('Location: index.php?page=crm&success=oauth_success');
                } else {
                    header('Location: index.php?page=crm&error=oauth_save_failed');
                }
            } else {
                header('Location: index.php?page=crm&error=oauth_token_failed');
            }
        } catch (\Exception $e) {
            error_log("OAuth callback error: " . $e->getMessage());
            header('Location: index.php?page=crm&error=oauth_error');
        }

        exit;
    }

    /**
     * Генерирует URL для OAuth авторизации
     */
    public function getOAuthUrl(): void
    {
        $this->checkAuth();

        try {
            $crmSettingsModel = new CrmSettings();
            $oauthData = $crmSettingsModel->getOAuthData();

            if (!$oauthData || empty($oauthData['client_id']) || empty($oauthData['portal_url'])) {
                $this->jsonResponse([
                    'status' => 'error',
                    'message' => 'OAuth не настроен. Сначала сохраните Client ID и URL портала.'
                ]);
                return;
            }

            $redirectUri = $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] .
                          dirname($_SERVER['SCRIPT_NAME']) . '/oauth_callback.php';

            $authUrl = $oauthData['portal_url'] . '/oauth?' . http_build_query([
                'client_id' => $oauthData['client_id'],
                'response_type' => 'code',
                'redirect_uri' => $redirectUri,
                'state' => 'oauth_' . time()
            ]);

            $this->jsonResponse([
                'status' => 'success',
                'auth_url' => $authUrl
            ]);
        } catch (\Exception $e) {
            $this->jsonResponse([
                'status' => 'error',
                'message' => 'Ошибка генерации OAuth URL: ' . $e->getMessage()
            ]);
        }
    }




}
